from django.db import models
from project.models import Project


class Api_Tree_Folder(models.Model):
    name = models.CharField(max_length=100, db_comment='文件夹名')
    type = models.CharField(max_length=1, db_comment='树类型;1目录;0接口')
    if_stream = models.BooleanField(default=False, db_comment='是否流式')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = 'quick_api_tree_folder'
        db_table_comment = '快捷测试目录树'

    def __str__(self):
        return f"目录名: {self.name}"


class Case_Tree_Folder(models.Model):
    name = models.CharField(max_length=100, db_comment='文件夹名')
    type = models.CharField(max_length=1, db_comment='树类型;2用例;1目录;0接口')
    if_stream = models.BooleanField(default=False, db_comment='是否流式')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = 'case_api_tree_folder'
        db_table_comment = '用例目录树'

    def __str__(self):
        return f"目录名: {self.name}"


class MultipartFile(models.Model):
    file_name = models.CharField(max_length=100, db_comment='文件名')
    file_key = models.CharField(max_length=100, db_comment='文件key')
    file_path = models.CharField(max_length=300, null=True, db_comment='文件路径')
    file_size = models.IntegerField(null=True, db_comment='文件大小')
    project_id = models.IntegerField(null=True, db_comment='项目id')

    class Meta:
        db_table = 'multipart_file'
        db_table_comment = 'multipart文件表'


# 基础模型
class BaseApi(models.Model):
    module_name = models.CharField(max_length=100, db_comment='模块名')
    case_level = models.CharField(max_length=50, null=True, blank=True, db_comment='用例等级')
    case_name = models.CharField(max_length=100, db_comment='用例名')
    case_code = models.CharField(max_length=100, db_comment='测试编码')
    api_name = models.CharField(max_length=200, db_comment='接口名')
    request_url = models.CharField(max_length=800, db_comment='请求URL')
    request_path = models.CharField(max_length=100, null=True, blank=True, db_comment='请求路径')
    request_method = models.CharField(max_length=50, db_comment='请求方式')
    params = models.JSONField(null=True)
    authorization = models.JSONField(null=True)
    headers = models.JSONField(null=True)
    body = models.JSONField(null=True)
    pre_script = models.TextField(null=True, blank=True)
    post_script = models.TextField(null=True, blank=True)
    tests = models.JSONField(null=True)
    postfix = models.JSONField(null=True)
    create_time = models.DateTimeField(auto_now_add=True, null=True, db_comment='创建时间')
    creator = models.CharField(max_length=20, null=True, db_comment='创建人')
    update_time = models.DateTimeField(auto_now=True, null=True, db_comment='更新时间')
    environment = models.IntegerField(null=True, db_comment='环境id')
    content_type = models.CharField(max_length=100, db_comment='连接类型')

    class Meta:
        abstract = True


# 快捷测试模型
class QuickTest(BaseApi):
    # 快捷测试特有的字段
    folder = models.ForeignKey(Api_Tree_Folder, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = 'quick_api'
        db_table_comment = '快捷测试接口表'

    def __str__(self):
        return f"接口名：{self.api_name}"


# 测试用例模型(主接口)
class TestCase(BaseApi):
    # 可以继续添加其他字段
    folder = models.ForeignKey(Case_Tree_Folder, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = 'case_api'
        db_table_comment = '用例主接口表'

    def __str__(self):
        return f"测试用例主接口：{self.case_name}"


# 测试用例模型(子接口)
class TestCaseChild(BaseApi):
    folder = models.ForeignKey(Case_Tree_Folder, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = 'case_api_child'
        db_table_comment = '用例子接口表'

    def __str__(self):
        return f"测试用例名：{self.case_name}"


class BaseExecution(models.Model):
    execution_count = models.PositiveIntegerField(default=1, db_comment='执行次数')
    if_execution = models.PositiveIntegerField(default=1, db_comment='是否执行; 1执行;0不执行')
    timeout = models.PositiveIntegerField(default=5, db_comment='超时时间（秒）')
    analysis = models.JSONField(null=True)
    execution_status = models.CharField(max_length=10, db_comment='执行状态;1:成功；2:失败')
    execution_time = models.DateTimeField(auto_now_add=True, null=True, db_comment='执行时间')

    class Meta:
        abstract = True


# 快捷测试执行模型
class QuickTestExecution(BaseExecution):
    quick_test = models.OneToOneField(QuickTest, on_delete=models.CASCADE)

    # 其他执行相关的字段
    class Meta:
        db_table = 'quick_api_execution'
        db_table_comment = '快捷测试执行表'


# 测试用例执行模型
class TestCaseExecution(BaseExecution):
    test_case = models.OneToOneField(TestCase, on_delete=models.CASCADE)

    # 其他执行相关的字段
    class Meta:
        db_table = 'case_api_execution'
        db_table_comment = '用例执行表'


class TestCaseChildExecution(BaseExecution):
    test_case = models.OneToOneField(TestCaseChild, on_delete=models.CASCADE)

    # 其他执行相关的字段
    class Meta:
        db_table = 'case_api_child_execution'
        db_table_comment = '用例子接口执行表'


class QuickTestApiParams(models.Model):
    key = models.CharField(max_length=100, db_comment='params变量名')
    type = models.CharField(max_length=1, db_comment='params类型;1:变量;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='params描述')
    value = models.CharField(max_length=1000, db_comment='params变量值')
    api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, db_comment='api')

    class Meta:
        db_table = 'quick_api_params'
        db_table_comment = '快捷测试参数表'


class QuickTestApiAuth(models.Model):
    username = models.CharField(max_length=100, null=True, blank=True, db_comment='basic_username')
    type = models.CharField(max_length=100, db_comment='auth类型;bearer_token;basic_auth;parent_auth')
    password = models.CharField(max_length=500, null=True, blank=True, db_comment='basic_password')
    token = models.CharField(max_length=500, null=True, blank=True, db_comment='bearer_token')
    api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, db_comment='api')

    class Meta:
        db_table = 'quick_api_auth'
        db_table_comment = '快捷测试认证表'


class QuickTestApiHeaders(models.Model):
    key = models.CharField(max_length=100, db_comment='headers变量名')
    type = models.CharField(max_length=1, db_comment='headers类型;1:变量;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='headers描述')
    value = models.CharField(max_length=1000, db_comment='headers变量值')
    api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, db_comment='api')

    class Meta:
        db_table = 'quick_api_headers'
        db_table_comment = '快捷测试头部表'


class QuickTestApiBody(models.Model):
    key = models.CharField(max_length=100, null=True, blank=True, db_comment='body变量名')
    key_type = models.CharField(max_length=1, null=True, blank=True, db_comment='body_key类型;0:none;1:变量;2:file;')
    body_type = models.CharField(max_length=1, db_comment='body类型;1:json;2:from-data;3:urlencoded;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='body描述')
    value = models.CharField(max_length=1000, null=True, blank=True, db_comment='body变量值')
    json_value = models.JSONField(null=True)
    file = models.JSONField(null=True, db_comment='form-data文件')
    api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, db_comment='api')

    class Meta:
        db_table = 'quick_api_body'
        db_table_comment = '快捷测试body表'


class QuickTestApiExtract(models.Model):
    key = models.CharField(max_length=100, db_comment='extract变量名')
    type = models.CharField(max_length=1, db_comment='extract类型;1:临时变量;1:环境变量;1:项目变量;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='extract描述')
    value = models.CharField(max_length=1000, db_comment='extract变量值-一般存JSONPath表达式')
    api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, db_comment='api')

    class Meta:
        db_table = 'quick_api_extract'
        db_table_comment = '快捷测试提取表'


class QuickTestApiAssertion(models.Model):
    name = models.CharField(max_length=100, db_comment='断言名称')
    obj = models.CharField(max_length=1, db_comment='断言对象;1:响应码;2:响应头;3:响应体;4:JSONPath值;')
    operator = models.CharField(max_length=100, db_comment='断言方法')
    header_key = models.CharField(max_length=100, null=True, db_comment='响应头键名')
    data = models.CharField(max_length=1000, null=True, db_comment='断言值')
    jsonpath = models.CharField(max_length=100, null=True, db_comment='JSONPath表达式')
    api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, db_comment='api')

    class Meta:
        db_table = 'quick_api_assertion'
        db_table_comment = '快捷测试断言表'


class QuickTestsResult(models.Model):
    title = models.CharField(max_length=100, db_comment='')
    status = models.CharField(max_length=20, db_comment='')
    error_log = models.CharField(max_length=500, null=True, db_comment='')
    api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, db_comment='api')

    class Meta:
        db_table = 'quick_api_result'
        db_table_comment = '快捷测试执行结果表'

# class QuickPreAndPostScript(models.Model):
#     pre_script = models.TextField(null=True, blank=True, db_comment='前置脚本')
#     post_script = models.TextField(null=True, blank=True, db_comment='后置脚本')
#     api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, null=True, db_comment='快捷测试的主接口')
#     class Meta:
#         db_table = 'quick_pre_and_post_script'
#         db_table_comment = '快捷测试前置后置脚本表'



# class QuickTempData(models.Model):
#     key = models.CharField(max_length=100, db_comment='')
#     value = models.CharField(max_length=1000, db_comment='')
#     project = models.ForeignKey(Project, on_delete=models.CASCADE, db_comment='')

#     class Meta:
#         db_table = 'quick_api_temp_data'
#         db_table_comment = '快捷测试临时数据表'


#############################用例模型#####################################
class TestCaseApiParams(models.Model):
    key = models.CharField(max_length=100, db_comment='params变量名')
    type = models.CharField(max_length=1, db_comment='params类型;1:变量;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='params描述')
    value = models.CharField(max_length=1000, db_comment='params变量值')
    api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
    api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, db_comment='测试用例')

    class Meta:
        db_table = 'case_api_params'
        db_table_comment = '用例参数表'


class TestCaseApiAuth(models.Model):
    username = models.CharField(max_length=100, null=True, blank=True, db_comment='basic_username')
    type = models.CharField(max_length=100, db_comment='auth类型;bearer_token;basic_auth;parent_auth')
    password = models.CharField(max_length=500, null=True, blank=True, db_comment='basic_password')
    token = models.CharField(max_length=500, null=True, blank=True, db_comment='bearer_token')
    api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
    api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True, db_comment='测试用例')

    class Meta:
        db_table = 'case_api_auth'
        db_table_comment = '用例认证表'


class TestCaseApiHeaders(models.Model):
    key = models.CharField(max_length=100, db_comment='headers变量名')
    type = models.CharField(max_length=1, db_comment='headers类型;1:变量;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='headers描述')
    value = models.CharField(max_length=1000, db_comment='headers变量值')
    api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
    api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True, db_comment='测试用例')

    class Meta:
        db_table = 'case_api_headers'
        db_table_comment = '用例头部表'


class TestCaseApiBody(models.Model):
    key = models.CharField(max_length=100, null=True, blank=True, db_comment='body变量名')
    key_type = models.CharField(max_length=1, null=True, blank=True, db_comment='body_key类型;0:none;1:变量;2:file;')
    body_type = models.CharField(max_length=1, db_comment='body类型;1:json;2:from-data;3:urlencoded;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='body描述')
    value = models.CharField(max_length=1000, null=True, blank=True, db_comment='body变量值')
    json_value = models.JSONField(null=True)
    file = models.JSONField(null=True, db_comment='form-data文件')
    api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
    api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True, db_comment='测试用例')

    class Meta:
        db_table = 'case_api_body'
        db_table_comment = '用例body表'


class TestCaseApiExtract(models.Model):
    key = models.CharField(max_length=100, db_comment='extract变量名')
    type = models.CharField(max_length=1, db_comment='extract类型;1:临时变量;1:环境变量;1:项目变量;')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='extract描述')
    value = models.CharField(max_length=1000, db_comment='extract变量值-一般存JSONPath表达式')
    api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
    api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True, db_comment='测试用例')

    class Meta:
        db_table = 'case_api_extract'
        db_table_comment = '用例提取表'


class TestCaseApiAssertion(models.Model):
    name = models.CharField(max_length=100, db_comment='断言名称')
    obj = models.CharField(max_length=1, db_comment='断言对象;1:响应码;2:响应头;3:响应体;4:JSONPath值;')
    operator = models.CharField(max_length=100, db_comment='断言方法')
    header_key = models.CharField(max_length=100, null=True, db_comment='响应头键名')
    data = models.CharField(max_length=1000, null=True, db_comment='断言值')
    jsonpath = models.CharField(max_length=100, null=True, db_comment='JSONPath表达式')
    api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
    api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True, db_comment='测试用例')

    class Meta:
        db_table = 'case_api_assertion'
        db_table_comment = '用例断言表'


class CaseTestsResult(models.Model):
    title = models.CharField(max_length=100, db_comment='')
    status = models.CharField(max_length=20, db_comment='')
    error_log = models.CharField(max_length=200, null=True, db_comment='')
    api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
    api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True, db_comment='测试用例')

    class Meta:
        db_table = 'case_api_result'
        db_table_comment = '用例执行结果表'

# class CasePreAndPostScript(models.Model):
#     pre_script = models.TextField(null=True, blank=True, db_comment='前置脚本')
#     post_script = models.TextField(null=True, blank=True, db_comment='后置脚本')
#     api = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例的主接口')
#     api_case = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True, db_comment='测试用例')
    
#     class Meta:
#         db_table = 'case_pre_and_post_script'
#         db_table_comment = '用例前置后置脚本表'


# class CaseTempData(models.Model):
#     key = models.CharField(max_length=100, db_comment='')
#     value = models.CharField(max_length=1000, db_comment='')
#     project = models.ForeignKey(Project, on_delete=models.CASCADE, db_comment='')

#     class Meta:
#         db_table = 'case_api_temp_data'
#         db_table_comment = '用例临时数据表'

class CaseFolderOverview(models.Model):
    rich_text = models.TextField(null=True, db_comment='富文本描述')
    folder = models.ForeignKey(Case_Tree_Folder, on_delete=models.CASCADE, db_comment='目录')

    class Meta:
        db_table = 'case_folder_overview'
        db_table_comment = '用例目录概览表'


class QuickAndCaseTempData(models.Model):
    key = models.CharField(max_length=100, db_comment='')
    value = models.CharField(max_length=1000, db_comment='')
    variable_type = models.CharField(max_length=1, null=True,
                                     db_comment='变量类型;1:string;2:int;3:数组;4:boolean;5:object;6:number;7:sql')
    project = models.ForeignKey(Project, on_delete=models.CASCADE, db_comment='项目')
    quick_api = models.ForeignKey(QuickTest, on_delete=models.CASCADE, null=True, db_comment='快捷测试')
    api_case = models.ForeignKey(TestCase, on_delete=models.CASCADE, null=True, db_comment='测试用例')
    api_case_child = models.ForeignKey(TestCaseChild, on_delete=models.CASCADE, null=True, blank=True,
                                       db_comment='测试用例')

    class Meta:
        db_table = 'quick_and_case_temp_data'
        db_table_comment = '快捷测试和用例临时数据表'


#########################编排模型################################
class Scene_Tree_Folder(models.Model):
    name = models.CharField(max_length=100, db_comment='文件夹名')
    type = models.CharField(max_length=1, db_comment='树类型;2场景用例;1目录')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True)
    scene_code = models.CharField(max_length=100, null=True, db_comment='场景编码')

    class Meta:
        db_table = 'scene_tree_folder'
        db_table_comment = '编排目录树'

    def __str__(self):
        return f"目录名: {self.name}"


class SceneApiInfo(models.Model):
    scene_name = models.CharField(max_length=100, db_comment='编排名称')
    scene_code = models.CharField(unique=True, max_length=100, db_comment='场景编码')
    scene_type = models.CharField(max_length=1, db_comment='编排类型;1:接口;2:用例;')
    environment_id = models.IntegerField(db_comment='环境id')
    project_id = models.IntegerField(db_comment='项目id')
    var_file = models.JSONField(null=True, db_comment='变量文件')
    iterations = models.IntegerField(null=True, db_comment='循环次数')
    delay_time = models.IntegerField(null=True, db_comment='延迟时间')
    folder_id_of_api = models.JSONField(db_comment='接口或用例对应的目录id')
    folder = models.ForeignKey(Scene_Tree_Folder, null=True, on_delete=models.CASCADE, db_comment='场景目录id')
    oper_time = models.DateTimeField(auto_now_add=True, db_comment='操作时间')

    class Meta:
        db_table = 'scene_api_info'
        db_table_comment = '场景API信息表'


class SceneApiExecutionResult(models.Model):
    parent_scene_code = models.CharField(max_length=100, db_comment='场景编码')
    execution_time = models.DateTimeField(auto_now_add=True, db_comment='执行时间')
    assertion_count = models.IntegerField(db_comment='断言次数')
    total_elapsed_time = models.FloatField(null=True, db_comment='编排流程总耗时')
    api_elapsed_time = models.FloatField(null=True, db_comment='接口耗时总耗时')
    avg_api_elapsed_time = models.FloatField(null=True, db_comment='接口平均耗时')
    execution_result = models.JSONField(null=True, db_comment='接口执行结果')
    echart_data = models.JSONField(null=True, db_comment='echart数据')
    report_data = models.JSONField(null=True, db_comment='报告数据')
    iterations = models.IntegerField(null=True, db_comment='循环次数')

    class Meta:
        db_table = 'scene_api_execution_result'
        db_table_comment = '场景API执行结果表'


class SceneCsvFile(models.Model):
    file_name = models.CharField(max_length=100, db_comment='文件名')
    file_bucket = models.CharField(max_length=100, db_comment='文件桶')
    file_path = models.CharField(max_length=300, null=True, db_comment='文件路径')
    file_size = models.IntegerField(null=True, db_comment='文件大小')
    file_type = models.CharField(max_length=1, db_comment='文件类型;1:csv;2:xlsx;')
    file_content = models.JSONField(null=True, db_comment='文件内容')
    parent_scene_code = models.CharField(max_length=100, db_comment='场景编码')

    class Meta:
        db_table = 'scene_csv_file'
        db_table_comment = '场景CSV文件表'


class SmartApiUploadFile(models.Model):
    file_name = models.CharField(max_length=100, db_comment='文件名')
    file_path = models.CharField(max_length=300, null=True, db_comment='文件路径')
    file_size = models.IntegerField(null=True, db_comment='文件大小')
    file_type = models.CharField(max_length=1, db_comment='文件类型;1:json;2:yaml;3:word')
    user_id = models.IntegerField(db_comment='用户id')
    project_id = models.IntegerField(db_comment='项目id')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')

    class Meta:
        db_table = 'smart_api_upload_file'
        db_table_comment = '智能API上传文件表'


class SmartApiTaskResult(models.Model):
    # 任务状态常量
    STATUS_PENDING = 'PENDING'
    STATUS_PROCESSING = 'PROCESSING'
    STATUS_SUCCESS = 'SUCCESS'
    STATUS_FAILURE = 'FAILURE'
    STATUS_PARTIAL_SUCCESS = 'PARTIAL_SUCCESS'
    
    STATUS_CHOICES = [
        (STATUS_PENDING, '等待处理'),
        (STATUS_PROCESSING, '处理中'),
        (STATUS_SUCCESS, '成功'),
        (STATUS_FAILURE, '失败'),
        (STATUS_PARTIAL_SUCCESS, '部分成功'),
    ]

    task_id = models.CharField(max_length=100, db_comment='Celery任务ID')
    file = models.ForeignKey(SmartApiUploadFile, on_delete=models.CASCADE, db_comment='关联的上传文件')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_PENDING, db_comment='任务状态')
    openapi_data = models.JSONField(null=True, db_comment='解析后的OpenAPI数据')
    llm_result = models.JSONField(null=True, db_comment='LLM生成的测试用例，包含parse_result和test_cases两个任务的结果')
    parse_task_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_PENDING, db_comment='API解析任务状态')
    test_cases_task_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_PENDING, db_comment='测试用例生成任务状态')
    error_message = models.TextField(null=True, blank=True, db_comment='错误信息')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')

    class Meta:
        db_table = 'smart_api_task_result'
        db_table_comment = '智能API任务结果表'

    def update_task_status(self):
        """
        根据子任务状态更新总任务状态
        """
        if self.parse_task_status == self.STATUS_FAILURE or self.test_cases_task_status == self.STATUS_FAILURE:
            self.status = self.STATUS_FAILURE
        elif self.parse_task_status == self.STATUS_SUCCESS and self.test_cases_task_status == self.STATUS_SUCCESS:
            self.status = self.STATUS_SUCCESS
        elif self.parse_task_status == self.STATUS_PENDING and self.test_cases_task_status == self.STATUS_PENDING:
            self.status = self.STATUS_PENDING
        else:
            self.status = self.STATUS_PARTIAL_SUCCESS
        self.save()

class UserModelUsageLimit(models.Model):
    """用户模型使用限制表"""
    user_id = models.IntegerField(db_comment='用户ID')
    usage_date = models.DateField(db_comment='使用日期')
    usage_count = models.IntegerField(default=0, db_comment='使用次数')
    model_id = models.IntegerField(default=1, db_comment='模型ID，默认为1')
    
    class Meta:
        db_table = 'user_model_usage_limit'
        db_table_comment = '用户模型使用限制表'
        unique_together = ('user_id', 'usage_date', 'model_id')
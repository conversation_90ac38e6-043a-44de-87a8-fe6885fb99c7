from django.urls import path, re_path
from .api_test_views.main_views.api_tree_folder_view import ApiTreeFolderView, ApiTreeFolderDetailView
from .api_test_views.main_views.api_request_view import ApiRequestView, ApiRequestDetailView
from .api_test_views.main_views.run_quick_api_view import RunQuickApiView
from .api_test_views.main_views.api_sub_data_view import ApiSubDataDetailView
from .api_test_views.main_views.api_response import ApiResponseView
from .api_test_views.main_views.api_tests_result import ApiTestsResultView
from .case_test_views.main_views.case_tree_folder_view import CaseTreeFolderView, CaseTreeFolderDetailView
from .case_test_views.main_views.case_save_as_tree import CaseTreeSaveAsView
from .case_test_views.main_views.case_request_view import CaseRequestView, CaseRequestDetailView
from .case_test_views.main_views.child_case_request_view import Child<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iew, ChildCaseRequestDetailView
from .case_test_views.main_views.case_sub_data_view import CaseSubDataDetailView
from .case_test_views.main_views.run_case_api_view import RunCaseApiView
from .case_test_views.main_views.run_child_case_api_view import RunChildCaseApiView
from .case_test_views.main_views.run_child_case_api_batch_view import RunChildCaseApiBatchView
from .case_test_views.main_views.case_response import CaseResponseView
from .case_test_views.main_views.case_tests_result import CaseTestsResultView
from .case_test_views.main_views.case_child_create_view import CaseChildCreateView
from .case_test_views.main_views.case_child_create_from_ai_view import CaseChildCreateFromAiView
from .case_test_views.main_views.case_child_copy_view import CaseCopyView
from .case_test_views.main_views.case_move_view import CaseMoveView
from .case_test_views.main_views.case_folder_views.case_folder_overview_view import CaseFolderOverviewView
from .case_test_views.main_views.scene_tree_folder_view import SceneTreeFolderView, SceneTreeFolderDetailView
from .case_test_views.main_views.scene_api_list_view import SceneApiListView
from .case_test_views.main_views.run_scene_api_view import RunSceneApiView
from .case_test_views.main_views.scene_api_save_view import SceneApiSaveView
from .case_test_views.main_views.scene_result_view import SceneResultView
from .case_test_views.main_views.scene_info_view import SceneInfoView, SceneInfoListView, SceneInfoListFromTreeView
from .case_test_views.main_views.scene_api_save_as_tree import SceneTreeSaveAsView
from .case_test_views.main_views.test_case_builder_view import TestCaseBuilderView
from .scene_test_views.main_views.upload_scene_csv_file_to_minio_view import ApiTestDataFileUploadView
from .scene_test_views.main_views.scene_list_view import SceneInfoAndApiResultView
from .scene_test_views.main_views.scene_csv_data_view import SceneCsvDataView
# from .api_test_views.main_views.upload_multipart_file_to_minio_view import MultipartFileUploadView1
from .api_test_views.main_views.upload_multipart_file_to_local_view import MultipartFileUploadView
from .smart_api_test_views.main_views.upload_api_files_view import SmartApiFileUploadView
from .smart_api_test_views.main_views.parser_view import ParserView
from .smart_api_test_views.main_views.smart_api_upload_file_view import SmartApiUploadFileView
urlpatterns = [
    path('api_tree_folder/', ApiTreeFolderView.as_view()),
    re_path(r'^api_tree_folder/(?:(?P<pk>\d+)/)?$', ApiTreeFolderDetailView.as_view()),
    path('api_request_data/', ApiRequestView.as_view()),
    path('api_request_data/<int:pk>/', ApiRequestDetailView.as_view()),
    path('run_quick_api/', RunQuickApiView.as_view()),
    path('api_data_detail/', ApiSubDataDetailView.as_view()),
    re_path(r'api_response/', ApiResponseView.as_view()),
    re_path(r'api_tests_result/', ApiTestsResultView.as_view()),

    path('case_tree_folder/', CaseTreeFolderView.as_view()),
    path('case_tree_save_as_folder/', CaseTreeSaveAsView.as_view()),
    re_path(r'^case_tree_folder/(?:(?P<pk>\d+)/)?$', CaseTreeFolderDetailView.as_view()),
    path('case_request_data/', CaseRequestView.as_view()),
    path('case_request_data/<int:pk>/', CaseRequestDetailView.as_view()),
    path('run_case_api/', RunCaseApiView.as_view()),
    path('case_data_detail/', CaseSubDataDetailView.as_view()),
    re_path(r'case_response/', CaseResponseView.as_view()),
    re_path(r'case_tests_result/', CaseTestsResultView.as_view()),

    path('create_case/', CaseChildCreateView.as_view()),
    path('create_case_from_ai/', CaseChildCreateFromAiView.as_view()),
    path('copy_case/', CaseCopyView.as_view()),
    path('move_case/', CaseMoveView.as_view()),
    path('run_child_case_api/', RunChildCaseApiView.as_view()),
    path('run_child_case_api_batch/', RunChildCaseApiBatchView.as_view()),
    path('child_case_request_data/', ChildCaseRequestView.as_view()),
    path('child_case_request_data/<int:pk>/', ChildCaseRequestDetailView.as_view()),

    path('case_folder_overview/', CaseFolderOverviewView.as_view()),
    path('scene_tree_folder/', SceneTreeFolderView.as_view()),
    re_path(r'^scene_tree_folder/(?:(?P<pk>\d+)/)?$', SceneTreeFolderDetailView.as_view()),

    path('scene_api_list/', SceneApiListView.as_view()),
    path('run_scene_api/', RunSceneApiView.as_view()),
    path('api_test_data_file_upload/', ApiTestDataFileUploadView.as_view()),
    path('scene_api_save/', SceneApiSaveView.as_view()),
    path('scene_result/', SceneResultView.as_view()),
    path('scene_info/', SceneInfoView.as_view()),
    path('scene_info_list/', SceneInfoListView.as_view()),
    path('scene_api_save_as_folder/', SceneTreeSaveAsView.as_view()),
    path('scene_info_and_api_result/', SceneInfoAndApiResultView.as_view()),
    path('scene_info_list_from_tree/', SceneInfoListFromTreeView.as_view()),

    path('scene_csv_data/', SceneCsvDataView.as_view()),
    path('multipart_file_upload/', MultipartFileUploadView.as_view()),
    # path('multipart_file_upload/', MultipartFileUploadView1.as_view()),
    path('smart_api_file_upload/', SmartApiFileUploadView.as_view()),
    path('parser/', ParserView.as_view()),
    path('smart_api_upload_file_list/', SmartApiUploadFileView.as_view()),
    
    # 测试用例生成器
    path('test_case_builder/', TestCaseBuilderView.as_view()),
]


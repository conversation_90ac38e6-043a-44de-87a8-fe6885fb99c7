# Generated by Django 4.2.5 on 2025-03-20 18:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0005_smartapiuploadfile'),
    ]

    operations = [
        migrations.CreateModel(
            name='SmartApiTaskResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.CharField(db_comment='Celery任务ID', max_length=100)),
                ('status', models.Char<PERSON>ield(db_comment='任务状态: PENDING, STARTED, SUCCESS, FAILURE', max_length=20)),
                ('openapi_data', models.JSONField(db_comment='解析后的OpenAPI数据', null=True)),
                ('llm_result', models.JSONField(db_comment='LLM生成的测试用例', null=True)),
                ('error_message', models.TextField(blank=True, db_comment='错误信息', null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
                ('file_id', models.ForeignKey(db_comment='关联的上传文件', on_delete=django.db.models.deletion.CASCADE, to='api_test.smartapiuploadfile')),
            ],
            options={
                'db_table': 'smart_api_task_result',
                'db_table_comment': '智能API任务结果表',
            },
        ),
    ]

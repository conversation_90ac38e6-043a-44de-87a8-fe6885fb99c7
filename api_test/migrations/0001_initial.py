# Generated by Django 4.2.5 on 2025-03-11 23:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Api_Tree_Folder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='文件夹名', max_length=100)),
                ('type', models.Char<PERSON>ield(db_comment='树类型;1目录;0接口', max_length=1)),
                ('if_stream', models.BooleanField(db_comment='是否流式', default=False)),
            ],
            options={
                'db_table': 'quick_api_tree_folder',
                'db_table_comment': '快捷测试目录树',
            },
        ),
        migrations.CreateModel(
            name='Case_Tree_Folder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='文件夹名', max_length=100)),
                ('type', models.CharField(db_comment='树类型;2用例;1目录;0接口', max_length=1)),
                ('if_stream', models.BooleanField(db_comment='是否流式', default=False)),
            ],
            options={
                'db_table': 'case_api_tree_folder',
                'db_table_comment': '用例目录树',
            },
        ),
        migrations.CreateModel(
            name='CaseFolderOverview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rich_text', models.TextField(db_comment='富文本描述', null=True)),
            ],
            options={
                'db_table': 'case_folder_overview',
                'db_table_comment': '用例目录概览表',
            },
        ),
        migrations.CreateModel(
            name='CaseTestsResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(db_comment='', max_length=100)),
                ('status', models.CharField(db_comment='', max_length=20)),
                ('error_log', models.CharField(db_comment='', max_length=200, null=True)),
            ],
            options={
                'db_table': 'case_api_result',
                'db_table_comment': '用例执行结果表',
            },
        ),
        migrations.CreateModel(
            name='MultipartFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(db_comment='文件名', max_length=100)),
                ('file_key', models.CharField(db_comment='文件key', max_length=100)),
                ('file_path', models.CharField(db_comment='文件路径', max_length=300, null=True)),
                ('file_size', models.IntegerField(db_comment='文件大小', null=True)),
                ('project_id', models.IntegerField(db_comment='项目id', null=True)),
            ],
            options={
                'db_table': 'multipart_file',
                'db_table_comment': 'multipart文件表',
            },
        ),
        migrations.CreateModel(
            name='QuickAndCaseTempData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='', max_length=100)),
                ('value', models.CharField(db_comment='', max_length=1000)),
                ('variable_type', models.CharField(db_comment='变量类型;1:string;2:int;3:数组;4:boolean;5:object;6:number', max_length=1, null=True)),
            ],
            options={
                'db_table': 'quick_and_case_temp_data',
                'db_table_comment': '快捷测试和用例临时数据表',
            },
        ),
        migrations.CreateModel(
            name='QuickTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_name', models.CharField(db_comment='模块名', max_length=100)),
                ('case_level', models.CharField(blank=True, db_comment='用例等级', max_length=50, null=True)),
                ('case_name', models.CharField(db_comment='用例名', max_length=100)),
                ('case_code', models.CharField(db_comment='测试编码', max_length=100)),
                ('api_name', models.CharField(db_comment='接口名', max_length=200)),
                ('request_url', models.CharField(db_comment='请求URL', max_length=100)),
                ('request_path', models.CharField(blank=True, db_comment='请求路径', max_length=100, null=True)),
                ('request_method', models.CharField(db_comment='请求方式', max_length=50)),
                ('params', models.JSONField(null=True)),
                ('authorization', models.JSONField(null=True)),
                ('headers', models.JSONField(null=True)),
                ('body', models.JSONField(null=True)),
                ('pre_script', models.TextField(blank=True, null=True)),
                ('post_script', models.TextField(blank=True, null=True)),
                ('tests', models.JSONField(null=True)),
                ('postfix', models.JSONField(null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间', null=True)),
                ('creator', models.CharField(db_comment='创建人', max_length=20, null=True)),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间', null=True)),
                ('environment', models.IntegerField(db_comment='环境id', null=True)),
                ('content_type', models.CharField(db_comment='连接类型', max_length=100)),
            ],
            options={
                'db_table': 'quick_api',
                'db_table_comment': '快捷测试接口表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestApiAssertion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='断言名称', max_length=100)),
                ('obj', models.CharField(db_comment='断言对象;1:响应码;2:响应头;3:响应体;4:JSONPath值;', max_length=1)),
                ('operator', models.CharField(db_comment='断言方法', max_length=100)),
                ('header_key', models.CharField(db_comment='响应头键名', max_length=100, null=True)),
                ('data', models.CharField(db_comment='断言值', max_length=100, null=True)),
                ('jsonpath', models.CharField(db_comment='JSONPath表达式', max_length=100, null=True)),
            ],
            options={
                'db_table': 'quick_api_assertion',
                'db_table_comment': '快捷测试断言表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestApiAuth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(blank=True, db_comment='basic_username', max_length=100, null=True)),
                ('type', models.CharField(db_comment='auth类型;bearer_token;basic_auth;parent_auth', max_length=100)),
                ('password', models.CharField(blank=True, db_comment='basic_password', max_length=500, null=True)),
                ('token', models.CharField(blank=True, db_comment='bearer_token', max_length=500, null=True)),
            ],
            options={
                'db_table': 'quick_api_auth',
                'db_table_comment': '快捷测试认证表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestApiBody',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(blank=True, db_comment='body变量名', max_length=100, null=True)),
                ('key_type', models.CharField(blank=True, db_comment='body_key类型;0:none;1:变量;2:file;', max_length=1, null=True)),
                ('body_type', models.CharField(db_comment='body类型;1:json;2:from-data;3:urlencoded;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='body描述', max_length=100, null=True)),
                ('value', models.CharField(blank=True, db_comment='body变量值', max_length=200, null=True)),
                ('json_value', models.JSONField(null=True)),
                ('file', models.JSONField(db_comment='form-data文件', null=True)),
            ],
            options={
                'db_table': 'quick_api_body',
                'db_table_comment': '快捷测试body表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestApiExtract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='extract变量名', max_length=100)),
                ('type', models.CharField(db_comment='extract类型;1:临时变量;1:环境变量;1:项目变量;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='extract描述', max_length=100, null=True)),
                ('value', models.CharField(db_comment='extract变量值-一般存JSONPath表达式', max_length=100)),
            ],
            options={
                'db_table': 'quick_api_extract',
                'db_table_comment': '快捷测试提取表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestApiHeaders',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='headers变量名', max_length=100)),
                ('type', models.CharField(db_comment='headers类型;1:变量;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='headers描述', max_length=100, null=True)),
                ('value', models.CharField(db_comment='headers变量值', max_length=100)),
            ],
            options={
                'db_table': 'quick_api_headers',
                'db_table_comment': '快捷测试头部表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestApiParams',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='params变量名', max_length=100)),
                ('type', models.CharField(db_comment='params类型;1:变量;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='params描述', max_length=100, null=True)),
                ('value', models.CharField(db_comment='params变量值', max_length=100)),
            ],
            options={
                'db_table': 'quick_api_params',
                'db_table_comment': '快捷测试参数表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('execution_count', models.PositiveIntegerField(db_comment='执行次数', default=1)),
                ('if_execution', models.PositiveIntegerField(db_comment='是否执行; 1执行;0不执行', default=1)),
                ('timeout', models.PositiveIntegerField(db_comment='超时时间（秒）', default=5)),
                ('analysis', models.JSONField(null=True)),
                ('execution_status', models.CharField(db_comment='执行状态;1:成功；2:失败', max_length=10)),
                ('execution_time', models.DateTimeField(auto_now_add=True, db_comment='执行时间', null=True)),
            ],
            options={
                'db_table': 'quick_api_execution',
                'db_table_comment': '快捷测试执行表',
            },
        ),
        migrations.CreateModel(
            name='QuickTestsResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(db_comment='', max_length=100)),
                ('status', models.CharField(db_comment='', max_length=20)),
                ('error_log', models.CharField(db_comment='', max_length=200, null=True)),
            ],
            options={
                'db_table': 'quick_api_result',
                'db_table_comment': '快捷测试执行结果表',
            },
        ),
        migrations.CreateModel(
            name='Scene_Tree_Folder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='文件夹名', max_length=100)),
                ('type', models.CharField(db_comment='树类型;2场景用例;1目录', max_length=1)),
                ('scene_code', models.CharField(db_comment='场景编码', max_length=100, null=True)),
                ('parent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.scene_tree_folder')),
            ],
            options={
                'db_table': 'scene_tree_folder',
                'db_table_comment': '编排目录树',
            },
        ),
        migrations.CreateModel(
            name='SceneApiExecutionResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parent_scene_code', models.CharField(db_comment='场景编码', max_length=100)),
                ('execution_time', models.DateTimeField(auto_now_add=True, db_comment='执行时间')),
                ('assertion_count', models.IntegerField(db_comment='断言次数')),
                ('total_elapsed_time', models.FloatField(db_comment='编排流程总耗时', null=True)),
                ('api_elapsed_time', models.FloatField(db_comment='接口耗时总耗时', null=True)),
                ('avg_api_elapsed_time', models.FloatField(db_comment='接口平均耗时', null=True)),
                ('execution_result', models.JSONField(db_comment='接口执行结果', null=True)),
                ('echart_data', models.JSONField(db_comment='echart数据', null=True)),
                ('report_data', models.JSONField(db_comment='报告数据', null=True)),
                ('iterations', models.IntegerField(db_comment='循环次数', null=True)),
            ],
            options={
                'db_table': 'scene_api_execution_result',
                'db_table_comment': '场景API执行结果表',
            },
        ),
        migrations.CreateModel(
            name='SceneCsvFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(db_comment='文件名', max_length=100)),
                ('file_bucket', models.CharField(db_comment='文件桶', max_length=100)),
                ('file_path', models.CharField(db_comment='文件路径', max_length=300, null=True)),
                ('file_size', models.IntegerField(db_comment='文件大小', null=True)),
                ('file_type', models.CharField(db_comment='文件类型;1:csv;2:xlsx;', max_length=1)),
                ('file_content', models.JSONField(db_comment='文件内容', null=True)),
                ('parent_scene_code', models.CharField(db_comment='场景编码', max_length=100)),
            ],
            options={
                'db_table': 'scene_csv_file',
                'db_table_comment': '场景CSV文件表',
            },
        ),
        migrations.CreateModel(
            name='TestCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_name', models.CharField(db_comment='模块名', max_length=100)),
                ('case_level', models.CharField(blank=True, db_comment='用例等级', max_length=50, null=True)),
                ('case_name', models.CharField(db_comment='用例名', max_length=100)),
                ('case_code', models.CharField(db_comment='测试编码', max_length=100)),
                ('api_name', models.CharField(db_comment='接口名', max_length=200)),
                ('request_url', models.CharField(db_comment='请求URL', max_length=100)),
                ('request_path', models.CharField(blank=True, db_comment='请求路径', max_length=100, null=True)),
                ('request_method', models.CharField(db_comment='请求方式', max_length=50)),
                ('params', models.JSONField(null=True)),
                ('authorization', models.JSONField(null=True)),
                ('headers', models.JSONField(null=True)),
                ('body', models.JSONField(null=True)),
                ('pre_script', models.TextField(blank=True, null=True)),
                ('post_script', models.TextField(blank=True, null=True)),
                ('tests', models.JSONField(null=True)),
                ('postfix', models.JSONField(null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间', null=True)),
                ('creator', models.CharField(db_comment='创建人', max_length=20, null=True)),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间', null=True)),
                ('environment', models.IntegerField(db_comment='环境id', null=True)),
                ('content_type', models.CharField(db_comment='连接类型', max_length=100)),
                ('folder', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.case_tree_folder')),
            ],
            options={
                'db_table': 'case_api',
                'db_table_comment': '用例主接口表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseChild',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_name', models.CharField(db_comment='模块名', max_length=100)),
                ('case_level', models.CharField(blank=True, db_comment='用例等级', max_length=50, null=True)),
                ('case_name', models.CharField(db_comment='用例名', max_length=100)),
                ('case_code', models.CharField(db_comment='测试编码', max_length=100)),
                ('api_name', models.CharField(db_comment='接口名', max_length=200)),
                ('request_url', models.CharField(db_comment='请求URL', max_length=100)),
                ('request_path', models.CharField(blank=True, db_comment='请求路径', max_length=100, null=True)),
                ('request_method', models.CharField(db_comment='请求方式', max_length=50)),
                ('params', models.JSONField(null=True)),
                ('authorization', models.JSONField(null=True)),
                ('headers', models.JSONField(null=True)),
                ('body', models.JSONField(null=True)),
                ('pre_script', models.TextField(blank=True, null=True)),
                ('post_script', models.TextField(blank=True, null=True)),
                ('tests', models.JSONField(null=True)),
                ('postfix', models.JSONField(null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间', null=True)),
                ('creator', models.CharField(db_comment='创建人', max_length=20, null=True)),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间', null=True)),
                ('environment', models.IntegerField(db_comment='环境id', null=True)),
                ('content_type', models.CharField(db_comment='连接类型', max_length=100)),
                ('folder', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.case_tree_folder')),
            ],
            options={
                'db_table': 'case_api_child',
                'db_table_comment': '用例子接口表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('execution_count', models.PositiveIntegerField(db_comment='执行次数', default=1)),
                ('if_execution', models.PositiveIntegerField(db_comment='是否执行; 1执行;0不执行', default=1)),
                ('timeout', models.PositiveIntegerField(db_comment='超时时间（秒）', default=5)),
                ('analysis', models.JSONField(null=True)),
                ('execution_status', models.CharField(db_comment='执行状态;1:成功；2:失败', max_length=10)),
                ('execution_time', models.DateTimeField(auto_now_add=True, db_comment='执行时间', null=True)),
                ('test_case', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
            ],
            options={
                'db_table': 'case_api_execution',
                'db_table_comment': '用例执行表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseChildExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('execution_count', models.PositiveIntegerField(db_comment='执行次数', default=1)),
                ('if_execution', models.PositiveIntegerField(db_comment='是否执行; 1执行;0不执行', default=1)),
                ('timeout', models.PositiveIntegerField(db_comment='超时时间（秒）', default=5)),
                ('analysis', models.JSONField(null=True)),
                ('execution_status', models.CharField(db_comment='执行状态;1:成功；2:失败', max_length=10)),
                ('execution_time', models.DateTimeField(auto_now_add=True, db_comment='执行时间', null=True)),
                ('test_case', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_api_child_execution',
                'db_table_comment': '用例子接口执行表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseApiParams',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='params变量名', max_length=100)),
                ('type', models.CharField(db_comment='params类型;1:变量;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='params描述', max_length=100, null=True)),
                ('value', models.CharField(db_comment='params变量值', max_length=100)),
                ('api', models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
                ('api_case', models.ForeignKey(db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_api_params',
                'db_table_comment': '用例参数表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseApiHeaders',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='headers变量名', max_length=100)),
                ('type', models.CharField(db_comment='headers类型;1:变量;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='headers描述', max_length=100, null=True)),
                ('value', models.CharField(db_comment='headers变量值', max_length=100)),
                ('api', models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
                ('api_case', models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_api_headers',
                'db_table_comment': '用例头部表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseApiExtract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='extract变量名', max_length=100)),
                ('type', models.CharField(db_comment='extract类型;1:临时变量;1:环境变量;1:项目变量;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='extract描述', max_length=100, null=True)),
                ('value', models.CharField(db_comment='extract变量值-一般存JSONPath表达式', max_length=100)),
                ('api', models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
                ('api_case', models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_api_extract',
                'db_table_comment': '用例提取表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseApiBody',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(blank=True, db_comment='body变量名', max_length=100, null=True)),
                ('key_type', models.CharField(blank=True, db_comment='body_key类型;0:none;1:变量;2:file;', max_length=1, null=True)),
                ('body_type', models.CharField(db_comment='body类型;1:json;2:from-data;3:urlencoded;', max_length=1)),
                ('desc', models.CharField(blank=True, db_comment='body描述', max_length=100, null=True)),
                ('value', models.CharField(blank=True, db_comment='body变量值', max_length=200, null=True)),
                ('json_value', models.JSONField(null=True)),
                ('file', models.JSONField(db_comment='form-data文件', null=True)),
                ('api', models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
                ('api_case', models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_api_body',
                'db_table_comment': '用例body表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseApiAuth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(blank=True, db_comment='basic_username', max_length=100, null=True)),
                ('type', models.CharField(db_comment='auth类型;bearer_token;basic_auth;parent_auth', max_length=100)),
                ('password', models.CharField(blank=True, db_comment='basic_password', max_length=500, null=True)),
                ('token', models.CharField(blank=True, db_comment='bearer_token', max_length=500, null=True)),
                ('api', models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
                ('api_case', models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_api_auth',
                'db_table_comment': '用例认证表',
            },
        ),
        migrations.CreateModel(
            name='TestCaseApiAssertion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='断言名称', max_length=100)),
                ('obj', models.CharField(db_comment='断言对象;1:响应码;2:响应头;3:响应体;4:JSONPath值;', max_length=1)),
                ('operator', models.CharField(db_comment='断言方法', max_length=100)),
                ('header_key', models.CharField(db_comment='响应头键名', max_length=100, null=True)),
                ('data', models.CharField(db_comment='断言值', max_length=100, null=True)),
                ('jsonpath', models.CharField(db_comment='JSONPath表达式', max_length=100, null=True)),
                ('api', models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
                ('api_case', models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_api_assertion',
                'db_table_comment': '用例断言表',
            },
        ),
        migrations.CreateModel(
            name='SceneApiInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scene_name', models.CharField(db_comment='编排名称', max_length=100)),
                ('scene_code', models.CharField(db_comment='场景编码', max_length=100, unique=True)),
                ('scene_type', models.CharField(db_comment='编排类型;1:接口;2:用例;', max_length=1)),
                ('environment_id', models.IntegerField(db_comment='环境id')),
                ('project_id', models.IntegerField(db_comment='项目id')),
                ('var_file', models.JSONField(db_comment='变量文件', null=True)),
                ('iterations', models.IntegerField(db_comment='循环次数', null=True)),
                ('delay_time', models.IntegerField(db_comment='延迟时间', null=True)),
                ('folder_id_of_api', models.JSONField(db_comment='接口或用例对应的目录id')),
                ('oper_time', models.DateTimeField(auto_now_add=True, db_comment='操作时间')),
                ('folder', models.ForeignKey(db_comment='场景目录id', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.scene_tree_folder')),
            ],
            options={
                'db_table': 'scene_api_info',
                'db_table_comment': '场景API信息表',
            },
        ),
    ]

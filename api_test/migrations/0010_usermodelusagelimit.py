# Generated by Django 4.2.5 on 2025-05-08 14:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0009_alter_quicktestapiassertion_data_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserModelUsageLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField(db_comment='用户ID')),
                ('usage_date', models.DateField(db_comment='使用日期')),
                ('usage_count', models.IntegerField(db_comment='使用次数', default=0)),
                ('model_id', models.IntegerField(db_comment='模型ID，默认为1', default=1)),
            ],
            options={
                'db_table': 'user_model_usage_limit',
                'db_table_comment': '用户模型使用限制表',
                'unique_together': {('user_id', 'usage_date', 'model_id')},
            },
        ),
    ]

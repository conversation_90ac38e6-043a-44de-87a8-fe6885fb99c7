# Generated by Django 4.2.5 on 2025-05-30 17:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0010_usermodelusagelimit'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='quicktest',
            name='request_url',
            field=models.<PERSON><PERSON><PERSON><PERSON>(db_comment='请求URL', max_length=300),
        ),
        migrations.AlterField(
            model_name='testcase',
            name='request_url',
            field=models.CharField(db_comment='请求URL', max_length=300),
        ),
        migrations.AlterField(
            model_name='testcasechild',
            name='request_url',
            field=models.<PERSON>r<PERSON>ield(db_comment='请求URL', max_length=300),
        ),
    ]

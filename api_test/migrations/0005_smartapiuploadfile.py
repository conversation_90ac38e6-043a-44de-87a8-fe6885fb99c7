# Generated by Django 4.2.5 on 2025-03-19 22:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0004_delete_scenecsvfile1'),
    ]

    operations = [
        migrations.CreateModel(
            name='SmartApiUploadFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.Char<PERSON><PERSON>(db_comment='文件名', max_length=100)),
                ('file_path', models.CharField(db_comment='文件路径', max_length=300, null=True)),
                ('file_size', models.IntegerField(db_comment='文件大小', null=True)),
                ('file_type', models.CharField(db_comment='文件类型;1:json;2:yaml;3:word', max_length=1)),
                ('user_id', models.IntegerField(db_comment='用户id')),
                ('project_id', models.Integer<PERSON>ield(db_comment='项目id')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
            ],
            options={
                'db_table': 'smart_api_upload_file',
                'db_table_comment': '智能API上传文件表',
            },
        ),
    ]

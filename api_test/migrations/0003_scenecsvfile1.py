# Generated by Django 4.2.5 on 2025-03-12 10:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SceneCsvFile1',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(db_comment='文件名', max_length=100)),
                ('file_bucket', models.<PERSON>r<PERSON><PERSON>(db_comment='文件桶', max_length=100)),
                ('file_path', models.<PERSON>r<PERSON><PERSON>(db_comment='文件路径', max_length=300, null=True)),
                ('file_size', models.IntegerField(db_comment='文件大小', null=True)),
                ('file_type', models.Char<PERSON>ield(db_comment='文件类型;1:csv;2:xlsx;', max_length=1)),
                ('file_content', models.<PERSON><PERSON><PERSON><PERSON>(db_comment='文件内容', null=True)),
                ('parent_scene_code', models.<PERSON>r<PERSON><PERSON>(db_comment='场景编码', max_length=100)),
            ],
            options={
                'db_table': 'scene_csv_file1',
                'db_table_comment': '场景CSV文件表',
            },
        ),
    ]

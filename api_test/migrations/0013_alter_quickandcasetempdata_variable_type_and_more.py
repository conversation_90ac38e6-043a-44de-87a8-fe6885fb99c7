# Generated by Django 4.2.5 on 2025-07-11 10:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0012_alter_quicktest_request_url_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='quickandcasetempdata',
            name='variable_type',
            field=models.CharField(db_comment='变量类型;1:string;2:int;3:数组;4:boolean;5:object;6:number;7:sql', max_length=1, null=True),
        ),
        migrations.CreateModel(
            name='QuickPreAndPostScript',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pre_script', models.TextField(blank=True, db_comment='前置脚本', null=True)),
                ('post_script', models.TextField(blank=True, db_comment='后置脚本', null=True)),
                ('api', models.ForeignKey(db_comment='快捷测试的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest')),
            ],
            options={
                'db_table': 'quick_pre_and_post_script',
                'db_table_comment': '快捷测试前置后置脚本表',
            },
        ),
        migrations.CreateModel(
            name='CasePreAndPostScript',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pre_script', models.TextField(blank=True, db_comment='前置脚本', null=True)),
                ('post_script', models.TextField(blank=True, db_comment='后置脚本', null=True)),
                ('api', models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase')),
                ('api_case', models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild')),
            ],
            options={
                'db_table': 'case_pre_and_post_script',
                'db_table_comment': '用例前置后置脚本表',
            },
        ),
    ]

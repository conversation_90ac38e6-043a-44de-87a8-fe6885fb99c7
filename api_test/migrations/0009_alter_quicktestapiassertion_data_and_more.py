# Generated by Django 4.2.5 on 2025-04-11 00:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0008_smartapitaskresult_parse_task_status_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='quicktestapiassertion',
            name='data',
            field=models.CharField(db_comment='断言值', max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='quicktestapibody',
            name='value',
            field=models.CharField(blank=True, db_comment='body变量值', max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='quicktestapiextract',
            name='value',
            field=models.Char<PERSON>ield(db_comment='extract变量值-一般存JSONPath表达式', max_length=1000),
        ),
        migrations.AlterField(
            model_name='quicktestapiheaders',
            name='value',
            field=models.Char<PERSON><PERSON>(db_comment='headers变量值', max_length=1000),
        ),
        migrations.AlterField(
            model_name='quicktestapiparams',
            name='value',
            field=models.Char<PERSON>ield(db_comment='params变量值', max_length=1000),
        ),
        migrations.AlterField(
            model_name='quicktestsresult',
            name='error_log',
            field=models.CharField(db_comment='', max_length=500, null=True),
        ),
        migrations.AlterField(
            model_name='testcaseapiassertion',
            name='data',
            field=models.CharField(db_comment='断言值', max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='testcaseapibody',
            name='value',
            field=models.CharField(blank=True, db_comment='body变量值', max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='testcaseapiextract',
            name='value',
            field=models.CharField(db_comment='extract变量值-一般存JSONPath表达式', max_length=1000),
        ),
        migrations.AlterField(
            model_name='testcaseapiheaders',
            name='value',
            field=models.CharField(db_comment='headers变量值', max_length=1000),
        ),
        migrations.AlterField(
            model_name='testcaseapiparams',
            name='value',
            field=models.CharField(db_comment='params变量值', max_length=1000),
        ),
    ]

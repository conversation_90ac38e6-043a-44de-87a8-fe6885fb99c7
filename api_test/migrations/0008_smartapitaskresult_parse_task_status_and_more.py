# Generated by Django 4.2.5 on 2025-04-10 10:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api_test', '0007_rename_file_id_smartapitaskresult_file'),
    ]

    operations = [
        migrations.AddField(
            model_name='smartapitaskresult',
            name='parse_task_status',
            field=models.CharField(choices=[('PENDING', '等待处理'), ('PROCESSING', '处理中'), ('SUCCESS', '成功'), ('FAILURE', '失败'), ('PARTIAL_SUCCESS', '部分成功')], db_comment='API解析任务状态', default='PENDING', max_length=20),
        ),
        migrations.AddField(
            model_name='smartapitaskresult',
            name='test_cases_task_status',
            field=models.CharField(choices=[('PENDING', '等待处理'), ('PROCESSING', '处理中'), ('SUCCESS', '成功'), ('FAILURE', '失败'), ('PARTIAL_SUCCESS', '部分成功')], db_comment='测试用例生成任务状态', default='PENDING', max_length=20),
        ),
        migrations.AlterField(
            model_name='smartapitaskresult',
            name='llm_result',
            field=models.JSONField(db_comment='LLM生成的测试用例，包含parse_result和test_cases两个任务的结果', null=True),
        ),
        migrations.AlterField(
            model_name='smartapitaskresult',
            name='status',
            field=models.CharField(choices=[('PENDING', '等待处理'), ('PROCESSING', '处理中'), ('SUCCESS', '成功'), ('FAILURE', '失败'), ('PARTIAL_SUCCESS', '部分成功')], db_comment='任务状态', default='PENDING', max_length=20),
        ),
    ]

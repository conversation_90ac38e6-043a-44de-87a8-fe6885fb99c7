# Generated by Django 4.2.5 on 2025-03-11 23:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('project', '0001_initial'),
        ('api_test', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='scene_tree_folder',
            name='project',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='quicktestsresult',
            name='api',
            field=models.ForeignKey(db_comment='api', on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktestexecution',
            name='quick_test',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktestapiparams',
            name='api',
            field=models.ForeignKey(db_comment='api', on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktestapiheaders',
            name='api',
            field=models.ForeignKey(db_comment='api', on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktestapiextract',
            name='api',
            field=models.ForeignKey(db_comment='api', on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktestapibody',
            name='api',
            field=models.ForeignKey(db_comment='api', on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktestapiauth',
            name='api',
            field=models.ForeignKey(db_comment='api', on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktestapiassertion',
            name='api',
            field=models.ForeignKey(db_comment='api', on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='quicktest',
            name='folder',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.api_tree_folder'),
        ),
        migrations.AddField(
            model_name='quickandcasetempdata',
            name='api_case',
            field=models.ForeignKey(db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase'),
        ),
        migrations.AddField(
            model_name='quickandcasetempdata',
            name='api_case_child',
            field=models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild'),
        ),
        migrations.AddField(
            model_name='quickandcasetempdata',
            name='project',
            field=models.ForeignKey(db_comment='项目', on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='quickandcasetempdata',
            name='quick_api',
            field=models.ForeignKey(db_comment='快捷测试', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.quicktest'),
        ),
        migrations.AddField(
            model_name='casetestsresult',
            name='api',
            field=models.ForeignKey(db_comment='测试用例的主接口', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcase'),
        ),
        migrations.AddField(
            model_name='casetestsresult',
            name='api_case',
            field=models.ForeignKey(blank=True, db_comment='测试用例', null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.testcasechild'),
        ),
        migrations.AddField(
            model_name='casefolderoverview',
            name='folder',
            field=models.ForeignKey(db_comment='目录', on_delete=django.db.models.deletion.CASCADE, to='api_test.case_tree_folder'),
        ),
        migrations.AddField(
            model_name='case_tree_folder',
            name='parent',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.case_tree_folder'),
        ),
        migrations.AddField(
            model_name='case_tree_folder',
            name='project',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
        migrations.AddField(
            model_name='api_tree_folder',
            name='parent',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='api_test.api_tree_folder'),
        ),
        migrations.AddField(
            model_name='api_tree_folder',
            name='project',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='project.project'),
        ),
    ]

import os, logging
from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from django.conf import settings
from ..serializers.smart_api_upload_file_serializer import SmartApiUploadFileSerializer

path = settings.MEDIA_ROOT + 'smart_api_files/'
logger = logging.getLogger('ats-console')


class SmartApiFileUploadView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SmartApiUploadFileSerializer

    def post(self, request):
        """获取任务状态和结果"""
        user_id = request.data.get('user_id')
        files = request.FILES.getlist('file')
        project_id = request.data.get('project_id')
        file_type = request.data.get('file_type')

        logger.info(
            f"上传参数: user_id={user_id}, project_id={project_id}, file_type={file_type}, files_count={len(files)}")

        upload_path = path + project_id + '/' + user_id
        print(upload_path)
        if not os.path.exists(upload_path):
            logger.info(f"创建上传目录: {upload_path}")
            os.makedirs(upload_path)

        upload_results = []
        for file_obj in files:
            logger.info(f"开始处理文件: {file_obj.name}")
            file_path = os.path.join(path + project_id + '/' + user_id, file_obj.name)

            data = {
                "file_name": file_obj.name,
                "file_size": file_obj.size,
                "file_path": file_path,
                "file_type": file_type,
                "user_id": user_id,
                "project_id": project_id,
            }

            # 直接创建新记录
            serializer = self.serializer_class(data=data)

            if serializer.is_valid():
                saved_file = serializer.save()
                logger.info(f"文件记录创建成功: {file_obj.name}")
            else:
                logger.error(f"文件记录创建失败: {file_obj.name}, 错误: {serializer.errors}")
                return Response(public_error_response(f"保存失败: {serializer.errors}"))

            try:
                # 写入新文件（如果同名文件存在会覆盖）
                with open(file_path, 'wb') as f:
                    for chunk in file_obj.chunks():
                        f.write(chunk)
                logger.info(f"文件上传成功: {file_obj.name}")
                upload_results.append({
                    "file_id": saved_file.id,
                    "file_name": file_obj.name,
                    "file_path": file_path,
                    "status": "成功",
                    "action": "创建"
                })
            except Exception as exc:
                logger.error(f"文件上传失败: {file_obj.name}, 错误: {exc}", exc_info=True)
                upload_results.append({
                    "file_name": file_obj.name,
                    "error": str(exc),
                    "status": "失败",
                    "action": "创建"
                })

        if all(result["status"] == "成功" for result in upload_results):
            logger.info("所有文件处理成功")
            return Response(public_success_response(upload_results))
        else:
            logger.error(f"部分文件处理失败: {upload_results}")
            return Response(public_error_response(upload_results))
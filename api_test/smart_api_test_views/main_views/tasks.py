from ats_celery.celery import app
from openai import OpenAI
import json
import re
from ...models import SmartApiTaskResult
import logging
from .save_case_api_view import save_case_api_and_data, save_child_case_and_data
from ats.public.public_func.generate_random_string import generate_random_string
from ..utils.siliconflow_api import call_siliconflow_api
# 配置日志
logger = logging.getLogger('ats-console')

# client = OpenAI(
#     api_key="sk-efe5bd161e93478fb71994374fb68f52",
#     base_url="https://api.deepseek.com/v1",
#     timeout=600  # 添加超时设置
# )

@app.task(bind=True,
         max_retries=1,
         default_retry_delay=30)
def get_llm_response(self, prompt: str, request_data: dict, task_id: str, result_key: str, openai_config=None):
    """
    异步调用LLM接口获取响应
    :param prompt: 提示文本
    :param request_data: 请求数据
    :param task_id: 任务ID
    :param result_key: 结果存储的键名
    :return: JSON格式的响应结果
    """

    # 获取任务记录
    try:
        task_result = SmartApiTaskResult.objects.get(task_id=task_id)
        logger.info(f"开始处理任务 {task_id} 的 {result_key} 部分")
        
        # 更新对应任务的状态为处理中
        if result_key == "parse_result":
            task_result.parse_task_status = SmartApiTaskResult.STATUS_PROCESSING
        else:
            task_result.test_cases_task_status = SmartApiTaskResult.STATUS_PROCESSING
        task_result.save()
        
        # 确保每次都从数据库获取最新的结果
        task_result.refresh_from_db()
        
    except SmartApiTaskResult.DoesNotExist:
        logger.warning(f"任务记录 {task_id} 不存在，可能已被删除")
        return None

    try:
        # 根据openai_config中的is_siliconflow字段或配置ID来判断是否使用siliconflow API
        if openai_config.get('is_siliconflow', False) or openai_config.get('config_id') == 1:
            logger.info(f"使用SiliconFlow API处理任务 {task_id} 的 {result_key} 部分")
            
            # 使用siliconflow API
            try:
                api_key = openai_config.get('api_key', 'sk-aaaaaa')
                model = openai_config.get('model', 'deepseek-ai/DeepSeek-V3')
                max_tokens = openai_config.get('max_tokens', 8192)
                temperature = openai_config.get('temperature', 0.7)
                top_p = openai_config.get('top_p', 0.9)
                
                # 调用siliconflow API
                response = call_siliconflow_api(
                    prompt=prompt, 
                    model=model, 
                    max_tokens=max_tokens, 
                    temperature=temperature, 
                    top_p=top_p, 
                    api_key=api_key
                )
                
                # 提取内容
                if 'choices' in response and len(response['choices']) > 0:
                    llm_content = response['choices'][0]['message']['content']
                    logger.info(f"获取到SiliconFlow LLM响应: {llm_content}")
                else:
                    raise Exception(f"未能从SiliconFlow API响应中提取有效内容: {response}")
                
            except Exception as e:
                logger.error(f"调用SiliconFlow API失败: {str(e)}")
                raise e
                
        else:
            # 使用原有的OpenAI客户端
            logger.info(f"使用OpenAI API处理任务 {task_id} 的 {result_key} 部分")
            
            client = OpenAI(
                api_key=openai_config['api_key'],
                base_url=openai_config['base_url'],
                timeout=openai_config['timeout']
            )
            
            # logger.info(f"Processing LLM request with prompt: {prompt}")
            
            response = client.chat.completions.create(
                messages=[{"role": "system", "content": "你是一名专业的软件测试工程师，擅长使用 OpenAPI 接口文档数据生成有效的测试数据和断言"},
                          {"role": "user", "content": prompt}],
                model=openai_config['model'],
                max_tokens=openai_config['max_tokens'],
                stream=False
            )

            llm_content = response.choices[0].message.content
            logger.info(f"获取到LLM响应: {llm_content}")
        
        def preprocess_js_expressions(content):
            try:
                # 移除单行注释，但确保不影响URL或其他正常内容
                lines = content.split('\n')
                processed_lines = []
                for line in lines:
                    # 只处理包含'//'的行，且确保'//'不是URL的一部分
                    if '//' in line and not ('http://' in line or 'https://' in line):
                        line = line[:line.index('//')]
                    processed_lines.append(line)
                content = '\n'.join(processed_lines)
                
                # 处理字符串重复表达式
                def replace_repeat(match):
                    char = match.group(1)
                    count = int(match.group(2))
                    count = min(count, 1000)
                    return json.dumps(char * count)
                
                content = re.sub(r'"([^"]*)"\.repeat\((\d+)\)', replace_repeat, content)
                
                return content
            except Exception as e:
                logger.error(f"预处理JSON数据时出错: {str(e)}")
                return content

        # 尝试解析JSON
        try:
            # 首先检查是否是markdown格式
            match = re.search(r'```json\n(.*?)\n```', llm_content, re.DOTALL)
            if match:
                json_str = match.group(1)
                logger.info("从markdown中提取JSON数据")
            else:
                json_str = llm_content
                logger.info("直接使用LLM响应作为JSON数据")
            
            # 预处理JavaScript表达式
            json_str = preprocess_js_expressions(json_str)
            
            # 确保JSON字符串是干净的
            json_str = json_str.strip()
            
            # 解析JSON
            try:
                result = json.loads(json_str)
                logger.info("JSON解析成功")
                
                # 如果是test_cases，进行额外的数据验证
                if result_key == "test_cases" and isinstance(result, list):
                    def validate_test_case(case):
                        if not isinstance(case, dict):
                            return None
                        
                        # 确保必要字段存在且类型正确
                        required_fields = {
                            'api_name': str,
                            'request_url': str,
                            'request_method': str,
                            'content_type': str,
                            'tests': list
                        }
                        
                        for field, field_type in required_fields.items():
                            if field not in case or not isinstance(case[field], field_type):
                                logger.warning(f"测试用例缺少必要字段或类型不正确: {field}")
                                return None
                        
                        # 确保params和headers是列表
                        case['params'] = case.get('params', [])
                        if not isinstance(case['params'], list):
                            case['params'] = []
                            
                        case['headers'] = case.get('headers', [])
                        if not isinstance(case['headers'], list):
                            case['headers'] = []
                        
                        return case
                    
                    # 过滤掉无效的测试用例
                    result = [case for case in result if validate_test_case(case) is not None]
                    if not result:
                        raise ValueError("没有有效的测试用例")
                
                # 再次从数据库获取最新数据
                task_result.refresh_from_db()
                
                # 获取当前的 llm_result
                current_results = task_result.llm_result if task_result.llm_result is not None else {}
                logger.info(f"当前结果集: {current_results}")
                
                # 更新任务状态
                if result_key == "parse_result":
                    task_result.parse_task_status = SmartApiTaskResult.STATUS_SUCCESS
                    # 在确认parse任务成功后进行数据拼接
                    if isinstance(result, dict):
                        result.update(request_data)
                        current_results[result_key] = result
                    else:
                        logger.warning(f"parse_result 不是字典类型，无法合并 request_data")
                else:
                    task_result.test_cases_task_status = SmartApiTaskResult.STATUS_SUCCESS
                    # 在确认test_cases任务成功后进行数据拼接
                    if isinstance(result, list):
                        for test_case in result:
                            if isinstance(test_case, dict):
                                request_data['case_code'] = "quick_test_" + generate_random_string(10)
                                test_case.update(request_data)
                            else:
                                logger.warning(f"test_case 不是字典类型，无法合并 request_data")
                        current_results[result_key] = result
                    else:
                        logger.warning(f"test_cases 不是列表类型，无法合并 request_data")
                    
                # 更新总任务状态
                if task_result.parse_task_status == SmartApiTaskResult.STATUS_SUCCESS and \
                   task_result.test_cases_task_status == SmartApiTaskResult.STATUS_SUCCESS:
                    task_result.status = SmartApiTaskResult.STATUS_SUCCESS
                    
                    # 两个任务都成功后，按顺序执行保存操作
                    logger.info("两个任务都已完成，开始按顺序保存数据")
                    
                    # 1. 首先保存 parse_result 数据
                    parse_result = current_results.get('parse_result')
                    if isinstance(parse_result, dict):
                        logger.info("开始保存parse_result数据")
                        save_result = save_case_api_and_data(parse_result)
                        if save_result != 'success':
                            logger.error(f"保存parse_result数据失败: {save_result}")
                            task_result.error_message = f"保存parse_result数据失败: {save_result}"
                            task_result.save()
                            return None
                        logger.info("parse_result数据保存成功")
                        
                        # 2. 然后保存 test_cases 数据
                        test_cases = current_results.get('test_cases', [])
                        if isinstance(test_cases, list) and test_cases:
                            logger.info(f"开始保存{len(test_cases)}个test_cases数据")
                            for test_case in test_cases:
                                save_result = save_child_case_and_data(test_case, task_id)
                                if save_result != 'success':
                                    logger.error(f"保存test_case数据失败: {save_result}")
                                    task_result.error_message = f"保存test_case数据失败: {save_result}"
                                    task_result.save()
                                    return None
                            logger.info("所有test_cases数据保存成功")
                    
                elif task_result.parse_task_status == SmartApiTaskResult.STATUS_FAILURE or \
                     task_result.test_cases_task_status == SmartApiTaskResult.STATUS_FAILURE:
                    task_result.status = SmartApiTaskResult.STATUS_FAILURE
                else:
                    task_result.status = SmartApiTaskResult.STATUS_PARTIAL_SUCCESS
                
                task_result.save()
                logger.info(f"更新后的结果集: {task_result.llm_result}")
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                logger.error(f"JSON解析失败位置附近的内容: {json_str[max(0, e.pos-50):min(len(json_str), e.pos+50)]}")
                raise
                
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            result = None
            if result_key == "parse_result":
                task_result.parse_task_status = SmartApiTaskResult.STATUS_FAILURE
            else:
                task_result.test_cases_task_status = SmartApiTaskResult.STATUS_FAILURE
            task_result.error_message = f"{result_key} 处理失败: {str(e)}"
            task_result.status = SmartApiTaskResult.STATUS_FAILURE
            task_result.save()

        logger.info(f"已更新任务 {task_id} 的 {result_key} 结果")
        return result

    except Exception as exc:
        logger.error(f"处理任务 {task_id} 的 {result_key} 部分时发生错误: {exc}", exc_info=True)
        
        # 更新错误信息和状态
        try:
            task_result = SmartApiTaskResult.objects.get(task_id=task_id)
            task_result.error_message = f"{result_key} 处理失败: {str(exc)}"
            if result_key == "parse_result":
                task_result.parse_task_status = SmartApiTaskResult.STATUS_FAILURE
            else:
                task_result.test_cases_task_status = SmartApiTaskResult.STATUS_FAILURE
            task_result.update_task_status()
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}", exc_info=True)

        # 设置重试
        retry_countdown = 30  # 固定30秒后重试
        if "TimeoutError" in str(exc):
            retry_countdown = 60  # 如果是超时错误，则等待60秒后重试
            
        self.retry(exc=exc, countdown=retry_countdown)
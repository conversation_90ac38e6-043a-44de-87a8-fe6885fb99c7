from ..utils.openapi_parser import parse_openapi_doc
from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ..serializers.smart_api_upload_file_serializer import SmartApiUploadFileSerializer
from ..serializers.smart_api_task_result_serializer import SmartApiTaskResultSerializer
from ...models import SmartApiUploadFile, SmartApiTaskResult, UserModelUsageLimit
from base.models import MyUser
from project.models import Openai_Config
from api_test.models import Case_Tree_Folder
from ats.public.public_func.generate_random_string import generate_random_string
from .tasks import get_llm_response
from .prompt import (
    extract_prance_openapi_data_prompt,
    generate_test_cases_prompt
)
import logging
import json
from django.utils import timezone
from django.db.models import F
from django.db import transaction

# 配置日志
logger = logging.getLogger('ats-console')

class ParserView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SmartApiUploadFileSerializer

    def post(self, request):
        file_id = request.data.get('file_id')
        project_id = request.data.get('project_id')
        user_id = request.data.get('user_id')
        openai_config_id = request.data.get('openai_config_id')

        try:
            try:
                openai_config = Openai_Config.objects.get(id=openai_config_id)
                if not openai_config.value:
                    return Response(public_error_response("Openai配置值为空"))
                
                logger.info(f"获取到的OpenAI配置值类型: {type(openai_config.value)}")
                
                # 检查value是否已经是字典类型
                if isinstance(openai_config.value, dict):
                    config_value = openai_config.value
                else:
                    # 尝试解析JSON字符串
                    try:
                        config_value = json.loads(openai_config.value)
                        logger.info("OpenAI配置值成功从字符串解析为字典")
                    except (TypeError, json.JSONDecodeError) as e:
                        logger.error(f"OpenAI配置解析错误: {str(e)}")
                        return Response(public_error_response(f"OpenAI配置格式错误: {str(e)}"))
                
                # 处理特殊情况：openai_config_id为1时，使用siliconflow API
                if int(openai_config_id) == 1:
                    logger.info("检测到openai_config_id为1，将使用SiliconFlow API")
                    
                    # 检查用户是否达到每日调用限制
                    today = timezone.now().date()
                    
                    # 使用事务确保原子性
                    with transaction.atomic():
                        # 获取或创建用户使用记录
                        usage_record, created = UserModelUsageLimit.objects.get_or_create(
                            user_id=user_id,
                            usage_date=today,
                            model_id=1,
                            defaults={'usage_count': 0}
                        )
                        
                        # 检查使用次数是否达到限制
                        if usage_record.usage_count >= 10:
                            logger.warning(f"用户 {user_id} 已达到今日默认模型使用限制(10次)")
                            return Response(public_error_response("您今日已达到默认模型的使用限制(10次)，请明天再试或使用其他模型"))
                        
                        # 更新使用次数
                        usage_record.usage_count = F('usage_count') + 1
                        usage_record.save()
                        
                        # 刷新以获取最新的使用次数
                        usage_record.refresh_from_db()
                        logger.info(f"用户 {user_id} 今日已使用默认模型 {usage_record.usage_count} 次")
                    
                    # 确保config_value包含必要的SiliconFlow参数
                    if 'api_key' not in config_value:
                        return Response(public_error_response(f"Openai配置openai_config_id为{openai_config_id}时，缺少必要的参数"))

                    if 'model' not in config_value:
                        return Response(public_error_response(f"Openai配置openai_config_id为{openai_config_id}时，缺少必要的参数"))
                    
                    if 'max_tokens' not in config_value:
                        return Response(public_error_response(f"Openai配置openai_config_id为{openai_config_id}时，缺少必要的参数"))
                    
                    # 添加标识，表明使用SiliconFlow API
                    config_value['is_siliconflow'] = True
                    config_value['config_id'] = 1
                else:
                    # 常规OpenAI配置，检查必要的配置项
                    if not config_value.get('api_key') or not config_value.get('base_url') or not config_value.get('model') or not config_value.get('max_tokens'):
                        return Response(public_error_response("Openai配置中缺少必要的参数"))
                
                # 将解析后的配置赋值给openai_config对象，以便在后续使用
                openai_config.api_key = config_value.get('api_key')
                openai_config.base_url = config_value.get('base_url')
                openai_config.model = config_value.get('model')
                openai_config.max_tokens = config_value.get('max_tokens')
                openai_config.stream = config_value.get('stream', False)
                openai_config.timeout = config_value.get('timeout', 600)
                
            except Openai_Config.DoesNotExist:
                return Response(public_error_response(f"Openai配置不存在: {openai_config_id}"))
            # 获取上传文件记录
            upload_file = SmartApiUploadFile.objects.get(id=file_id)
            file_path = upload_file.file_path  # 从上传文件记录中获取文件路径
            
            user_name = MyUser.objects.get(id=user_id).username
            folder_id = Case_Tree_Folder.objects.filter(project_id=project_id, name="AI生成").first().id
            case_code_and_taks_id = "quick_test_" + generate_random_string(10)
            parse_result_request_data = {
                "case_code": case_code_and_taks_id,
                "request_path": "",
                "module_name": "quick_test",
                "case_name": "quick_test",
                "environment": 0,
                "authorization": {
                    "username": "",
                    "password": "",
                    "token": "",
                    "type": "none"
                },
                "pre_script": "",
                "post_script": "",
                "postfix": [],
                "tests": [],
                "creator": user_name,
                "execution_count": 1,
                "timeout": 10,
                "if_execution": 1,
                "folder": folder_id,
                "project": project_id
            }

            test_cases_request_data = {
                "case_code": "quick_test_" + generate_random_string(10),
                "request_path": "",
                "module_name": "quick_test",
                "case_name": "quick_test",
                "environment": 0,
                "authorization": {
                    "username": "",
                    "password": "",
                    "token": "",
                    "type": "none"
                },
                "pre_script": "",
                "post_script": "",
                "postfix": [],
                "creator": user_name,
                "execution_count": 1,
                "timeout": 10,
                "if_execution": 1,
                "folder": 0,
                "project": project_id
            }

            logger.info(f"开始解析OpenAPI文档: {file_path}")
            
            # 解析OpenAPI文档
            openapi_data = parse_openapi_doc(file_path)
            server_info = openapi_data["servers"][0]
            paths_info = openapi_data["paths"]
            logger.info("OpenAPI文档解析完成，准备处理API信息")
            
            # 创建一个列表存储组合后的API信息
            combined_api_list = []
            
            # 遍历所有路径信息
            for path, path_info in paths_info.items():
                # 为每个路径创建一个新的字典
                for method, operation in path_info.items():
                    # 删除不需要的字段
                    operation.pop("deprecated", None)
                    operation.pop("tags", None)

                    # 只保留 responses 中的 200
                    if "responses" in operation:
                        operation["responses"] = {
                            "200": operation["responses"].get("200", {})
                        }

                    api_data = {
                        "server": server_info,
                        "path": path,
                        "method": method,
                        "operation": operation
                    }
                    combined_api_list.append(api_data)

            # print(combined_api_list)
            logger.info(f"共处理了 {len(combined_api_list)} 个API接口信息")

            tasks = []
            # 如果openapi_data是列表，则为每个项目创建任务
            if isinstance(combined_api_list, list):
                for idx, api_data in enumerate(combined_api_list):
                    # 为每个API生成唯一的task_id和case_code
                    unique_task_id = "quick_test_" + generate_random_string(10)
                    
                    # 使用相同的case_code
                    parse_result_request_data['case_code'] = unique_task_id
                    test_cases_request_data['case_code'] = unique_task_id
                    
                    # 将单个api_data转换为字符串
                    api_str = json.dumps(api_data, ensure_ascii=False)
                    
                    # 创建任务结果记录
                    task_result = SmartApiTaskResult.objects.create(
                        task_id=unique_task_id,  # 使用唯一的task_id
                        file=upload_file,  # 修改为直接使用外键关联
                        status=SmartApiTaskResult.STATUS_PENDING,
                        openapi_data=api_data,
                        llm_result={
                            "parse_result": None,
                            "test_cases": None
                        },
                        parse_task_status=SmartApiTaskResult.STATUS_PENDING,
                        test_cases_task_status=SmartApiTaskResult.STATUS_PENDING
                    )
                    
                    # 将解析后的配置值包装成一个简单的字典而不是使用原始对象
                    openai_config_dict = {
                        'api_key': config_value.get('api_key'),
                        'base_url': config_value.get('base_url'),
                        'model': config_value.get('model'),
                        'max_tokens': config_value.get('max_tokens'),
                        'stream': config_value.get('stream', False),
                        'timeout': config_value.get('timeout', 600)
                    }
                    
                    # 如果是SiliconFlow配置，添加特殊标识
                    if int(openai_config_id) == 1:
                        openai_config_dict['is_siliconflow'] = True
                        openai_config_dict['config_id'] = 1
                    
                    # 1. API解析任务
                    parse_prompt = f"{api_str}\n{extract_prance_openapi_data_prompt}"
                    parse_task = get_llm_response.delay(parse_prompt, parse_result_request_data, unique_task_id, "parse_result", openai_config_dict)
                    tasks.append(unique_task_id)
                    
                    # 2. 测试用例生成任务
                    test_prompt = f"{api_str}\n{generate_test_cases_prompt}"
                    test_task = get_llm_response.delay(test_prompt, test_cases_request_data, unique_task_id, "test_cases", openai_config_dict)
                    
                    logger.info(f"开始处理第 {idx+1}/{len(combined_api_list)} 个API的多个任务")
                    logger.info(f"任务ID: {unique_task_id}")
            else:
                logger.error(f"OpenAPI文档格式错误: {combined_api_list}")
                return Response(public_error_response('OpenAPI文档格式错误'))

            return Response(public_success_response({
                'task_ids': tasks,
                'message': '任务已提交，请通过任务ID查询结果'
            }))
            
        except Exception as e:
            logger.error(f"处理过程中发生错误: {str(e)}", exc_info=True)
            return Response(public_error_response(str(e)))
            
    def get(self, request):
        """获取任务状态和结果"""
        task_id = request.query_params.get('task_id')
        file_id = request.query_params.get('file_id')
        
        try:
            if task_id:
                # 根据任务ID查询
                task_result = SmartApiTaskResult.objects.get(task_id=task_id)
            elif file_id:
                # 根据文件ID查询所有相关任务
                task_results = SmartApiTaskResult.objects.filter(file_id=file_id)
                serializer = SmartApiTaskResultSerializer(task_results, many=True)
                return Response(public_success_response(serializer.data))
            else:
                return Response(public_error_response('必须提供task_id或file_id参数'))

            serializer = SmartApiTaskResultSerializer(task_result)
            return Response(public_success_response(serializer.data))
            
        except SmartApiTaskResult.DoesNotExist:
            return Response(public_error_response('任务不存在'))
        except Exception as e:
            logger.error(f"查询任务状态时发生错误: {str(e)}", exc_info=True)
            return Response(public_error_response(str(e)))
extract_prance_openapi_data_prompt = """
我需要进行接口测试，根据以上我提供的prance 解析出来的 OpenAPI 3.1 文档中的json数据，帮我提取出有用的数据，替换到示例的json数据中，示例如下：

{
  "api_name": "新增商品接口", // 结合提取到的summary和description生成，尽量不要超过10个字
  "request_url": "http://***************:8085/post/test", // 将提取到的url和path拼接起来
  "request_method": "POST",
  // 如果params为空，则填写为"params": []
  "params": [
    {
      "id": -1,  // 固定值，不要修改
      "key": "params1",
      "type": "1",  // 固定值，不要修改
      "value": "params1",
      "desc": "对应的描述，如果为空则不需要填写"
    },
    {
      "id": -1,  // 固定值，不要修改
      "key": "params2",
      "type": "1",  // 固定值，不要修改
      "value": "params2",
      "desc": "对应的描述，如果为空则不需要填写"
    },
    {...如果还有其他params参数，继续添加}
  ],
  // 如果headers为空，则填写为"headers": []
  "headers": [
    {
      "id": -1,  // 固定值，不要修改
      "key": "header1",
      "type": "1",  // 固定值，不要修改
      "value": "header1",
      "desc": "对应的描述，如果为空则不需要填写"
    },
    {
      "id": -1,  // 固定值，不要修改
      "key": "header2",
      "type": "1",  // 固定值，不要修改
      "value": "header2",
      "desc": "对应的描述，如果为空则不需要填写"
    },
    {...如果还有其他headers参数，继续添加}
  ],
  "content_type": "application/json", // 提取不到的情况下默认都填写为application/json

  // 下面是body的填写示例，只需要根据类型填写一个body内容

  // 如果content_type是application/json，则按下面这段示例填写，如果没有数据就填写为 "body": {}
  "body": {
    "id": 123456,
    "name": "张三",
    "email": "<EMAIL>",
    "is_active": true,
    "created_at": "2025-03-18T12:00:00Z"
  },
  // 如果content_type是application/x-www-form-urlencoded，则按下面这段示例填写，如果没有数据就填写为 "body": []
  "body": [
    {
      "id": -1,  // 固定值，不要修改
      "key": "xwwwformurlencoded1",
      "key_type": "1",  // 固定值，不要修改
      "body_type": "3",  // 固定值，不要修改
      "value": "xwwwformurlencoded1",
      "desc": "对应的描述，如果为空则不需要填写"
    },
    {
      "id": -1,  // 固定值，不要修改
      "key": "xwwwformurlencoded2",
      "key_type": "1",  // 固定值，不要修改
      "body_type": "3",  // 固定值，不要修改
      "value": "xwwwformurlencoded2",
      "desc": "对应的描述，如果为空则不需要填写"
    },
    {...如果还有其他x_www_form_urlencoded参数，继续添加}
  ],

  // 如果content_type是multipart/form-data，则按下面这段示例填写，如果没有数据就填写为 "body": []
  "body": [
    {
      "id": -1,  // 固定值，不要修改
      "key": "formdata1",
      "key_type": "1",  // 固定值，不要修改
      "body_type": "2",  // 固定值，不要修改
      "value": "formdata1",
      "desc": "对应的描述，如果为空则不需要填写",
      "file": []
    },
    {
      "id": -1,  // 固定值，不要修改
      "key": "formdata1",
      "key_type": "1",  // 固定值，不要修改
      "body_type": "2",  // 固定值，不要修改
      "value": "formdata1",
      "desc": "对应的描述，如果为空则不需要填写",
      "file": []
    },
    {...如果还有其他multipart/form-data参数，继续添加}
  ]
}

请确保最终生成的json数据满足以下要求：
1. 按照我的示例以及注释要求生成json数据
2. 不要遗漏任何有用的数据
3. 提取的时候不要改变原来json数据的顺序
4. 不要包含注释
"""


generate_test_cases_prompt = """
你是一名专业的软件测试工程师，擅长使用 OpenAPI 接口文档进行自动化接口测试设计。

上面是我向你提供通过 Prance 工具解析出来的 OpenAPI 3.1 文档中的 JSON 数据。

你的任务是根据这些接口定义，**智能生成多组请求数据，同时每组都需要包含断言**，做到全面覆盖接口测试的目的，包括但不限于以下测试设计方法：

1. **等价类划分**：为每个字段生成有效值与无效值（如合法/非法格式、正确/错误类型）。
2. **边界值分析**：为数字/字符串字段生成最大值、最小值、边界值等，字符串的最大值不要过大。
3. **必填/非必填验证**：测试缺少必填字段的情况。
4. **嵌套结构和数组字段**：考虑多种嵌套层级的数据组合。
5. **组合测试**：尝试不同字段组合，测试系统对边界情况的处理。

请生成如下格式的测试数据（建议2组-3组左右），每一组数据中都需要包含对应数据的断言，特别是正常数据下断言内容一定要多一点：

{
  "api_name": "边界值测试", // 结合你生成的测试数据生成，尽量不要超过10个字
  "request_url": "http://***************:8085/post/test", // 将提取到的url和path拼接起来
  "request_method": "POST",
  // 如果params为空，则填写为"params": []
  "params": [
    {
      "id": -1,
      "key": "params1", // 你生成的测试数据中的key
      "type": "1",
      "value": "params1", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内"
    },
    {
      "id": -1,
      "key": "params2", // 你生成的测试数据中的key
      "type": "1",
      "value": "params2", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内"
    },
    {...如果还有其他params参数，继续添加}
  ],
  // 如果headers为空，则填写为"headers": []
  "headers": [
    {
      "id": -1,
      "key": "header1", // 你生成的测试数据中的key
      "type": "1",
      "value": "header1", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内"
    },
    {
      "id": -1,
      "key": "header2", // 你生成的测试数据中的key
      "type": "1",
      "value": "header2", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内"
    },
    {...如果还有其他headers参数，继续添加}
  ],
  "content_type": "application/json", // 提取不到的情况下默认都填写为application/json

  // 下面是body的填写示例，只需要根据类型填写一个body内容

  // 如果content_type是application/json，则按下面这段示例填写，如果没有数据就填写为 "body": {}
  "body": {
    "id": 123456, // 你生成的测试数据
    "name": "张三", // 你生成的测试数据
    "email": "<EMAIL>", // 你生成的测试数据
    "is_active": true, // 你生成的测试数据
    "created_at": "2025-03-18T12:00:00Z" // 你生成的测试数据
  },
  // 如果content_type是application/x-www-form-urlencoded，则按下面这段示例填写，如果没有数据就填写为 "body": []
  "body": [
    {
      "id": -1,
      "key": "xwwwformurlencoded1", // 你生成的测试数据中的key
      "key_type": "1",
      "body_type": "3",
      "value": "xwwwformurlencoded1", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内"
    },
    {
      "id": -1,
      "key": "xwwwformurlencoded2", // 你生成的测试数据中的key
      "key_type": "1",
      "body_type": "3",
      "value": "xwwwformurlencoded2", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内"
    },
    {...如果还有其他x_www_form_urlencoded参数，继续添加}
  ],

  // 如果content_type是multipart/form-data，则按下面这段示例填写，如果没有数据就填写为 "body": []
  "body": [
    {
      "id": -1,
      "key": "formdata1", // 你生成的测试数据中的key
      "key_type": "1",
      "body_type": "2",
      "value": "formdata1", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内",
      "file": []
    },
    {
      "id": -1,
      "key": "formdata1", // 你生成的测试数据中的key
      "key_type": "1",
      "body_type": "2",
      "value": "formdata1", // 你生成的测试数据中的value
      "desc": "测试数据和方法描述，10个字以内",
      "file": []
    },
    {...如果还有其他multipart/form-data参数，继续添加}
  ],
  "tests": [
  // obj 1 表示响应状态码断言，2 表示响应头断言，3 表示响应体断言，4 表示响应url断言
  // 断言名称要尽量简洁，不要超过10个字
  // 同类型的断言可以有多个，你自由增加即可，覆盖全面
  // 只有obj为4的时候才需要填写jsonpath，其他情况下不需要填写，id固定为-1，data不管是数字还是字符串，都填写为字符串形式
    {
      "id": -1,
      "name": "响应状态码是200", // 你生成的断言名称
      "obj": "1",
      "header_key": "",
      "operator": "等于", // 你选择的断言操作符 等于、不等于
      "data": "200", // 你生成的断言数据
      "jsonpath": ""
    },
    {
      "id": -1,
      "name": "响应头中包含Server", // 你生成的断言名称
      "obj": "2",
      "header_key": "Server", // 你生成的断言的header_key
      "operator": "等于", // 你选择的断言操作符 等于、包含、存在
      "data": "gunicorn/19.9.0", // 你生成的断言数据
      "jsonpath": ""
    },
    {
      "id": -1,
      "name": "响应体中包含ccc", // 你生成的断言名称
      "obj": "3",
      "header_key": "",
      "operator": "包含", // 你选择的断言操作符 等于、包含
      "data": "ccc", // 你生成的断言数据
      "jsonpath": ""
    },
    {
      "id": -1,
      "name": "响应体中name等于张三", // 你生成的断言名称
      "obj": "4",
      "header_key": "",
      "operator": "等于", // 你选择的断言操作符 等于、包含、存在、大于、小于、大于等于、小于等于、不等于、类型等于
      "data": "张三", // 你生成的断言数据，如果断言操作符是类型等于，则data填写类型，如"int"、"string"、"boolean"、"array"、"object"、"null"、"number"
      "jsonpath": "$.url" // 你生成的断言的jsonpath，务必准确，基于响应体结构生成
    }
    {...如果还有其他断言，继续添加}
  ]
}

请确保最终生成的json数据还满足以下要求：
1. 测试覆盖全面，数据合理，测试数据以body为主，params和headers为辅
2. 测试数据一定是具体数值，不要出现函数或.repeat()这种json不能识别的内容！不要出现函数或.repeat()这种json不能识别的内容！不要出现函数或.repeat()这种json不能识别的内容！
3. 断言数据要根据请求数据合理生成，不要出现断言数据和请求数据不匹配的情况
4. 断言要尽量全面，要根据响应数据结构生成正确的jsonpath
5. 最终生成的json数据不要出现注释和其他无用的文字内容
6. 最终生成的json数据都统一放在[]中
"""
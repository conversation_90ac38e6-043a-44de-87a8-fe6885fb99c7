import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.pagination import PageNumberPagination
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...models import SmartApiUploadFile
from ..serializers.smart_api_upload_file_serializer import SmartApiUploadFileSerializer
logger = logging.getLogger('ats-console')


class SmartApiUploadFileView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def get(self, request):
        # 获取id参数，如果有id则查询单个，否则查询列表
        file_id = request.query_params.get('id')
        project_id = request.query_params.get('project_id')
        
        if file_id:
            return self.retrieve(request, file_id, project_id)
            
        page_size = request.query_params.get('page_size', default=10)
        page_number = request.query_params.get('page', default=1)
        

        try:
            paginator = PageNumberPagination()
            paginator.page_size = int(page_size)
            paginator.page = int(page_number)
        except ValueError:
            logger.error("Pagination parameter format error!")
            return Response(public_error_response('分页参数格式错误！'), status=status.HTTP_400_BAD_REQUEST)

        queryset = SmartApiUploadFile.objects.all()
        if project_id:
            queryset = queryset.filter(project_id=project_id)
            
        page = paginator.paginate_queryset(queryset, request)
        serializer = SmartApiUploadFileSerializer(page, many=True)
        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serializer.data
        }
        return paginator.get_paginated_response(response_data)
        
    def retrieve(self, request, file_id, project_id=None):
        """查看单个上传文件详情"""
        try:
            if project_id:
                file_obj = SmartApiUploadFile.objects.get(id=file_id, project_id=project_id)
            else:
                file_obj = SmartApiUploadFile.objects.get(id=file_id)
        except SmartApiUploadFile.DoesNotExist:
            logger.error(f"文件ID不存在: {file_id}")
            return Response(public_error_response('文件不存在！'), status=status.HTTP_404_NOT_FOUND)
            
        serializer = SmartApiUploadFileSerializer(file_obj)
        return Response(public_success_response(serializer.data))
        
    def delete(self, request):
        """删除上传文件"""
        file_id = request.query_params.get('id')
        
        if not file_id:
            logger.error("删除文件时缺少ID参数")
            return Response(public_error_response('缺少文件ID！'), status=status.HTTP_400_BAD_REQUEST)
            
        try:
            file_obj = SmartApiUploadFile.objects.get(id=file_id)
        except SmartApiUploadFile.DoesNotExist:
            logger.error(f"要删除的文件ID不存在: {file_id}")
            return Response(public_error_response('文件不存在！'), status=status.HTTP_404_NOT_FOUND)
            
        file_obj.delete()
        logger.info(f"文件删除成功: {file_id}")
        return Response(public_success_response("文件删除成功"))

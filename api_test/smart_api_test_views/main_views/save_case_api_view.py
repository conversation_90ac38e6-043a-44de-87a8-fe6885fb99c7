import logging
import requests
import json
from django.conf import settings
from ...models import TestCase
from ats.public.public_func.generate_random_string import generate_random_string

logger = logging.getLogger('ats-console')

def save_case_api_and_data(data: dict):
    try:
        # 确保输入数据是字典类型
        if isinstance(data, str):
            try:
                data = json.loads(data)
                logger.info("成功将输入字符串解析为JSON对象")
            except json.JSONDecodeError as e:
                logger.error(f"解析输入数据为JSON对象失败: {str(e)}")
                return f'error: 输入数据格式错误 - {str(e)}'
                
        case_request_data = data.copy()  # 创建数据的副本，避免修改原始数据
        case_request_data['api_name'] = case_request_data['api_name'] + generate_random_string(6)
        
        # 确保 body 字段存在且格式正确
        if 'body' not in case_request_data:
            logger.warning("数据中缺少 body 字段，添加空对象")
            case_request_data['body'] = {}
        elif case_request_data.get('content_type') == 'application/json':
            if isinstance(case_request_data['body'], str):
                try:
                    # 尝试将字符串解析为 JSON 对象
                    case_request_data['body'] = json.loads(case_request_data['body'])
                    logger.info("成功将 body 字符串解析为 JSON 对象")
                except json.JSONDecodeError as e:
                    logger.error(f"解析 body 字符串为 JSON 对象失败: {str(e)}")
                    case_request_data['body'] = {}
        
        case_tree_folder_data = {
            "name": case_request_data.get('api_name'),
            "parent": data.get('folder'),
            "type": "0",
            "if_stream": False
        }
        base_url = f"http://{settings.SERVER_IP}:{settings.NGINX_PORT}/api/api_test"
        case_tree_folder_full_url = f"{base_url}/case_tree_folder/"
        headers = { 'x-api-key': 'smart_api_test' }
        
        logger.info(f"开始保存用例树，数据: {case_tree_folder_data}")
        response = requests.post(case_tree_folder_full_url, json=case_tree_folder_data, headers=headers)
        
        if response.json()['code'] == 2000:
            logger.info(f"保存用例树成功")
            case_request_data['folder'] = response.json()['data']['id']
            case_api_data_save_full_url = f"{base_url}/case_request_data/"
            
            logger.info(f"开始保存用例数据，数据: {case_request_data}")
            case_api_data_save_response = requests.post(case_api_data_save_full_url, json=case_request_data, headers=headers)
            
            if case_api_data_save_response.status_code == 200:
                logger.info(f"保存用例数据成功")
                return 'success'
            else:
                logger.error(f"保存用例数据失败 - 状态码: {case_api_data_save_response.status_code}, 响应内容: {case_api_data_save_response.text}")
                return 'error'
        else:
            logger.error(f"保存用例树失败 - 状态码: {response.status_code}, 响应内容: {response.text}")
            return 'error'
    except Exception as e:
        logger.error(f"保存用例数据过程中发生异常: {str(e)}", exc_info=True)
        return f'error: {str(e)}'
    
def save_child_case_and_data(data: dict, task_id: str):
    try:
        # 确保输入数据是字典类型
        if isinstance(data, str):
            try:
                data = json.loads(data)
                logger.info("成功将输入字符串解析为JSON对象")
            except json.JSONDecodeError as e:
                logger.error(f"解析输入数据为JSON对象失败: {str(e)}")
                return f'error: 输入数据格式错误 - {str(e)}'
                
        child_case_request_data = data.copy()  # 创建数据的副本，避免修改原始数据
        child_case_request_data['api_name'] = child_case_request_data['api_name'] + generate_random_string(6)
        
        # 确保 body 字段存在且格式正确
        if 'body' not in child_case_request_data:
            logger.warning("数据中缺少 body 字段，添加空对象")
            child_case_request_data['body'] = {}
        elif child_case_request_data.get('content_type') == 'application/json':
            if isinstance(child_case_request_data['body'], str):
                try:
                    # 尝试将字符串解析为 JSON 对象
                    child_case_request_data['body'] = json.loads(child_case_request_data['body'])
                    logger.info("成功将 body 字符串解析为 JSON 对象")
                except json.JSONDecodeError as e:
                    logger.error(f"解析 body 字符串为 JSON 对象失败: {str(e)}")
                    child_case_request_data['body'] = {}
        
        test_case = TestCase.objects.filter(case_code=task_id).first()
        parent_id = test_case.folder_id if test_case else None
        case_tree_folder_data = {
            "name": child_case_request_data.get('api_name'),
            "parent": parent_id,
            "type": "2",
            "case_level": "2"
        }
        base_url = f"http://{settings.SERVER_IP}:{settings.NGINX_PORT}/api/api_test"
        case_tree_folder_full_url = f"{base_url}/create_case_from_ai/"
        headers = { 'x-api-key': 'smart_api_test' }
        
        logger.info(f"开始保存child用例树，数据: {case_tree_folder_data}")
        response = requests.post(case_tree_folder_full_url, json=case_tree_folder_data, headers=headers)
        
        if response.json()['code'] == 2000:
            logger.info(f"保存child用例树成功")
            child_case_request_data['folder'] = response.json()['data']['id']
            case_api_data_save_full_url = f"{base_url}/child_case_request_data/"
            
            logger.info(f"开始保存child用例数据，数据: {child_case_request_data}")
            case_api_data_save_response = requests.post(case_api_data_save_full_url, json=child_case_request_data, headers=headers)
            
            if case_api_data_save_response.status_code == 200:
                logger.info(f"保存child用例数据成功")
                return 'success'
            else:
                logger.error(f"保存child用例数据失败 - 状态码: {case_api_data_save_response.status_code}, 响应内容: {case_api_data_save_response.text}")
                return 'error'
        else:
            logger.error(f"保存child用例树失败 - 状态码: {response.status_code}, 响应内容: {response.text}")
            return 'error'
    except Exception as e:
        logger.error(f"保存child用例数据过程中发生异常: {str(e)}", exc_info=True)
        return f'error: {str(e)}'
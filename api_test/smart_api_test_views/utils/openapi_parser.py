import os
import prance
from typing import Dict


def parse_openapi_doc(file_path: str) -> Dict:
    try:
        if not file_path:
            raise ValueError("File path cannot be empty")

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        if not os.access(file_path, os.R_OK):
            raise PermissionError(f"File is not readable: {file_path}")

        valid_extensions = ['.json', '.yaml', '.yml']
        if not any(file_path.lower().endswith(ext) for ext in valid_extensions):
            raise ValueError(f"Invalid file type. Supported types: {', '.join(valid_extensions)}")

        parser = prance.ResolvingParser(file_path, strict=False)
        openapi_data = parser.specification
        return openapi_data

    except (FileNotFoundError, PermissionError, ValueError) as e:
        print(f"文件验证错误: {e}")
        raise
    except Exception as e:
        print(f"OpenAPI 3.1 文档格式错误: {e}")
        raise
import requests

def call_siliconflow_api(prompt, model="deepseek-ai/DeepSeek-V3", max_tokens=8192, temperature=0.7, top_p=0.9, api_key="sk-ivuluxmnpiwotoztqvknhwjsoqfwcnvnpingybtqubwcbmpj"):
    """
    调用SiliconFlow API获取LLM响应
    
    参数:
    prompt: 提示文本
    model: 模型名称
    max_tokens: 最大生成token数
    temperature: 温度参数
    top_p: top_p参数
    api_key: API密钥
    
    返回:
    API响应内容
    """
    url = "https://api.siliconflow.cn/v1/chat/completions"
    
    payload = {
        "model": model,
        "stream": False,
        "max_tokens": max_tokens,
        "temperature": temperature,
        "top_p": top_p,
        "stop": [],
        "messages": [
            {
                "role": "system", 
                "content": "你是一名专业的软件测试工程师，擅长使用 OpenAPI 接口文档数据生成有效的测试数据和断言"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    }
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    response = requests.request("POST", url, json=payload, headers=headers)
    
    if response.status_code == 200:
        try:
            return response.json()
        except Exception as e:
            raise Exception(f"解析SiliconFlow API响应失败: {str(e)}")
    else:
        raise Exception(f"SiliconFlow API调用失败，状态码: {response.status_code}, 响应: {response.text}")

# 以下是示例代码，用于测试函数
# if __name__ == "__main__":
#     response = call_siliconflow_api("帮我写一首诗")
#     print(response)
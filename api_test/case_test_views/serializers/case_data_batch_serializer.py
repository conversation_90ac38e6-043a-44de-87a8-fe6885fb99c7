from rest_framework import serializers
from api_test.models import TestCaseApiParams, TestCaseApiAuth, TestCaseApiHeaders, TestCaseApiBody, \
    TestCaseApiExtract, TestCaseApiAssertion


class TestCaseApiParamsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseApiParams
        fields = '__all__'


class TestCaseApiAuthSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseApiAuth
        fields = '__all__'


class TestCaseApiHeadersSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseApiHeaders
        fields = '__all__'


class TestCaseApiBodySerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseApiBody
        fields = '__all__'


class TestCaseApiExtractSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseApiExtract
        fields = '__all__'


class TestCaseApiAssertionSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseApiAssertion
        fields = '__all__'

from rest_framework import serializers
from ...models import Test<PERSON><PERSON><PERSON>hil<PERSON>, TestCaseChildExecution


class TestChildCaseRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseChild
        fields = '__all__'


class TestChildCaseExecutionSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestCaseChildExecution
        fields = ['execution_count', 'timeout', 'if_execution']

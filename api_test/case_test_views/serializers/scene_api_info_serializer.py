from ...models import SceneApiInfo
from rest_framework import serializers
from project.models import Environment, Project

class SceneApiInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = SceneApiInfo
        fields = '__all__'


class SceneApiInfoListSerializer(serializers.ModelSerializer):
    environment_name = serializers.SerializerMethodField()
    project_name = serializers.SerializerMethodField()

    class Meta:
        model = SceneApiInfo
        fields = '__all__'  # 保持原有字段

    def get_environment_name(self, obj):
        try:
            environment = Environment.objects.get(id=obj.environment_id)
            return environment.name
        except Environment.DoesNotExist:
            return None

    def get_project_name(self, obj):
        try:
            project = Project.objects.get(id=obj.project_id)
            return project.name
        except Project.DoesNotExist:
            return None
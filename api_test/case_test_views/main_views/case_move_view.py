from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ...models import Case_Tree_Folder, TestCase, TestCaseChild


class CaseMoveView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def post(self, request):
        try:
            # 获取需要移动的接口ID和目标文件夹ID
            case_id = request.data.get('case_id')
            target_folder_id = request.data.get('target_folder_id')
            
            # 验证参数
            if not case_id or not target_folder_id:
                return Response(public_error_response('缺少必要参数: case_id 或 target_folder_id'))
            
            # 获取要移动的接口和目标文件夹
            case = Case_Tree_Folder.objects.get(id=case_id)
            target_folder = Case_Tree_Folder.objects.get(id=target_folder_id)
            
            # 验证目标是否为目录
            if target_folder.type != '1':
                return Response(public_error_response('目标必须是目录类型'))
            
            old_parent_id = case.parent_id
            
            # 如果是接口类型
            if case.type == '0':
                # 首先移动接口本身
                case.parent = target_folder
                case.save()

                return Response(public_success_response({
                    'message': '接口及其用例移动成功',
                    'case_id': case.id,
                    'case_name': case.name,
                    'old_parent_id': old_parent_id,
                    'new_parent_id': target_folder.id,
                    # 'child_cases_count': len(child_cases)
                }))
            else:
                return Response(public_error_response('只支持移动接口类型'))
            
        except Case_Tree_Folder.DoesNotExist:
            return Response(public_error_response('接口或目标文件夹不存在'))
        except Exception as e:
            return Response(public_error_response(f'移动失败，错误信息: {str(e)}')) 
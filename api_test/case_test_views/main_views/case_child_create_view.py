from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ...models import (TestCase, TestCaseExecution, TestCaseChild, TestCaseChildExecution,
                       TestCaseApiAuth, TestCaseApiBody, TestCaseApiExtract, TestCaseApiParams, TestCaseApiHeaders,
                       TestCaseApiAssertion)
from ..serializers.case_tree_folder_serializer import CaseTreeFolderSerializer


def copy_case_api_data_to_child(child_tree_id, case_api_folder_id, child_api_level, case_name):
    case_api = TestCase.objects.get(folder_id=case_api_folder_id)
    case_api_dict = case_api.__dict__
    case_api_dict['case_level'] = child_api_level
    case_api_dict['folder_id'] = child_tree_id
    case_api_dict['case_name'] = case_name
    case_api_dict['api_name'] = case_name
    del case_api_dict['_state']
    case_api_id = case_api_dict.pop('id')
    # print("case_api_dict", case_api_dict)

    child_api = TestCaseChild.objects.create(**case_api_dict)
    case_api_execution = TestCaseExecution.objects.get(test_case_id=case_api_id)
    case_api_execution_dict = case_api_execution.__dict__
    del case_api_execution_dict['_state']
    del case_api_execution_dict['id']
    del case_api_execution_dict['analysis']
    case_api_execution_dict['test_case_id'] = child_api.id
    # print("case_api_execution_dict", case_api_execution_dict)
    TestCaseChildExecution.objects.create(**case_api_execution_dict)

    all_auth = TestCaseApiAuth.objects.filter(api_id=case_api_id)
    for auth in all_auth:
        auth_dict = auth.__dict__
        del auth_dict['_state']
        auth_dict['api_case_id'] = child_api.id
        auth_dict['api_id'] = None
        del auth_dict['id']
        TestCaseApiAuth.objects.create(**auth_dict)

    all_body = TestCaseApiBody.objects.filter(api_id=case_api_id)
    for body in all_body:
        body_dict = body.__dict__
        del body_dict['_state']
        body_dict['api_case_id'] = child_api.id
        body_dict['api_id'] = None
        del body_dict['id']
        TestCaseApiBody.objects.create(**body_dict)

    all_extract = TestCaseApiExtract.objects.filter(api_id=case_api_id)
    for extract in all_extract:
        extract_dict = extract.__dict__
        del extract_dict['_state']
        extract_dict['api_case_id'] = child_api.id
        extract_dict['api_id'] = None
        del extract_dict['id']
        TestCaseApiExtract.objects.create(**extract_dict)

    all_params = TestCaseApiParams.objects.filter(api_id=case_api_id)
    for params in all_params:
        params_dict = params.__dict__
        del params_dict['_state']
        params_dict['api_case_id'] = child_api.id
        params_dict['api_id'] = None
        del params_dict['id']
        TestCaseApiParams.objects.create(**params_dict)

    all_headers = TestCaseApiHeaders.objects.filter(api_id=case_api_id)
    for headers in all_headers:
        headers_dict = headers.__dict__
        del headers_dict['_state']
        headers_dict['api_case_id'] = child_api.id
        headers_dict['api_id'] = None
        del headers_dict['id']
        TestCaseApiHeaders.objects.create(**headers_dict)

    all_assertion = TestCaseApiAssertion.objects.filter(api_id=case_api_id)
    for assertion in all_assertion:
        assertion_dict = assertion.__dict__
        del assertion_dict['_state']
        assertion_dict['api_case_id'] = child_api.id
        assertion_dict['api_id'] = None
        del assertion_dict['id']
        TestCaseApiAssertion.objects.create(**assertion_dict)


class CaseChildCreateView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    case_tree_serializer_class = CaseTreeFolderSerializer

    def post(self, request):
        case_level = request.data.get('case_level')
        case_name = request.data.get('name')
        parent_tree_id = request.data.get('parent')
        try:
            TestCase.objects.get(folder_id=parent_tree_id)
        except TestCase.DoesNotExist:
            return Response(public_error_response('接口数据为空，请先完善接口'))
        case_tree_serializer = self.case_tree_serializer_class(data=request.data)
        if case_tree_serializer.is_valid():
            child_tree = case_tree_serializer.save()
            child_tree_id = child_tree.id
            parent_tree_id = child_tree.parent_id
            try:
                copy_case_api_data_to_child(child_tree_id, parent_tree_id, case_level, case_name)
            except Exception as e:
                return Response(public_error_response(f'新建失败，错误信息：{e}'))
            return Response(public_success_response(case_tree_serializer.data))
        return Response(public_error_response(case_tree_serializer.errors))

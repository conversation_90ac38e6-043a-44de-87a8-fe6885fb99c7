from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myauthentication import My<PERSON>uthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ....models import CaseFolderOverview
from ...serializers.case_folder_overview_serializer import CaseFolderOverviewSerializer


class CaseFolderOverviewView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = CaseFolderOverviewSerializer

    def get(self, request):
        folder_id = request.query_params.get('folder_id')
        query_set = CaseFolderOverview.objects.filter(folder_id=folder_id).first()
        serializer = self.serializer_class(query_set, many=False)
        return Response(public_success_response(serializer.data))
    
    def post(self, request):
        data = request.data
        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            CaseFolderOverview.objects.filter(folder_id=data['folder']).delete()
            serializer.save()
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))



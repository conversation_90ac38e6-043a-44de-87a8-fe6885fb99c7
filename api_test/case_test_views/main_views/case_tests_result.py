from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from rest_framework.views import APIView
from rest_framework.response import Response
from ...models import CaseTestsResult, TestCase, TestCaseChild, TestCaseChildExecution
from ..serializers.case_tests_result_serializer import CaseTestsResultSerializer
import logging

logger = logging.getLogger('ats-console')

class CaseTestsResultView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = CaseTestsResultSerializer

    def get(self, request):
        folder_id = request.query_params.get('folder_id')
        query_type = request.query_params.get('query_type')
        if folder_id and query_type == '0':
            try:
                # 记录查询TestCase的开始
                logger.info(f"开始查询TestCase - folder_id: {folder_id}")
                api_id = TestCase.objects.get(folder_id=folder_id).id
                queryset = CaseTestsResult.objects.filter(api_id=api_id)
                serializer = self.serializer_class(queryset, many=True)
                return Response(public_success_response(serializer.data))
            except TestCase.DoesNotExist:
                logger.error(f"TestCase不存在 - folder_id: {folder_id}")
                return Response(public_error_response('该树节点下没有API'))
            except Exception as e:
                logger.error(f"查询TestCase测试结果时发生异常 - folder_id: {folder_id}, error: {str(e)}")
                return Response(public_error_response(f'查询测试结果失败: {str(e)}'))
                
        elif folder_id and query_type == '2':
            try:
                # 记录查询TestCaseChild的开始
                logger.info(f"开始查询TestCaseChild - folder_id: {folder_id}")
                api_id = TestCaseChild.objects.get(folder_id=folder_id).id
                logger.info(f"成功查询到TestCaseChild - api_id: {api_id}")
                
                # 查询测试结果
                logger.debug(f"开始查询CaseTestsResult - api_case_id: {api_id}")
                queryset = CaseTestsResult.objects.filter(api_case_id=api_id)
                result_count = queryset.count()
                logger.info(f"查询到CaseTestsResult数量: {result_count} - api_case_id: {api_id}")
                
                serializer = self.serializer_class(queryset, many=True)
                logger.info(f"TestCaseChild测试结果数据序列化成功 - folder_id: {folder_id}, 结果数量: {result_count}")
                return Response(public_success_response(serializer.data))
            except TestCaseChild.DoesNotExist:
                logger.error(f"TestCaseChild不存在 - folder_id: {folder_id}")
                return Response(public_error_response('该树节点下没有API'))
            except Exception as e:
                logger.error(f"查询TestCaseChild测试结果时发生异常 - folder_id: {folder_id}, error: {str(e)}")
                return Response(public_error_response(f'查询测试结果失败: {str(e)}'))
        else:
            logger.error("Failed to get a tree node")
            return Response(public_error_response('获取树节点失败'))


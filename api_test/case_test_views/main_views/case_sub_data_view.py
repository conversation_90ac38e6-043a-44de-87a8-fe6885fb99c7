from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myauthentication import My<PERSON>uthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...models import TestCase, TestCaseApiHeaders, TestCaseApiAssertion, \
    TestCaseApiBody, TestCaseApiAuth, TestCaseApiParams, TestCaseApiExtract, TestCaseChild
from ..serializers.case_data_batch_serializer import Test<PERSON>ase<PERSON>piParamsSerializer, TestCaseApiHeadersSerializer, \
    TestCaseApiAuthSerializer, TestCaseApiExtractSerializer, TestCaseApiAssertionSerializer, \
    TestCaseApiBodySerializer

import logging

logger = logging.getLogger('ats-console')

class CaseSubDataDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    
    def get(self, request):
        tree_id = request.query_params.get('tree_id')
        query_type = request.query_params.get('query_type')

        if query_type == '0':
            try:
                # 记录查询TestCase的开始
                logger.info(f"开始查询TestCase - folder_id: {tree_id}")
                api = TestCase.objects.get(folder_id=tree_id)
                logger.info(f"成功查询到TestCase - id: {api.id}, case_code: {api.case_code}, api_name: {api.api_name}")
                
                params_query_set = TestCaseApiParams.objects.filter(api=api)
                params_serializer = TestCaseApiParamsSerializer(params_query_set, many=True)
                params = params_serializer.data

                headers_query_set = TestCaseApiHeaders.objects.filter(api=api)
                headers_serializer = TestCaseApiHeadersSerializer(headers_query_set, many=True)
                headers = headers_serializer.data

                authorization_query_set = TestCaseApiAuth.objects.get(api=api)
                authorization_serializer = TestCaseApiAuthSerializer(authorization_query_set, many=False)
                authorization = authorization_serializer.data

                postfix_query_set = TestCaseApiExtract.objects.filter(api=api)
                postfix_serializer = TestCaseApiExtractSerializer(postfix_query_set, many=True)
                postfix = postfix_serializer.data
                logger.debug(f"查询到postfix数量: {len(postfix)}")

                tests_query_set = TestCaseApiAssertion.objects.filter(api=api)
                tests_serializer = TestCaseApiAssertionSerializer(tests_query_set, many=True)
                tests = tests_serializer.data
                logger.debug(f"查询到tests数量: {len(tests)}")

                body_query_set = TestCaseApiBody.objects.filter(api=api)
                body_serializer = TestCaseApiBodySerializer(body_query_set, many=True)
                body = body_serializer.data
                logger.debug(f"查询到body数量: {len(body)}")

                data = {
                    'case_code': api.case_code,
                    'api_name': api.api_name,
                    'request_path': api.request_path,
                    'environment': api.environment,
                    'request_url': api.request_url,
                    'request_method': api.request_method,
                    'params': params,
                    'headers': headers,
                    'content_type': api.content_type,
                    'body': body,
                    'authorization': authorization,
                    'pre_script': api.pre_script,
                    'post_script': api.post_script,
                    'postfix': postfix,
                    'tests': tests,
                    'creator': api.creator
                }
                return Response(public_success_response(data))
            except TestCase.DoesNotExist:
                logger.error(f"TestCase不存在 - folder_id: {tree_id}")
                return Response(public_error_response('接口不存在'), status=200)
            except Exception as e:
                logger.error(f"查询TestCase数据时发生异常 - tree_id: {tree_id}, error: {str(e)}")
                return Response(public_error_response(f'查询数据失败: {str(e)}'), status=200)

        elif query_type == '2':
            try:
                # 记录查询TestCaseChild的开始
                logger.info(f"开始查询TestCaseChild - folder_id: {tree_id}")
                api = TestCaseChild.objects.get(folder_id=tree_id)
                logger.info(f"成功查询到TestCaseChild - id: {api.id}, case_code: {api.case_code}, api_name: {api.api_name}")

                # 查询相关数据
                logger.debug(f"开始查询TestCaseChild关联数据 - api_case_id: {api.id}")
                
                params_query_set = TestCaseApiParams.objects.filter(api_case=api)
                params_serializer = TestCaseApiParamsSerializer(params_query_set, many=True)
                params = params_serializer.data
                logger.debug(f"查询到params数量: {len(params)}")

                headers_query_set = TestCaseApiHeaders.objects.filter(api_case=api)
                headers_serializer = TestCaseApiHeadersSerializer(headers_query_set, many=True)
                headers = headers_serializer.data
                logger.debug(f"查询到headers数量: {len(headers)}")

                try:
                    authorization_query_set = TestCaseApiAuth.objects.get(api_case=api)
                    authorization_serializer = TestCaseApiAuthSerializer(authorization_query_set, many=False)
                    authorization = authorization_serializer.data
                    logger.debug("成功查询到authorization数据")
                except TestCaseApiAuth.DoesNotExist:
                    logger.warning(f"TestCaseApiAuth不存在 - api_case_id: {api.id}")
                    authorization = {}

                postfix_query_set = TestCaseApiExtract.objects.filter(api_case=api)
                postfix_serializer = TestCaseApiExtractSerializer(postfix_query_set, many=True)
                postfix = postfix_serializer.data
                logger.debug(f"查询到postfix数量: {len(postfix)}")

                tests_query_set = TestCaseApiAssertion.objects.filter(api_case=api)
                tests_serializer = TestCaseApiAssertionSerializer(tests_query_set, many=True)
                tests = tests_serializer.data
                logger.debug(f"查询到tests数量: {len(tests)}")

                body_query_set = TestCaseApiBody.objects.filter(api_case=api)
                body_serializer = TestCaseApiBodySerializer(body_query_set, many=True)
                body = body_serializer.data
                logger.debug(f"查询到body数量: {len(body)}")

                data = {
                    'case_code': api.case_code,
                    'api_name': api.api_name,
                    'request_path': api.request_path,
                    'environment': api.environment,
                    'request_url': api.request_url,
                    'request_method': api.request_method,
                    'params': params,
                    'headers': headers,
                    'content_type': api.content_type,
                    'body': body,
                    'authorization': authorization,
                    'pre_script': api.pre_script,
                    'post_script': api.post_script,
                    'postfix': postfix,
                    'tests': tests,
                    'creator': api.creator
                }
                logger.info(f"TestCaseChild数据查询成功 - tree_id: {tree_id}")
                return Response(public_success_response(data))
            except TestCaseChild.DoesNotExist:
                logger.error(f"TestCaseChild不存在 - folder_id: {tree_id}")
                return Response(public_error_response('接口不存在'), status=200)
            except Exception as e:
                logger.error(f"查询TestCaseChild数据时发生异常 - tree_id: {tree_id}, error: {str(e)}")
                return Response(public_error_response(f'查询数据失败: {str(e)}'), status=200)
        else:
            logger.warning(f"无效的query_type参数 - tree_id: {tree_id}, query_type: {query_type}")
            return Response(public_error_response('无效的查询类型'), status=200)

from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ...models import (TestCase, TestCaseExecution, TestCaseChild, TestCaseChildExecution,
                       TestCaseApiAuth, TestCaseApiBody, TestCaseApiExtract, TestCaseApiParams, TestCaseApiHeaders,
                       TestCaseApiAssertion)
from ..serializers.case_tree_folder_serializer import CaseTreeFolderSerializer

class CaseChildCreateFromAiView(APIView, MyAuthentication):
    def initial(self, request, *args, **kwargs):
        if request.headers.get('x-api-key') == 'smart_api_test':
            pass
        else:
            super().initial(request, *args, **kwargs)

    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    case_tree_serializer_class = CaseTreeFolderSerializer

    def post(self, request):
        case_level = request.data.get('case_level')
        case_name = request.data.get('name')
        parent_tree_id = request.data.get('parent')
        try:
            TestCase.objects.get(folder_id=parent_tree_id)
        except TestCase.DoesNotExist:
            return Response(public_error_response('接口数据为空，请先完善接口'))
        case_tree_serializer = self.case_tree_serializer_class(data=request.data)
        if case_tree_serializer.is_valid():
            child_tree = case_tree_serializer.save()
            child_tree_id = child_tree.id
            parent_tree_id = child_tree.parent_id
            return Response(public_success_response(case_tree_serializer.data))
        return Response(public_error_response(case_tree_serializer.errors))

from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ...models import Scene<PERSON>piInfo, Scene_Tree_Folder, SceneApiExecutionResult
from ...case_test_views.serializers.scene_api_info_serializer import SceneApiInfoSerializer, SceneApiInfoListSerializer

import logging

logger = logging.getLogger('ats-console')

class SceneInfoView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneApiInfoSerializer
    
    def get(self, request):
        try:
            scene_code = request.query_params.get('scene_code')
            result = SceneApiInfo.objects.get(scene_code=scene_code)
            serializer = self.serializer_class(instance=result, many=False)
            return Response(public_success_response(serializer.data))
        except Exception as e:
            logger.error("SceneApiInfo.DoesNotExist")
            return Response(public_error_response(str(e)))
        

class SceneInfoListView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneApiInfoListSerializer
    
    def get(self, request):
        page_size = request.query_params.get('page_size', default=10)
        page = request.query_params.get('page', default=1)
        project_id = request.query_params.get('project_id')
        try:
            queryset = SceneApiInfo.objects.filter(project_id=project_id).order_by('id').reverse()
        except Exception as e:
            logger.error("SceneApiInfo.DoesNotExist")
            return Response(public_error_response(str(e)))
        paginator = PageNumberPagination()
        paginator.page_size = int(page_size)
        paginator.page = int(page)
        page = paginator.paginate_queryset(queryset, request, view=self)
        if page is not None:
            serializer = self.serializer_class(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        serializer = self.serializer_class(queryset, many=True)
        return Response(public_success_response(serializer.data))
    
    def delete(self, request):
        if_tmp_scene = request.query_params.get('if_tmp_scene', default=False)
        scene_code = request.query_params.get('scene_code')
        if if_tmp_scene == 'true' and Scene_Tree_Folder.objects.filter(scene_code=scene_code).exists():
            logger.warning("Scene_Tree_Folder.DoesNotExist, can not delete")
            return Response(public_success_response('已被场景使用，不可删除'))
        try:
            SceneApiInfo.objects.filter(scene_code=scene_code).delete()
            Scene_Tree_Folder.objects.filter(scene_code=scene_code).delete()
            SceneApiExecutionResult.objects.filter(parent_scene_code=scene_code).delete()
            return Response(public_success_response('删除成功'))
        except Exception as e:
            logger.error("SceneApiInfo.DoesNotExist")
            return Response(public_error_response(str(e)))


class SceneInfoListFromTreeView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneApiInfoListSerializer
    
    def get(self, request):
        tree_id = request.query_params.get('tree_id')
        tree_obj = Scene_Tree_Folder.objects.get(id=tree_id)
        project_id = request.query_params.get('project_id')
        if tree_obj.name == '顶级目录':
            try:
                queryset = SceneApiInfo.objects.all().filter(project_id=project_id)
            except Exception as e:
                logger.error("SceneApiInfo.DoesNotExist")
                return Response(public_error_response(str(e)))
            paginator = PageNumberPagination()
            paginator.page_size = 10
            paginator.page = 1
            page = paginator.paginate_queryset(queryset, request, view=self)
            if page is not None:
                serializer = self.serializer_class(page, many=True)
                return paginator.get_paginated_response(serializer.data)
            serializer = self.serializer_class(queryset, many=True)
            return Response(public_success_response(serializer.data))

        if tree_obj.type == '1':
            children_tree_id = Scene_Tree_Folder.objects.filter(parent_id=tree_id).values_list('scene_code', flat=True)
            result = SceneApiInfo.objects.filter(scene_code__in=children_tree_id)
            serializer = self.serializer_class(instance=result, many=True)
            return Response(public_success_response(serializer.data))
        else:
            scene_code = tree_obj.scene_code
            result = SceneApiInfo.objects.filter(scene_code=scene_code)
            serializer = self.serializer_class(instance=result, many=True)
            return Response(public_success_response(serializer.data))

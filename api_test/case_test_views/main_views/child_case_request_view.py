from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ..serializers.child_case_request_serializer import TestChildCaseRequestSerializer, TestChildCaseExecutionSerializer
from ...models import TestCaseChild, TestCaseChildExecution
from .child_case_data_batch import handle_request_data, handle_request_sub_data

import logging

logger = logging.getLogger('ats-console')

class ChildCaseRequestView(APIView, MyAuthentication):
    def initial(self, request, *args, **kwargs):
        if request.headers.get('x-api-key') == 'smart_api_test':
            pass
        else:
            super().initial(request, *args, **kwargs)
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = TestChildCaseRequestSerializer

    def get(self, request):
        apis = TestCaseChild.objects.all().order_by('create_time')
        response_data = []

        for api in apis:
            api_data = self.serializer_class(api).data
            try:
                execution_data = TestCaseChildExecution.objects.get(test_case=api)
                execution_serializer = TestChildCaseExecutionSerializer(execution_data)
                api_data['execution'] = execution_serializer.data
            except TestCaseChildExecution.DoesNotExist:
                logger.error("TestCaseChildExecution.DoesNotExist")
                api_data['execution'] = {}  # 提供一个空字典作为默认值

            response_data.append(api_data)

        return Response(public_success_response(response_data))

    def post(self, request):
        orl_data = request.data
        quick_test_data = {key: value for key, value in request.data.items() if
                           key not in ['execution_count', 'timeout', 'if_execution']}
        quick_test_data = handle_request_data(quick_test_data)
        execution_data = {key: value for key, value in request.data.items() if
                          key in ['execution_count', 'timeout', 'if_execution']}
        serializer = self.serializer_class(data=quick_test_data)
        if serializer.is_valid():
            try:
                quick_test = TestCaseChild.objects.get(api_name=quick_test_data['api_name'])
                for key, value in serializer.validated_data.items():
                    setattr(quick_test, key, value)
                quick_test.save()
            except TestCaseChild.DoesNotExist:
                logger.error("TestCaseChild.DoesNotExist")
                quick_test = TestCaseChild.objects.create(**serializer.validated_data)
                TestCaseChildExecution.objects.create(test_case=quick_test, **execution_data)
            handle_request_sub_data(orl_data)
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        else:
            return Response(public_error_response(serializer.errors))


class ChildCaseRequestDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = TestChildCaseRequestSerializer

    def put(self, request, pk):
        try:
            api = TestCaseChild.objects.get(pk=pk)
            execution = TestCaseChildExecution.objects.get(test_case=api)
        except TestCaseChild.DoesNotExist:
            logger.error("TestCaseChild.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

        quick_test_data = {key: value for key, value in request.data.items() if key not in ['execution_count', 'timeout', 'if_execution']}
        execution_data = {key: value for key, value in request.data.items() if key in ['execution_count', 'timeout', 'if_execution']}

        serializer = self.serializer_class(instance=api, data=quick_test_data)
        if serializer.is_valid():
            serializer.save()
            for key, value in execution_data.items():
                setattr(execution, key, value)
            execution.save()
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        else:
            return Response(public_error_response(serializer.errors))

    def get(self, request, pk):
        try:
            api = TestCaseChild.objects.get(pk=pk)
            serializer = self.serializer_class(api)
            api_data = serializer.data.copy()

            try:
                execution_data = TestCaseChildExecution.objects.get(test_case=api)
                execution_serializer = TestChildCaseExecutionSerializer(execution_data)
                api_data['execution'] = execution_serializer.data
            except TestCaseChildExecution.DoesNotExist:
                api_data['execution'] = {}  # 提供一个空字典作为默认值

            return Response(public_success_response(api_data))

        except TestCaseChild.DoesNotExist:
            logger.error("TestCaseChild.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, pk):
        try:
            api = TestCaseChild.objects.get(pk=pk)
            api.delete()
            return Response(public_success_response('删除成功！'), status=status.HTTP_200_OK)
        except TestCaseChild.DoesNotExist:
            logger.error("TestCaseChild.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.response import Response
from ..serializers.scene_tree_folder_serializer import SceneTreeFolderSerializer
from ...models import Scene_Tree_Folder, SceneApiInfo


class SceneTreeFolderView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneTreeFolderSerializer

    def get_child_folder(self, item):
        # 匹配下级子菜单
        second_level_folders = Scene_Tree_Folder.objects.filter(parent_id=item.id)

        # 继续递归匹配是否有多级子菜单
        children = [self.get_child_folder(child) for child in second_level_folders]

        return {
            "id": item.id,
            "name": item.name,
            "type": item.type,
            "parent_id": item.parent_id,
            "children": children or None,
            "scene_code": item.scene_code
        }

    def get(self, request):
        # 需要项目id筛选查询
        project_id = request.query_params.get('project_id')
        # 获取顶级目录
        first_level_folder = Scene_Tree_Folder.objects.get(project_id=project_id, name='顶级目录', parent=None)

        # 递归下级目录
        child_folder = self.get_child_folder(first_level_folder)

        return Response(public_success_response(child_folder))

    def post(self, request):
        # scene_code = request.data.get('scene_code')
        # if not SceneApiInfo.objects.filter(scene_code=scene_code).exists():
        #     return Response(public_error_response('未找到对应场景！'))
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))


class SceneTreeFolderDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneTreeFolderSerializer

    def put(self, request, pk):
        folder = Scene_Tree_Folder.objects.get(pk=pk)
        serializer = self.serializer_class(instance=folder, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            scene_code = folder.scene_code
            if scene_code:
                SceneApiInfo.objects.filter(scene_code=scene_code).update(scene_name=request.data.get('name'))
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))

    def delete(self, request, pk):
        folder = Scene_Tree_Folder.objects.get(pk=pk)
        def delete_scene_apis(folder):
            if folder.scene_code:
                SceneApiInfo.objects.filter(scene_code=folder.scene_code).delete()
            children = Scene_Tree_Folder.objects.filter(parent_id=folder.id)
            for child in children:
                delete_scene_apis(child)

        if folder.name != '顶级目录':
            delete_scene_apis(folder)
            folder.delete()
            return Response(public_success_response('删除成功'))
        return Response(public_error_response('顶级目录不允许删除！'))

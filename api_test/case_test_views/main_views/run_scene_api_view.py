import json
from time import sleep, time
from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from project.models import Public_Data, Environment_Data
from api_test.models import SceneApiExecutionResult
from ...api_test_views.api_test_run.project.format_project_variable import format_request_data, build_context_from_vars
from ...api_test_views.api_test_run.project.action import ActionHandler
from ...api_test_views.api_test_run.project.api_assert import ApiAssert
from ...api_test_views.api_test_run.project.post_processors import ResponseProcessor
from ...api_test_views.api_test_run.project.script_executor import ScriptExecutor
from ...models import TestCase, SceneApiInfo, SceneApiExecutionResult, Case_Tree_Folder, SceneCsvFile, QuickAndCaseTempData
from ...case_test_views.serializers.scene_api_execution_result_serializer import SceneApiExecutionResultSerializer

import logging

logger = logging.getLogger('ats-console')

def detect_variable_type(value):
    """
    检测变量类型并返回对应的variable_type代码
    
    Returns:
        str: variable_type代码
        str: 处理后的值（用于存储）
    """
    if isinstance(value, bool):
        # 注意：bool检查要在int之前，因为bool是int的子类
        return '4', str(value).lower()  # 布尔值存储为 'true'/'false'
    elif isinstance(value, int):
        return '2', str(value)  # 整数
    elif isinstance(value, float):
        return '6', str(value)  # 浮点数
    elif isinstance(value, dict):
        return '5', json.dumps(value, ensure_ascii=False)  # 对象（JSON）
    elif isinstance(value, list):
        return '3', json.dumps(value, ensure_ascii=False)  # 数组（JSON）
    else:
        return '1', str(value)  # 字符串（默认）

class RunSceneApiView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneApiExecutionResultSerializer

    # 重写initial方法，跳过认证和权限检查, 给定时任务用
    def initial(self, request, *args, **kwargs):
        # 检查是否是内部调用，例如检查一个特定的请求头
        if request.headers.get('Internal-Call') == 'True':
            # 如果是内部调用，跳过认证和权限检查
            return
        # 否则，执行正常的认证和权限检查
        super().initial(request, *args, **kwargs)

    def post(self, request):
        start_time = time()  # 开始时间
        scene_apis = request.data
        scene_code = request.data.get('scene_code', None)
        # print("parent_scene_code", parent_scene_code)
        try:
            scene_info = SceneApiInfo.objects.get(scene_code=scene_code)
            # 使用TestCase模型逻辑
            api_model_name = 'TestCase'
        except SceneApiInfo.DoesNotExist:
            return Response(public_error_response('场景不存在'))
        environment_id = scene_info.environment_id
        project_id = scene_info.project_id
        iterations = scene_info.iterations
        var_file = scene_info.var_file
        delay_time = scene_info.delay_time
        folder_ids = scene_info.folder_id_of_api

        if_stream_list = Case_Tree_Folder.objects.filter(id__in=folder_ids).values_list('id', 'if_stream')
        #由于filter返回的顺序不一定和folder_ids一致，所以需要进行排序
        folder_objects = sorted(
            [{"folder_id": id, "if_stream": if_stream} for id, if_stream in if_stream_list],
            key=lambda x: folder_ids.index(x['folder_id'])
        )
        # 为了查tempdata
        api_ids = list(TestCase.objects.filter(folder_id__in=folder_ids).values_list('id', flat=True))

        if var_file:
            var_file_obj = SceneCsvFile.objects.get(parent_scene_code=scene_code)
            var_file_data_list = var_file_obj.file_content
            print("var_file_data_list", var_file_data_list)
        else:
            var_file_data_list = None
        # print("folder_if_stream_objects", folder_objects)

        if not scene_apis and not folder_ids:
            return Response(public_error_response('场景中没有接口'))

        analysis_result = []
        tests_count = 0
        send_request_times = 0  # 存储每次调用send_request的时间
        test_api_count = len(folder_ids)
        test_api_fail_count = 0
        # 修正：计算实际执行总次数（API数量 * 迭代次数）
        total_execution_count = len(folder_ids) * iterations
        total_fail_count = 0
        for i in range(iterations):
            if var_file_data_list and len(var_file_data_list) > i:
                var_file_data = var_file_data_list[i]
            else:
                var_file_data = None

            for folder_obj in folder_objects:
                folder_id = folder_obj['folder_id']
                if_stream = folder_obj['if_stream']
                sleep(delay_time / 1000)
                try:
                    # print("folder_id", folder_id)
                    api = TestCase.objects.get(folder_id=folder_id)
                except TestCase.DoesNotExist:
                    test_api_fail_count += 1
                    total_fail_count += 1
                    # analysis_result.append({"execution_result": [{"folder_id": folder_id, "execution_status": "fail", "error_info": "接口不存在"}]})
                    analysis_result.append({"folder_id": folder_id, "execution_status": "fail(接口不存在)", "error_info": None})
                    continue
                api_id = api.id
                case_code = api.case_code
                api_name = api.api_name
                module_name = api.module_name
                case_name = api.case_name
                case_level = api.case_level
                request_url = api.request_url
                request_method = api.request_method
                request_headers = api.headers
                request_body = api.body
                request_params = api.params
                request_authorization = api.authorization
                request_pre_script = api.pre_script
                request_post_script = api.post_script
                request_tests = api.tests
                request_postfix = api.postfix
                request_content_type = api.content_type
                request_path = api.request_path
                execution_count = 1
                timeout = 20
                if_execution = 1
                if_stream = if_stream
                var_file_data_dict = var_file_data
                api_model_name = api_model_name
                api_ids = api_ids
                data = {
                    "project_id": project_id,
                    "environment_id": environment_id,
                    "original_api_request_data":
                        {
                            "api_id": api_id,
                            "case_code": case_code,
                            "case_name": case_name,
                            "api_name": api_name,
                            "case_level": case_level,
                            "module_name": module_name,
                            "request_url": request_url,
                            "request_path": request_path,
                            "request_method": request_method,
                            "headers": request_headers,
                            "body": request_body,
                            "params": request_params,
                            "authorization": request_authorization,
                            "pre_script": request_pre_script,
                            "post_script": request_post_script,
                            "tests": request_tests,
                            "postfix": request_postfix,
                            "content_type": request_content_type,
                            "execution": {
                                "execution_count": execution_count,
                                "timeout": timeout,
                                "if_execution": if_execution
                            },
                            "if_stream": if_stream,
                            "var_file_data_dict": var_file_data_dict,
                            "api_model_name": api_model_name,
                            "api_ids": api_ids
                        }
                }

                formatted_api, var_data = format_request_data(data)
                # print("接口数据格式化完成：", formatted_api)
                logger.info(f"接口数据格式化完成：{formatted_api}")

                # 执行前置脚本
                script_executor = ScriptExecutor()
                
                # 使用format_request_data返回的变量数据构建context，避免重复查询数据库
                context = build_context_from_vars(var_data, api_id, api_model_name)
                
                logger.info(f"构建的context变量池: {list(context.keys())}")
                pre_script_result = script_executor.execute_pre_script(formatted_api.get('pre_script', ''), context)
                
                # 如果前置脚本有返回值，处理环境变量和项目变量
                if pre_script_result:
                    logger.info(f"前置脚本返回结果：{pre_script_result}")
                    
                    # 处理环境变量
                    env_vars = pre_script_result.get('env', {})
                    if env_vars and environment_id:
                        logger.info(f"更新环境变量 - 数量: {len(env_vars)}")
                        for key, value in env_vars.items():
                            variable_type, processed_value = detect_variable_type(value)
                            Environment_Data.objects.update_or_create(
                                key=key,
                                environment_id=environment_id,
                                defaults={
                                    'key': key,
                                    'value': processed_value,
                                    'variable_type': variable_type,
                                    'environment_id': environment_id,
                                    'type': '1'
                                }
                            )
                            logger.info(f"前置脚本更新环境变量: {key} = {value} (类型: {variable_type})")
                    
                    # 处理项目变量
                    project_vars = pre_script_result.get('project', {})
                    if project_vars:
                        logger.info(f"更新项目变量 - 数量: {len(project_vars)}")
                        for key, value in project_vars.items():
                            variable_type, processed_value = detect_variable_type(value)
                            Public_Data.objects.update_or_create(
                                key=key,
                                project_id=project_id,
                                defaults={
                                    'key': key,
                                    'value': processed_value,
                                    'variable_type': variable_type,
                                    'project_id': project_id,
                                    'type': '1'
                                }
                            )
                            logger.info(f"前置脚本更新项目变量: {key} = {value} (类型: {variable_type})")

                request_start_time = time()  # 请求开始时间
                handler = ActionHandler(formatted_api)
                response = handler.send_request()
                if 'error_info' in response:
                    error_message = response['error_info']
                    print('error_message', error_message)
                    test_api_fail_count += 1
                    total_fail_count += 1
                    analysis_result.append({"folder_id": folder_id, "execution_status": "fail(请求错误)", "error_info": error_message})
                    request_end_time = time()  # 请求结束时间
                    send_request_times += request_end_time - request_start_time # 计算此次请求时间并存储
                    continue
                request_end_time = time()  # 请求结束时间
                send_request_times += request_end_time - request_start_time # 计算此次请求时间并存储
                response_text, postfix_status = response
                try:
                    analysis = json.loads(response_text)
                except Exception as e:
                    print('保存响应数据失败了' + str(e))
                    test_api_fail_count += 1
                    total_fail_count += 1
                    analysis_result.append({"folder_id": folder_id, "execution_status": "fail(响应错误)", "error_info": str(e)})
                    continue
                    # analysis = {"error": str(e)}
                
                analysis['folder_id'] = folder_id

                # 传全部的响应和断言过去，ApiAssert中处理逻辑
                res_json_data_dict = analysis
                tests_list = formatted_api['tests']

                analysis['assertion_result'] = []
                analysis['execution_status'] = 'success'
                analysis['error_info'] = None
                analysis['iteration'] = i + 1
                
                SceneApiExecutionResult.objects.filter(parent_scene_code=scene_code).delete()
                if tests_list:
                    tests_count += len(tests_list)
                    tests_result = ApiAssert(res_json_data_dict, tests_list).result_list
                    analysis['assertion_result'] = tests_result
                    for item in tests_result:
                        if item['status'] != '通过':
                            test_api_fail_count += 1
                            total_fail_count += 1
                            analysis['execution_status'] = 'fail(断言失败)'
                            break

                postfix_list = formatted_api['postfix']

                # 后置提取设为变量
                if postfix_list and postfix_status:
                    environment_id = data['environment_id']
                    project_id = data['project_id']
                    api_id_for_postfix = data['original_api_request_data']['api_id']
                    api_model_name_for_postfix = data['original_api_request_data']['api_model_name']
                    api_ids_for_postfix = data['original_api_request_data']['api_ids']
                    postfix_obj = ResponseProcessor(res_json_data_dict, postfix_list, environment_id, project_id, api_id_for_postfix, api_model_name_for_postfix, api_ids_for_postfix)

                    environment_postfix_data = postfix_obj.environment_result_list
                    project_postfix_data = postfix_obj.project_result_list
                    tmp_postfix_data = postfix_obj.tmp_result_list

                    # print("project_postfix_data", project_postfix_data)

                    if environment_postfix_data:
                        for item in environment_postfix_data:
                            Environment_Data.objects.update_or_create(key=item['key'], defaults=item)
                    if project_postfix_data:
                        for item in project_postfix_data:
                            # print('进来了呀 project_postfix_data', item['key'])
                            Public_Data.objects.update_or_create(key=item['key'], defaults=item)
                    if tmp_postfix_data:
                        for item in tmp_postfix_data:
                            QuickAndCaseTempData.objects.update_or_create(key=item['key'], defaults=item)
                
                # 执行后置脚本
                post_script_result = script_executor.execute_post_script(
                    formatted_api.get('post_script', ''),
                    res_json_data_dict.get('res_json_data'),
                    res_json_data_dict.get('res_status_code'),
                    res_json_data_dict.get('res_headers', {}),
                    res_json_data_dict.get('res_url', ''),
                    context  # 传递context给后置脚本
                )
                
                # 如果后置脚本有返回值，处理环境变量和项目变量
                if post_script_result:
                    logger.info(f"后置脚本返回结果：{post_script_result}")
                    
                    # 处理环境变量
                    env_vars = post_script_result.get('env', {})
                    if env_vars and environment_id:
                        logger.info(f"更新环境变量 - 数量: {len(env_vars)}")
                        for key, value in env_vars.items():
                            variable_type, processed_value = detect_variable_type(value)
                            Environment_Data.objects.update_or_create(
                                key=key,
                                environment_id=environment_id,
                                defaults={
                                    'key': key,
                                    'value': processed_value,
                                    'variable_type': variable_type,
                                    'environment_id': environment_id,
                                    'type': '1'
                                }
                            )
                            logger.info(f"后置脚本更新环境变量: {key} = {value} (类型: {variable_type})")
                    
                    # 处理项目变量
                    project_vars = post_script_result.get('project', {})
                    if project_vars:
                        logger.info(f"更新项目变量 - 数量: {len(project_vars)}")
                        for key, value in project_vars.items():
                            variable_type, processed_value = detect_variable_type(value)
                            Public_Data.objects.update_or_create(
                                key=key,
                                project_id=project_id,
                                defaults={
                                    'key': key,
                                    'value': processed_value,
                                    'variable_type': variable_type,
                                    'project_id': project_id,
                                    'type': '1'
                                }
                            )
                            logger.info(f"后置脚本更新项目变量: {key} = {value} (类型: {variable_type})")
                
                analysis_result.append(analysis)
        
        total_time = time() - start_time  # 计算总时间
        # 修正：使用实际执行总次数和失败总次数来计算
        success_count = total_execution_count - total_fail_count
        fail_count = total_fail_count
        echart_data = {
            "success_count": success_count,
            "fail_count": fail_count
        }

        data = {
            "execution_result": analysis_result,
            "total_elapsed_time": round(total_time, 2),
            "api_elapsed_time": round(send_request_times, 2),
            "avg_api_elapsed_time": round(send_request_times / len(folder_ids), 2),
            "assertion_count": tests_count,
            "parent_scene_code": scene_code,
            "echart_data": echart_data,
            "report_data": None,
            "execution_time": start_time,
            "iterations": iterations
        }

        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            serializer.save()
        else:
            return Response(public_error_response(serializer.errors))
        return Response(public_success_response('执行成功'))


import json
from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from project.models import Public_Data, Environment_Data
from api_test.models import Test<PERSON><PERSON><PERSON>hildExecution, CaseTestsResult, TestCaseChild, Case_Tree_Folder, QuickAndCaseTempData, MultipartFile
from ...api_test_views.api_test_run.project.format_project_variable import format_request_data, build_context_from_vars
from ...api_test_views.api_test_run.project.action import ActionHandler
from ...api_test_views.api_test_run.project.api_assert import ApiAssert
from ...api_test_views.api_test_run.project.post_processors import ResponseProcessor
from ...api_test_views.api_test_run.project.script_executor import ScriptExecutor
from ...models import Case_Tree_Folder, QuickAndCaseTempData

import logging

logger = logging.getLogger('ats-console')

def detect_variable_type(value):
    """
    检测变量类型并返回对应的variable_type代码
    
    Returns:
        str: variable_type代码
        str: 处理后的值（用于存储）
    """
    if isinstance(value, bool):
        # 注意：bool检查要在int之前，因为bool是int的子类
        return '4', str(value).lower()  # 布尔值存储为 'true'/'false'
    elif isinstance(value, int):
        return '2', str(value)  # 整数
    elif isinstance(value, float):
        return '6', str(value)  # 浮点数
    elif isinstance(value, dict):
        return '5', json.dumps(value, ensure_ascii=False)  # 对象（JSON）
    elif isinstance(value, list):
        return '3', json.dumps(value, ensure_ascii=False)  # 数组（JSON）
    else:
        return '1', str(value)  # 字符串（默认）

class RunChildCaseApiBatchView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def post(self, request):
        project_id = request.data['project']
        parent_folder_id = request.data.get('parent_folder_id')
        
        logger.info(f"开始批量执行子用例 - 项目ID: {project_id}, 父文件夹ID: {parent_folder_id}")
        
        if not parent_folder_id:
            logger.error("缺少父文件夹ID参数")
            return Response(public_error_response("parent_folder_id 参数不能为空"))

        # 查找父目录下的所有子用例目录
        child_folders = Case_Tree_Folder.objects.filter(parent_id=parent_folder_id, type='2')
        
        if not child_folders.exists():
            logger.error(f"父文件夹 {parent_folder_id} 下未找到子用例")
            return Response(public_error_response("未找到子用例"))

        logger.info(f"找到 {child_folders.count()} 个子用例待执行")
        
        batch_results = []
        success_count = 0
        fail_count = 0

        for folder in child_folders:
            try:
                # 获取子用例
                api = TestCaseChild.objects.get(folder_id=folder.id)
                logger.info(f"开始执行子用例 - 文件夹ID: {folder.id}, 名称: {folder.name}, 用例名称: {api.case_name}")
                print("api", api.environment)
                
                # 执行单个子用例
                result = self._run_single_case(api, project_id, api.environment, folder.if_stream)
                
                if result['success']:
                    success_count += 1
                    logger.info(f"子用例执行成功 - 文件夹ID: {folder.id}, 名称: {folder.name}")
                else:
                    fail_count += 1
                    logger.error(f"子用例执行失败 - 文件夹ID: {folder.id}, 名称: {folder.name}, 错误: {result['message']}")
                    
                batch_results.append({
                    'folder_id': folder.id,
                    'folder_name': folder.name,
                    'case_name': api.case_name,
                    'case_code': api.case_code,
                    'success': result['success'],
                    'message': result['message'],
                    'response_data': result.get('response_data')
                })
                
            except TestCaseChild.DoesNotExist:
                fail_count += 1
                logger.error(f"目录 {folder.name} 下未找到子用例")
                batch_results.append({
                    'folder_id': folder.id,
                    'folder_name': folder.name,
                    'success': False,
                    'message': f"目录 {folder.name} 下未找到子用例"
                })
            except Exception as e:
                fail_count += 1
                logger.error(f"子用例执行异常 - 文件夹ID: {folder.id}, 名称: {folder.name}, 错误: {str(e)}")
                batch_results.append({
                    'folder_id': folder.id,
                    'folder_name': folder.name,
                    'success': False,
                    'message': f"执行失败: {str(e)}"
                })

        # 返回批量执行结果
        summary = {
            'total_count': len(child_folders),
            'success_count': success_count,
            'fail_count': fail_count,
            'results': batch_results
        }
        
        logger.info(f"批量执行完成 - 总计: {len(child_folders)}个, 成功: {success_count}个, 失败: {fail_count}个")

        return Response(public_success_response(summary))

    def _run_single_case(self, api, project_id, environment_id, if_stream):
        """
        执行单个子用例，复用原有逻辑
        """
        try:
            api_model_name = api.__class__.__name__
            api_id = api.id
            case_code = api.case_code
            api_name = api.api_name
            module_name = api.module_name
            case_name = api.case_name
            case_level = api.case_level
            request_url = api.request_url
            request_method = api.request_method
            request_headers = api.headers
            request_body = api.body
            request_params = api.params
            request_authorization = api.authorization
            request_pre_script = api.pre_script
            request_post_script = api.post_script
            request_tests = api.tests
            request_postfix = api.postfix
            request_content_type = api.content_type
            request_path = api.request_path
            execution_count = 1
            timeout = 20
            if_execution = 1
            
            logger.info(f"准备子用例数据 - ID: {api_id}, 名称: {case_name}, URL: {request_url}{request_path}")

            data = {
                "project_id": project_id,
                "environment_id": environment_id,
                "original_api_request_data": {
                    "api_id": api_id,
                    "case_code": case_code,
                    "case_name": case_name,
                    "api_name": api_name,
                    "case_level": case_level,
                    "module_name": module_name,
                    "request_url": request_url,
                    "request_path": request_path,
                    "request_method": request_method,
                    "headers": request_headers,
                    "body": request_body,
                    "params": request_params,
                    "authorization": request_authorization,
                    "pre_script": request_pre_script,
                    "post_script": request_post_script,
                    "tests": request_tests,
                    "postfix": request_postfix,
                    "content_type": request_content_type,
                    "execution": {
                        "execution_count": execution_count,
                        "timeout": timeout,
                        "if_execution": if_execution
                    },
                    "if_stream": if_stream,
                    "api_model_name": api_model_name
                }
            }

            # 处理multipart/form-data文件
            if data['original_api_request_data']['body'] and data['original_api_request_data']['content_type'] == 'multipart/form-data':
                logger.info(f"处理子用例的multipart/form-data文件 - 用例ID: {api_id}")
                for key, value in data['original_api_request_data']['body'].items():
                    if value == "#file#":
                        file_path = MultipartFile.objects.filter(file_key=key, project_id=data['project_id']).first().file_path
                        data['original_api_request_data']['body'][key] = '#file#' + file_path
                        logger.info(f"文件字段 {key} 关联到文件路径: {file_path}")

            logger.info(f"格式化子用例接口数据 - 用例ID: {api_id}")
            formatted_api, var_data = format_request_data(data)

            if formatted_api['execution']['if_execution'] == 1:
                logger.info(f"子用例设置为执行状态，开始发送请求 - 用例ID: {api_id}")
                quick_test_id = formatted_api['api_id']
                case_count = formatted_api['execution']['execution_count']
                
                for _ in range(1, case_count + 1):
                    logger.info(f"执行子用例第 {_} 次请求 - 用例ID: {api_id}")
                    
                    # 执行前置脚本
                    script_executor = ScriptExecutor()
                    
                    # 使用format_request_data返回的变量数据构建context，避免重复查询数据库
                    context = build_context_from_vars(var_data, api_id, api_model_name)
                    
                    logger.info(f"构建的context变量池: {list(context.keys())}")
                    pre_script_result = script_executor.execute_pre_script(formatted_api.get('pre_script', ''), context)
                    
                    # 如果前置脚本有返回值，处理环境变量和项目变量
                    if pre_script_result:
                        logger.info(f"前置脚本返回结果：{pre_script_result}")
                        
                        # 处理环境变量
                        env_vars = pre_script_result.get('env', {})
                        if env_vars and environment_id:
                            logger.info(f"更新环境变量 - 数量: {len(env_vars)}")
                            for key, value in env_vars.items():
                                variable_type, processed_value = detect_variable_type(value)
                                Environment_Data.objects.update_or_create(
                                    key=key,
                                    environment_id=environment_id,
                                    defaults={
                                        'key': key,
                                        'value': processed_value,
                                        'variable_type': variable_type,
                                        'environment_id': environment_id,
                                        'type': '1'
                                    }
                                )
                                logger.info(f"前置脚本更新环境变量: {key} = {value} (类型: {variable_type})")
                        
                        # 处理项目变量
                        project_vars = pre_script_result.get('project', {})
                        if project_vars:
                            logger.info(f"更新项目变量 - 数量: {len(project_vars)}")
                            for key, value in project_vars.items():
                                variable_type, processed_value = detect_variable_type(value)
                                Public_Data.objects.update_or_create(
                                    key=key,
                                    project_id=project_id,
                                    defaults={
                                        'key': key,
                                        'value': processed_value,
                                        'variable_type': variable_type,
                                        'project_id': project_id,
                                        'type': '1'
                                    }
                                )
                                logger.info(f"前置脚本更新项目变量: {key} = {value} (类型: {variable_type})")

                    handler = ActionHandler(formatted_api)
                    response = handler.send_request()
                    
                    if 'error_info' in response:
                        error_message = response['error_info']
                        logger.error(f"子用例请求处理失败 - 用例ID: {api_id}, 错误: {error_message}")
                        return {
                            'success': False,
                            'message': f'请求处理失败: {response["error_info"]}'
                        }
                    
                    response_text, postfix_status = response
                    logger.info(f"子用例请求成功 - 用例ID: {api_id}")
                    
                    # 保存响应到数据库
                    try:
                        logger.info(f"保存子用例响应数据到数据库 - 用例ID: {quick_test_id}")
                        executor = TestCaseChildExecution.objects.get(test_case_id=quick_test_id)
                        analysis = json.loads(response_text)
                        executor.analysis = analysis
                        executor.save()
                        logger.info(f"子用例响应数据保存成功 - 用例ID: {quick_test_id}")
                    except Exception as e:
                        logger.error(f"保存子用例响应到数据库失败 - 用例ID: {quick_test_id}, 错误: {str(e)}")
                        print(f'保存响应到数据库失败: {str(e)}')

                    # 处理断言
                    res_json_data_dict = analysis
                    tests_list = formatted_api['tests']
                    CaseTestsResult.objects.filter(api_case_id=api_id).delete()
                    
                    if tests_list:
                        logger.info(f"开始执行子用例断言 - 用例ID: {api_id}, 断言数量: {len(tests_list)}")
                        tests_result = ApiAssert(res_json_data_dict, tests_list).result_list
                        for test_result in tests_result:
                            CaseTestsResult.objects.create(api_case_id=api_id, **test_result)
                        
                        # 统计断言结果
                        pass_count = sum(1 for result in tests_result if result['status'] == '通过')
                        fail_count = sum(1 for result in tests_result if result['status'] == '失败')
                        logger.info(f"子用例断言结果统计 - 用例ID: {api_id}, 总计: {len(tests_result)}个, 通过: {pass_count}个, 失败: {fail_count}个")

                    # 处理后置提取
                    postfix_list = formatted_api['postfix']
                    if postfix_list and postfix_status:
                        logger.info(f"开始执行子用例后置提取 - 用例ID: {api_id}, 提取项数量: {len(postfix_list)}")
                        environment_id = data['environment_id']
                        project_id = data['project_id']
                        api_id_for_postfix = data['original_api_request_data']['api_id']
                        api_model_name_for_postfix = data['original_api_request_data']['api_model_name']
                        
                        postfix_obj = ResponseProcessor(res_json_data_dict, postfix_list, environment_id, project_id, api_id_for_postfix, api_model_name_for_postfix)

                        environment_postfix_data = postfix_obj.environment_result_list
                        project_postfix_data = postfix_obj.project_result_list
                        tmp_postfix_data = postfix_obj.tmp_result_list

                        if environment_postfix_data:
                            logger.info(f"更新子用例环境变量 - 用例ID: {api_id}, 数量: {len(environment_postfix_data)}")
                            for item in environment_postfix_data:
                                Environment_Data.objects.update_or_create(key=item['key'], defaults=item)
                        if project_postfix_data:
                            logger.info(f"更新子用例项目变量 - 用例ID: {api_id}, 数量: {len(project_postfix_data)}")
                            for item in project_postfix_data:
                                Public_Data.objects.update_or_create(key=item['key'], defaults=item)
                        if tmp_postfix_data:
                            logger.info(f"更新子用例临时变量 - 用例ID: {api_id}, 数量: {len(tmp_postfix_data)}")
                            for item in tmp_postfix_data:
                                QuickAndCaseTempData.objects.update_or_create(key=item['key'], defaults=item)
                        logger.info(f"子用例后置提取完成 - 用例ID: {api_id}")

                    # 执行后置脚本
                    post_script_result = script_executor.execute_post_script(
                        formatted_api.get('post_script', ''),
                        res_json_data_dict.get('res_json_data'),
                        res_json_data_dict.get('res_status_code'),
                        res_json_data_dict.get('res_headers', {}),
                        res_json_data_dict.get('res_url', ''),
                        context  # 传递context给后置脚本
                    )
                    
                    # 如果后置脚本有返回值，处理环境变量和项目变量
                    if post_script_result:
                        logger.info(f"后置脚本返回结果：{post_script_result}")
                        
                        # 处理环境变量
                        env_vars = post_script_result.get('env', {})
                        if env_vars and environment_id:
                            logger.info(f"更新环境变量 - 数量: {len(env_vars)}")
                            for key, value in env_vars.items():
                                variable_type, processed_value = detect_variable_type(value)
                                Environment_Data.objects.update_or_create(
                                    key=key,
                                    environment_id=environment_id,
                                    defaults={
                                        'key': key,
                                        'value': processed_value,
                                        'variable_type': variable_type,
                                        'environment_id': environment_id,
                                        'type': '1'
                                    }
                                )
                                logger.info(f"后置脚本更新环境变量: {key} = {value} (类型: {variable_type})")
                        
                        # 处理项目变量
                        project_vars = post_script_result.get('project', {})
                        if project_vars:
                            logger.info(f"更新项目变量 - 数量: {len(project_vars)}")
                            for key, value in project_vars.items():
                                variable_type, processed_value = detect_variable_type(value)
                                Public_Data.objects.update_or_create(
                                    key=key,
                                    project_id=project_id,
                                    defaults={
                                        'key': key,
                                        'value': processed_value,
                                        'variable_type': variable_type,
                                        'project_id': project_id,
                                        'type': '1'
                                    }
                                )
                                logger.info(f"后置脚本更新项目变量: {key} = {value} (类型: {variable_type})")

                    logger.info(f"子用例执行完成 - 用例ID: {api_id}")
                    return {
                        'success': True,
                        'message': '执行成功',
                        'response_data': analysis
                    }
            else:
                logger.warning(f"子用例设置为不执行状态，跳过请求 - 用例ID: {api_id}")
                return {
                    'success': False,
                    'message': '用例设置为不执行'
                }

        except Exception as e:
            logger.error(f"子用例执行异常: {str(e)}")
            return {
                'success': False,
                'message': f'执行异常: {str(e)}'
            }

from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from rest_framework.views import APIView
from rest_framework.response import Response
from ...models import TestCaseExecution, TestCase, TestCaseChildExecution, TestCaseChild
from ..serializers.case_response_serializer import CaseResponseSerializer
from ..serializers.child_case_response_serializer import ChildCaseResponseSerializer
import logging

logger = logging.getLogger('ats-console')

class CaseResponseView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    
    def get(self, request):
        folder_id = request.query_params.get('folder_id')
        query_type = request.query_params.get('query_type')
        if folder_id and query_type == '0':
            serializer_class = CaseResponseSerializer
            try:
                # 记录查询TestCase的开始
                logger.info(f"开始查询TestCase - folder_id: {folder_id}")
                api_id = TestCase.objects.get(folder_id=folder_id).id
                logger.info(f"成功查询到TestCase - api_id: {api_id}")
                queryset = TestCaseExecution.objects.get(test_case_id=api_id)
                logger.info(f"成功查询到TestCaseExecution - execution_id: {queryset.id}")
                
                serializer = serializer_class(queryset, many=False)
                logger.info(f"TestCase响应数据序列化成功 - folder_id: {folder_id}")
                return Response(public_success_response(serializer.data))
            except TestCase.DoesNotExist:
                logger.error(f"TestCase不存在 - folder_id: {folder_id}")
                return Response(public_error_response('该树节点下没有API'))
            except TestCaseExecution.DoesNotExist:
                logger.error(f"TestCaseExecution不存在 - folder_id: {folder_id}")
                return Response(public_error_response('该API没有执行记录'))
            except Exception as e:
                logger.error(f"查询TestCase响应数据时发生异常 - folder_id: {folder_id}, error: {str(e)}")
                return Response(public_error_response(f'查询响应数据失败: {str(e)}'))
                
        elif folder_id and query_type == '2':
            serializer_class = ChildCaseResponseSerializer
            try:
                # 记录查询TestCaseChild的开始
                logger.info(f"开始查询TestCaseChild - folder_id: {folder_id}")
                child_api_id = TestCaseChild.objects.get(folder_id=folder_id).id
                logger.info(f"成功查询到TestCaseChild - child_api_id: {child_api_id}")

                queryset = TestCaseChildExecution.objects.get(test_case_id=child_api_id)
                logger.info(f"成功查询到TestCaseChildExecution - execution_id: {queryset.id}")
                
                serializer = serializer_class(queryset, many=False)
                logger.info(f"TestCaseChild响应数据序列化成功 - folder_id: {folder_id}")
                return Response(public_success_response(serializer.data))
            except TestCaseChild.DoesNotExist:
                logger.error(f"TestCaseChild不存在 - folder_id: {folder_id}")
                return Response(public_error_response('该树节点下没有API'))
            except TestCaseChildExecution.DoesNotExist:
                logger.error(f"TestCaseChildExecution不存在 - folder_id: {folder_id}")
                return Response(public_error_response('该API没有执行记录'))
            except Exception as e:
                logger.error(f"查询TestCaseChild响应数据时发生异常 - folder_id: {folder_id}, error: {str(e)}")
                return Response(public_error_response(f'查询响应数据失败: {str(e)}'))
        else:
            logger.warning(f"参数不完整或无效 - folder_id: {folder_id}, query_type: {query_type}")
            return Response(public_error_response('Failed to get tree node'))

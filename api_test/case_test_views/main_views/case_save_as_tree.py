from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.response import Response
from ..serializers.case_tree_folder_serializer import CaseTreeFolderSerializer
from ...models import Case_Tree_Folder


class CaseTreeSaveAsView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = CaseTreeFolderSerializer

    def get_child_folder(self, item):
        # 匹配下级子菜单
        second_level_folders = Case_Tree_Folder.objects.filter(parent_id=item.id, type='1')

        # 继续递归匹配是否有多级子菜单
        children = [self.get_child_folder(child) for child in second_level_folders if child.type == '1']

        return {
            "id": item.id,
            "name": item.name,
            "type": item.type,
            "parent_id": item.parent_id,
            "if_stream": item.if_stream,
            "children": children or None
        }

    def get(self, request):
        # 需要项目id筛选查询
        project_id = request.query_params.get('project_id')
        # 获取顶级目录
        first_level_folder = Case_Tree_Folder.objects.get(project_id=project_id, name='顶级目录', parent=None)

        # 递归下级目录
        child_folder = self.get_child_folder(first_level_folder)

        return Response(public_success_response(child_folder))

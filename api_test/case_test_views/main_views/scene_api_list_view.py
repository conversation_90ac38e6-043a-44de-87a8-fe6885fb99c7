from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myauthentication import My<PERSON>uthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...models import TestCase

import logging

logger = logging.getLogger('ats-console')

class SceneApiListView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def post(self, request):
        scene_apis = request.data

        if not scene_apis:
            logger.error("scene_apis can not be empty")
            return Response(public_error_response('场景接口ID不能为空'), status=200)
        
        result = []
        for scene_api in scene_apis:
            try:
                api = TestCase.objects.get(folder_id=scene_api['id'])
                data = {
                    'api_id': api.id,
                    'folder_id': api.folder_id,
                    'case_code': api.case_code,
                    'api_name': api.api_name,
                    'request_path': api.request_path,
                    'environment': api.environment,
                    'request_url': api.request_url,
                    'request_method': api.request_method,
                    'content_type': api.content_type,
                    'checked': True
                }
                result.append(data)
            except TestCase.DoesNotExist:
                logger.error("TestCase.DoesNotExist")
                continue
        return Response(public_success_response(result))

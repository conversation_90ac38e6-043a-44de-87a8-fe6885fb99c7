from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.response import Response
from ..serializers.scene_api_info_serializer import SceneApiInfoSerializer
from ...models import SceneApiInfo, Scene_Tree_Folder
import logging

logger = logging.getLogger('ats-console')

class SceneApiSaveView(APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneApiInfoSerializer

    def post(self, request):
        data = request.data
        print(data)
        try:
            scene_api_info = SceneApiInfo.objects.get(scene_code=data['scene_code'])
            serializer = self.serializer_class(instance=scene_api_info, data=data)
        except SceneApiInfo.DoesNotExist:
            logger.error("SceneApiInfo.DoesNotExist")
            serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            serializer.save()
            Scene_Tree_Folder.objects.filter(scene_code=data['scene_code']).update(name=data['scene_name'])
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))
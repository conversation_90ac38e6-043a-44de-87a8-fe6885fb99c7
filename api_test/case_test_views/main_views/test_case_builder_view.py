import json, re
import requests
from openai import OpenAI
from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication

import logging

logger = logging.getLogger('ats-console')

# 基础断言生成提示词（生成少量核心断言）
prompt1 = """
你是一个专业的API测试用例生成器。根据以下接口响应数据，生成关键的测试断言用例。请根据实际情况生成3-6个核心的断言。

断言类型说明：
- obj: "1" = 响应码断言，"2" = 响应头断言，"3" = 响应体断言，"4" = JSONPath断言
- operator支持的操作：
  * 响应码: "等于", "不等于"
  * 响应头: "包含", "存在", "等于"
  * 响应体: "包含" (仅支持包含操作，不要使用等于)
  * JSONPath: "包含", "等于", "不等于", "大于", "小于", "大于等于", "小于等于", "存在", "类型等于"

重要提示：
1. JSONPath表达式必须以 $.开头，例如$.user.name这种格式或者$.orders[0].total_amount这种格式
2. 响应体断言(obj:"3")只能使用"包含"操作符，不要使用"等于"
3. JSONPath类型等于支持的类型: "string", "int", "object", "array", "boolean", "number", "NoneType"
4. 【关键】JSONPath断言(obj:"4")只能用于res_json_data中的数据，JSONPath表达式基于res_json_data的内容结构，不要包含res_json_data、res_headers、res_status_code等前缀
5. 【关键】如果要验证响应头信息，请使用响应头断言(obj:"2")，不要用JSONPath断言
6. 【关键】如果要验证状态码，请使用响应码断言(obj:"1")，不要用JSONPath断言
7. 【关键】大于、小于、大于等于、小于等于操作符只能用于数值型字段（int、number类型），不能用于字符串
8. 【断言命名规则】断言名称要简洁明了，不要包含res_json_data、res_headers等技术术语，直接描述验证的业务含义

数据结构说明：
- res_json_data: 实际的API响应体JSON数据，**仅JSONPath断言使用此数据**
- res_headers: API响应头信息，**仅响应头断言使用此数据**
- res_status_code: API响应状态码，**仅响应码断言使用此数据**

断言使用规则：
- 验证状态码 → 使用响应码断言(obj:"1")，data填写状态码值
- 验证响应头 → 使用响应头断言(obj:"2")，header_key填写头名称，data填写期望值
- 验证响应体JSON数据 → 使用JSONPath断言(obj:"4")，jsonpath基于res_json_data结构，如 $.headers.Host 而不是 $.res_json_data.headers.Host

断言命名示例：
- 好的命名：验证响应状态码为200、验证Content-Type为application/json、验证用户ID存在、验证订单金额大于0
- 坏的命名：验证res_json_data中的origin字段值、验证JSONPath $.res_headers.Server、断言res_status_code等于200

请基于响应数据生成测试用例，重点关注：
1. 响应状态码验证-必需生成一条
2. 关键响应头检查-必需生成一条
3. 重要数据字段的JSONPath断言-必需生成一条或多条
4. 其余字段根据实际情况生成

响应数据：
{api_data}

请严格按照以下JSON格式返回，不要添加任何其他内容：
{{
  "tests": [
    {{
      "name": "断言名称",
      "obj": "断言对象类型",
      "operator": "断言操作符",
      "header_key": "响应头键名(仅响应头断言需要)",
      "data": "断言值",
      "jsonpath": "JSONPath表达式(仅JSONPath断言需要，必须以$. 开头，jsonpath语法层级确保正确)"
    }}
  ]
}}
"""

# 详细断言生成提示词（生成更多全面的断言）
prompt2 = """
你是一个专业的API测试用例生成器。根据以下接口响应数据，生成全面的测试断言用例。请根据实际情况生成6-12个详细的断言。

断言类型说明：
- obj: "1" = 响应码断言，"2" = 响应头断言，"3" = 响应体断言，"4" = JSONPath断言
- operator支持的操作：
  * 响应码: "等于", "不等于"
  * 响应头: "包含", "存在", "等于"
  * 响应体: "包含" (仅支持包含操作，不要使用等于)
  * JSONPath: "包含", "等于", "不等于", "大于", "小于", "大于等于", "小于等于", "存在", "类型等于"

重要提示：
1. JSONPath表达式必须以 $.开头，例如：$.user.name, $.orders[0].total_amount
2. 响应体断言(obj:"3")只能使用"包含"操作符，不要使用"等于"
3. JSONPath类型等于支持的类型: "string", "int", "object", "array", "boolean", "number", "NoneType"
4. 【关键】JSONPath断言(obj:"4")只能用于res_json_data中的数据，JSONPath表达式基于res_json_data的内容结构，不要包含res_json_data、res_headers、res_status_code等前缀
5. 【关键】如果要验证响应头信息，请使用响应头断言(obj:"2")，不要用JSONPath断言
6. 【关键】如果要验证状态码，请使用响应码断言(obj:"1")，不要用JSONPath断言
7. 【关键】大于、小于、大于等于、小于等于操作符只能用于数值型字段（int、number类型），不能用于字符串
8. 【断言命名规则】断言名称要简洁明了，不要包含res_json_data、res_headers等技术术语，直接描述验证的业务含义

数据结构说明：
- res_json_data: 实际的API响应体JSON数据，**仅JSONPath断言使用此数据**
- res_headers: API响应头信息，**仅响应头断言使用此数据**
- res_status_code: API响应状态码，**仅响应码断言使用此数据**

断言使用规则：
- 验证状态码 → 使用响应码断言(obj:"1")，data填写状态码值
- 验证响应头 → 使用响应头断言(obj:"2")，header_key填写头名称，data填写期望值
- 验证响应体JSON数据 → 使用JSONPath断言(obj:"4")，jsonpath基于res_json_data结构，如 $.headers.Host 而不是 $.res_json_data.headers.Host

断言命名示例：
- 好的命名：验证响应状态码为200、验证Content-Type为application/json、验证用户ID存在、验证订单金额大于0
- 坏的命名：验证res_json_data中的origin字段值、验证JSONPath $.res_headers.Server、断言res_status_code等于200

请基于响应数据生成测试用例，全面覆盖：
1. 响应状态码验证（成功和异常状态）-必需生成一条
2. 关键响应头检查（Content-Type、字符编码等）-必需生成一条
3. 响应体关键字段验证（使用包含断言）-必需生成一条或两条
4. 数据类型验证（使用JSONPath）-必需生成一条或多条
5. 数值范围验证（使用JSONPath）-必需生成一条或多条
6. 数组和对象结构验证（使用JSONPath）-必需生成一条或多条
7. 字段存在性验证（使用JSONPath）-必需生成一条或多条
8. 其余字段根据实际情况生成

响应数据：
{api_data}

请严格按照以下JSON格式返回，不要添加任何其他内容：
{{
  "tests": [
    {{
      "name": "断言名称",
      "obj": "断言对象类型",
      "operator": "断言操作符",
      "header_key": "响应头键名(仅响应头断言需要)",
      "data": "断言值",
      "jsonpath": "JSONPath表达式(仅JSONPath断言需要，必须以$.开头)"
    }}
  ]
}}
"""


def get_llm_response(prompt):
    """
    调用大语言模型获取响应
    """
    try:
        logger.info("开始调用大语言模型生成测试用例")
        client = OpenAI(
            base_url='http://124.225.137.100:8204/v1/',
            api_key='sk-cacefa32dc1346049a7858abe5b623b2',
            timeout=120.0  # 设置120秒超时
        )
        response = client.chat.completions.create(
            model='Qwen3-32B-87',
            messages=[
                {
                    'role': 'system',
                    'content': '你是一个专业的API测试用例生成器，根据用户输入的接口响应信息，生成高质量的测试用例。请确保返回的JSON格式严格正确。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            stream=False,
            temperature=0.3,  # 降低随机性，提高一致性
            max_tokens=8192
        )
        
        llm_content = response.choices[0].message.content
        logger.info(f"大语言模型响应成功，响应长度: {len(llm_content)}")
        # logger.info(f"大语言模型响应: {llm_content}")
        return llm_content
        
    except requests.exceptions.Timeout:
        logger.error("大语言模型调用超时")
        return {"error": "timeout", "message": "大语言模型调用超时，请稍后重试"}
    except requests.exceptions.ConnectionError:
        logger.error("大语言模型服务连接失败")
        return {"error": "connection_error", "message": "大语言模型服务连接失败，请检查网络连接"}
    except requests.exceptions.RequestException as e:
        logger.error(f"大语言模型网络请求异常: {str(e)}")
        return {"error": "network_error", "message": f"网络请求异常: {str(e)}"}
    except Exception as e:
        logger.error(f"调用大语言模型失败: {str(e)}")
        return {"error": "unknown_error", "message": f"大语言模型调用失败: {str(e)}"}


def preprocess_js_expressions(content):
    try:
        # 移除单行注释，但确保不影响URL或其他正常内容
        lines = content.split('\n')
        processed_lines = []
        for line in lines:
            # 只处理包含'//'的行，且确保'//'不是URL的一部分
            if '//' in line and not ('http://' in line or 'https://' in line):
                line = line[:line.index('//')]
            processed_lines.append(line)
        content = '\n'.join(processed_lines)
        
        # 处理字符串重复表达式
        def replace_repeat(match):
            char = match.group(1)
            count = int(match.group(2))
            count = min(count, 1000)
            return json.dumps(char * count)
        
        content = re.sub(r'"([^"]*)"\.repeat\((\d+)\)', replace_repeat, content)
        
        return content
    except Exception as e:
        logger.error(f"预处理JSON数据时出错: {str(e)}")
        return content


def parse_llm_response(llm_response):
    try:
        logger.info("开始解析大语言模型响应")
        
        # 首先检查是否是markdown格式
        match = re.search(r'```json\n(.*?)\n```', llm_response, re.DOTALL)
        if match:
            json_str = match.group(1)
            logger.info("从markdown中提取JSON数据")
        else:
            # 尝试其他markdown格式
            match = re.search(r'```\n(.*?)\n```', llm_response, re.DOTALL)
            if match:
                json_str = match.group(1)
                logger.info("从通用代码块中提取JSON数据")
            else:
                json_str = llm_response
                logger.info("直接使用LLM响应作为JSON数据")
        
        # 预处理JavaScript表达式
        json_str = preprocess_js_expressions(json_str)
        
        # 确保JSON字符串是干净的
        json_str = json_str.strip()
        
        # 解析JSON
        try:
            result = json.loads(json_str)
            logger.info("JSON解析成功")
            
            # 验证结果格式
            if isinstance(result, dict) and 'tests' in result and isinstance(result['tests'], list):
                logger.info(f"成功解析测试用例数据，获得 {len(result['tests'])} 个测试用例")
                return result['tests']
            else:
                logger.error("解析的JSON数据格式不正确，缺少tests字段或格式错误")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            logger.error(f"JSON解析失败位置附近的内容: {json_str[max(0, e.pos-50):min(len(json_str), e.pos+50)]}")
            
            # 尝试更宽松的解析方式
            logger.info("尝试更宽松的JSON解析方式")
            
            # 尝试查找JSON对象
            start_idx = json_str.find('{')
            end_idx = json_str.rfind('}')
            
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_str_extracted = json_str[start_idx:end_idx + 1]
                try:
                    result = json.loads(json_str_extracted)
                    if isinstance(result, dict) and 'tests' in result and isinstance(result['tests'], list):
                        logger.info(f"从响应中提取JSON成功，获得 {len(result['tests'])} 个测试用例")
                        return result['tests']
                except json.JSONDecodeError:
                    pass
            
            # 如果还是失败，尝试正则表达式提取
            logger.info("尝试使用正则表达式提取JSON数据")
            json_pattern = r'\{[^{}]*"tests"\s*:\s*\[[^\]]*\][^{}]*\}'
            matches = re.findall(json_pattern, llm_response, re.DOTALL)
            
            for match in matches:
                try:
                    result = json.loads(match)
                    if isinstance(result, dict) and 'tests' in result and isinstance(result['tests'], list):
                        logger.info(f"通过正则表达式成功解析JSON，获得 {len(result['tests'])} 个测试用例")
                        return result['tests']
                except json.JSONDecodeError:
                    continue
            
            logger.error("所有JSON解析方法都失败了")
            return None
        
    except Exception as e:
        logger.error(f"解析大语言模型响应时发生异常: {str(e)}")
        return None


def validate_and_format_tests(tests_data):
    """
    验证和格式化测试用例数据
    """
    try:
        logger.info(f"开始验证和格式化 {len(tests_data)} 个测试用例")
        
        formatted_tests = []
        valid_obj_types = ['1', '2', '3', '4']
        
        for i, test in enumerate(tests_data):
            try:
                # 基本字段验证
                if not isinstance(test, dict):
                    logger.warning(f"测试用例 {i+1} 不是字典格式，跳过")
                    continue
                    
                # 必需字段检查
                required_fields = ['name', 'obj', 'operator']
                missing_fields = [field for field in required_fields if field not in test or not test[field]]
                if missing_fields:
                    logger.warning(f"测试用例 {i+1} 缺少必需字段: {missing_fields}，跳过")
                    continue
                
                # 验证obj类型
                if test['obj'] not in valid_obj_types:
                    logger.warning(f"测试用例 {i+1} obj类型无效: {test['obj']}，跳过")
                    continue
                
                # 业务逻辑验证
                obj_type = test['obj']
                operator = test['operator']
                
                # 响应体断言只允许"包含"操作
                if obj_type == '3' and operator != '包含':
                    logger.warning(f"测试用例 {i+1} 响应体断言只支持'包含'操作，当前操作符'{operator}'不被允许，跳过")
                    continue
                
                # JSONPath断言必须包含jsonpath字段且以$.开头
                if obj_type == '4':
                    jsonpath = test.get('jsonpath', '')
                    if not jsonpath:
                        logger.warning(f"测试用例 {i+1} JSONPath断言缺少jsonpath字段，跳过")
                        continue
                    if not jsonpath.startswith('$.'):
                        logger.warning(f"测试用例 {i+1} JSONPath表达式'{jsonpath}'必须以'$.'开头，跳过")
                        continue
                
                # 响应头断言必须包含header_key字段
                if obj_type == '2' and not test.get('header_key'):
                    logger.warning(f"测试用例 {i+1} 响应头断言缺少header_key字段，跳过")
                    continue
                
                # 格式化测试用例
                formatted_test = {
                    "name": str(test['name'])[:100],  # 限制长度
                    "obj": str(test['obj']),
                    "operator": str(test['operator']),
                    "header_key": str(test.get('header_key', ''))[:100] if test.get('header_key') else '',
                    "data": str(test.get('data', ''))[:1000] if test.get('data') else '',
                    "jsonpath": str(test.get('jsonpath', ''))[:100] if test.get('jsonpath') else ''
                }
                
                formatted_tests.append(formatted_test)
                logger.debug(f"测试用例 {i+1} 验证通过: {formatted_test['name']}")
                
            except Exception as e:
                logger.warning(f"验证测试用例 {i+1} 时发生异常: {str(e)}，跳过")
                continue
        
        logger.info(f"验证完成，有效测试用例数量: {len(formatted_tests)}")
        return formatted_tests
        
    except Exception as e:
        logger.error(f"验证和格式化测试用例时发生异常: {str(e)}")
        return []


class TestCaseBuilderView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def post(self, request):
        """
        生成测试用例接口
        """
        try:
            logger.info("收到测试用例生成请求")
            
            # 参数验证
            api_data = request.data.get('api_data')
            builder_type = request.data.get('builder_type')
            
            if not api_data:
                logger.warning("请求参数缺少api_data")
                return Response(public_error_response('请提供接口响应数据'))
            
            if not builder_type:
                logger.warning("请求参数缺少builder_type")
                return Response(public_error_response('请指定生成类型'))
            
            if builder_type not in ['1', '2']:
                logger.warning(f"无效的builder_type: {builder_type}")
                return Response(public_error_response('生成类型参数错误，请使用"1"或"2"'))
            
            logger.info(f"参数验证通过 - 生成类型: {builder_type}, 数据大小: {len(str(api_data))}")
            
            # 验证api_data结构
            if not self.validate_api_data_structure(api_data):
                logger.error("API数据结构验证失败")
                return Response(public_error_response('API响应数据结构不正确，请确保包含res_json_data、res_headers、res_status_code字段'))
            
            logger.info(f"API数据结构验证通过")
            
            # 选择提示词模板
            if builder_type == '1':
                prompt_template = prompt1
                logger.info("使用基础测试用例生成模式")
            else:
                prompt_template = prompt2
                logger.info("使用详细测试用例生成模式")
            
            # 构建完整提示词
            try:
                api_data_str = json.dumps(api_data, ensure_ascii=False, indent=2)
                full_prompt = prompt_template.format(api_data=api_data_str)
            except Exception as e:
                logger.error(f"构建提示词时发生异常: {str(e)}")
                return Response(public_error_response('处理接口数据时发生错误'))
            
            # 调用大语言模型
            llm_response = get_llm_response(full_prompt)
            if not llm_response:
                logger.error("大语言模型调用失败")
                return Response(public_error_response('测试用例生成服务暂时不可用，请稍后重试'))
            
            # 检查是否有错误信息
            if isinstance(llm_response, dict) and 'error' in llm_response:
                error_message = llm_response.get('message', '大语言模型调用失败')
                logger.error(f"大语言模型调用错误: {error_message}")
                return Response(public_error_response(error_message))
            
            # 解析响应
            tests_data = parse_llm_response(llm_response)
            if not tests_data:
                logger.error("解析大语言模型响应失败")
                return Response(public_error_response('测试用例生成失败，请检查接口数据格式'))
            
            # 验证和格式化
            formatted_tests = validate_and_format_tests(tests_data)
            if not formatted_tests:
                logger.error("没有生成有效的测试用例")
                return Response(public_error_response('未能生成有效的测试用例，请检查接口响应数据'))
            
            logger.info(f"测试用例生成成功，共生成 {len(formatted_tests)} 个测试用例")
            logger.info(f"测试用例: {formatted_tests}")
            
            # 构建响应数据
            response_data = {
                'tests': formatted_tests,
                'count': len(formatted_tests),
                'message': f'测试用例生成成功，共生成{len(formatted_tests)}个测试用例'
            }
            
            return Response(public_success_response(response_data))
            
        except Exception as e:
            logger.error(f"测试用例生成接口发生未捕获异常: {str(e)}")
            return Response(public_error_response('服务器内部错误，请稍后重试'))
    
    def validate_api_data_structure(self, api_data):
        """
        验证API数据结构
        """
        try:
            if not isinstance(api_data, dict):
                return False
            
            # 检查必需的字段
            required_fields = ['res_json_data', 'res_headers', 'res_status_code']
            for field in required_fields:
                if field not in api_data:
                    logger.warning(f"API数据缺少必需字段: {field}")
                    return False
            
            # 验证字段类型
            if not isinstance(api_data['res_headers'], dict):
                logger.warning("res_headers字段不是字典类型")
                return False
            
            if not isinstance(api_data['res_status_code'], int):
                logger.warning("res_status_code字段不是整数类型")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证API数据结构时发生异常: {str(e)}")
            return False
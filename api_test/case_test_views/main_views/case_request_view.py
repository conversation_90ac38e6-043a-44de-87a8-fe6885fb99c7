from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ..serializers.case_request_serializer import TestCaseRequestSerializer, TestCaseExecutionSerializer
from ...models import TestCase, TestCaseExecution
from .case_data_batch import handle_request_data, handle_request_sub_data
import logging

logger = logging.getLogger('ats-console')

class CaseRequestView(APIView, MyAuthentication):
    def initial(self, request, *args, **kwargs):
        if request.headers.get('x-api-key') == 'smart_api_test':
            pass
        else:
            super().initial(request, *args, **kwargs)
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = TestCaseRequestSerializer

    def get(self, request):
        apis = TestCase.objects.all().order_by('create_time')
        response_data = []

        for api in apis:
            api_data = self.serializer_class(api).data
            try:
                execution_data = TestCaseExecution.objects.get(test_case=api)
                execution_serializer = TestCaseExecutionSerializer(execution_data)
                api_data['execution'] = execution_serializer.data
            except TestCaseExecution.DoesNotExist:
                logger.error("TestCaseExecution.DoesNotExist")
                api_data['execution'] = {}  # 提供一个空字典作为默认值

            response_data.append(api_data)

        return Response(public_success_response(response_data))

    def post(self, request):
        orl_data = request.data
        # 如果是另存为用例接口那就认为全新增
        save_as_api_data = request.data
        if save_as_api_data.get('api_source') == 'save_as_case_api':
            params = save_as_api_data.get('params')
            for item in params:
                item['id'] = -1
            headers = save_as_api_data.get('headers')
            for item in headers:
                item['id'] = -1
            if isinstance(save_as_api_data.get('body'), list):
                body = save_as_api_data.get('body')
                for item in body:
                    item['id'] = -1
            postfix = save_as_api_data.get('postfix')
            for item in postfix:
                item['id'] = -1
            tests = save_as_api_data.get('tests')
            for item in tests:
                item['id'] = -1

        quick_test_data = {key: value for key, value in request.data.items() if
                           key not in ['execution_count', 'timeout', 'if_execution']}
        quick_test_data = handle_request_data(quick_test_data)
        execution_data = {key: value for key, value in request.data.items() if
                          key in ['execution_count', 'timeout', 'if_execution']}
        serializer = self.serializer_class(data=quick_test_data)
        if serializer.is_valid():
            try:
                quick_test = TestCase.objects.get(api_name=quick_test_data['api_name'])
                for key, value in serializer.validated_data.items():
                    setattr(quick_test, key, value)
                quick_test.save()
                logger.info(f"更新已存在的测试用例: {quick_test_data['api_name']}")
            except TestCase.DoesNotExist:
                logger.info(f"创建新的测试用例: {quick_test_data['api_name']}")
                quick_test = TestCase.objects.create(**serializer.validated_data)
                TestCaseExecution.objects.create(test_case=quick_test, **execution_data)
            handle_request_sub_data(orl_data)
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        else:
            logger.error(f"序列化器验证失败: {serializer.errors}")
            return Response(public_error_response(serializer.errors))


class CaseRequestDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = TestCaseRequestSerializer

    def put(self, request, pk):
        try:
            api = TestCase.objects.get(pk=pk)
            execution = TestCaseExecution.objects.get(test_case=api)
        except TestCase.DoesNotExist:
            logger.error("TestCase.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

        quick_test_data = {key: value for key, value in request.data.items() if key not in ['execution_count', 'timeout', 'if_execution']}
        execution_data = {key: value for key, value in request.data.items() if key in ['execution_count', 'timeout', 'if_execution']}

        serializer = self.serializer_class(instance=api, data=quick_test_data)
        if serializer.is_valid():
            serializer.save()
            for key, value in execution_data.items():
                setattr(execution, key, value)
            execution.save()
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        else:
            return Response(public_error_response(serializer.errors))

    def get(self, request, pk):
        try:
            api = TestCase.objects.get(pk=pk)
            serializer = self.serializer_class(api)
            api_data = serializer.data.copy()

            try:
                execution_data = TestCaseExecution.objects.get(test_case=api)
                execution_serializer = TestCaseExecutionSerializer(execution_data)
                api_data['execution'] = execution_serializer.data
            except TestCaseExecution.DoesNotExist:
                logger.error("TestCaseExecution.DoesNotExist")
                api_data['execution'] = {}  # 提供一个空字典作为默认值

            return Response(public_success_response(api_data))

        except TestCase.DoesNotExist:
            logger.error("TestCase.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, pk):
        try:
            api = TestCase.objects.get(pk=pk)
            api.delete()
            return Response(public_success_response('删除成功！'), status=status.HTTP_200_OK)
        except TestCase.DoesNotExist:
            logger.error("TestCase.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

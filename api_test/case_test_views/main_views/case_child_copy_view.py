from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_func.generate_random_string import generate_random_string
from ...models import (Case_Tree_Folder, TestCaseChild, TestCaseChildExecution,
                       TestCaseApiAuth, TestCaseApiBody, TestCaseApiExtract, TestCaseApiParams, TestCaseApiHeaders,
                       TestCaseApiAssertion)


def copy_case_api_data(new_case_tree_node_id, copied_case_id, new_case_tree_node_name):
    copied_case_api = TestCaseChild.objects.get(folder_id=copied_case_id)
    copied_api_dict = copied_case_api.__dict__
    copied_api_dict['folder_id'] = new_case_tree_node_id
    copied_api_dict['case_name'] = new_case_tree_node_name
    copied_api_dict['api_name'] = new_case_tree_node_name
    del copied_api_dict['_state']
    copied_api_id = copied_api_dict.pop('id')

    new_api = TestCaseChild.objects.create(**copied_api_dict)
    copied_case_api_execution = TestCaseChildExecution.objects.get(test_case_id=copied_api_id)
    copied_case_api_execution_dict = copied_case_api_execution.__dict__
    del copied_case_api_execution_dict['_state']
    del copied_case_api_execution_dict['id']
    del copied_case_api_execution_dict['analysis']
    copied_case_api_execution_dict['test_case_id'] = new_api.id
    TestCaseChildExecution.objects.create(**copied_case_api_execution_dict)

    all_auth = TestCaseApiAuth.objects.filter(api_case_id=copied_api_id)
    for auth in all_auth:
        auth_dict = auth.__dict__
        del auth_dict['_state']
        auth_dict['api_case_id'] = new_api.id
        del auth_dict['id']
        TestCaseApiAuth.objects.create(**auth_dict)

    all_body = TestCaseApiBody.objects.filter(api_case_id=copied_api_id)
    for body in all_body:
        body_dict = body.__dict__
        del body_dict['_state']
        body_dict['api_case_id'] = new_api.id
        del body_dict['id']
        TestCaseApiBody.objects.create(**body_dict)

    all_extract = TestCaseApiExtract.objects.filter(api_case_id=copied_api_id)
    for extract in all_extract:
        extract_dict = extract.__dict__
        del extract_dict['_state']
        extract_dict['api_case_id'] = new_api.id
        del extract_dict['id']
        TestCaseApiExtract.objects.create(**extract_dict)

    all_params = TestCaseApiParams.objects.filter(api_case_id=copied_api_id)
    for params in all_params:
        params_dict = params.__dict__
        del params_dict['_state']
        params_dict['api_case_id'] = new_api.id
        del params_dict['id']
        TestCaseApiParams.objects.create(**params_dict)

    all_headers = TestCaseApiHeaders.objects.filter(api_case_id=copied_api_id)
    for headers in all_headers:
        headers_dict = headers.__dict__
        del headers_dict['_state']
        headers_dict['api_case_id'] = new_api.id
        del headers_dict['id']
        TestCaseApiHeaders.objects.create(**headers_dict)

    all_assertion = TestCaseApiAssertion.objects.filter(api_case_id=copied_api_id)
    for assertion in all_assertion:
        assertion_dict = assertion.__dict__
        del assertion_dict['_state']
        assertion_dict['api_case_id'] = new_api.id
        del assertion_dict['id']
        TestCaseApiAssertion.objects.create(**assertion_dict)


class CaseCopyView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def post(self, request):
        try:
            copied_case_id = request.data.get('case_id')
            copied_case = Case_Tree_Folder.objects.get(id=copied_case_id)
            copied_case_dict = copied_case.__dict__
            del copied_case_dict['_state']
            del copied_case_dict['id']
            copied_case_dict['name'] = copied_case_dict['name'] + '-' + str(copied_case_id) + generate_random_string(5)
            new_case_tree_node = Case_Tree_Folder.objects.create(**copied_case_dict)
            new_case_tree_node_id = new_case_tree_node.id
            new_case_tree_node_name = new_case_tree_node.name
            copy_case_api_data(new_case_tree_node_id, copied_case_id, new_case_tree_node_name)
            return Response(public_success_response(f'{new_case_tree_node.name}复制成功'))
        except Exception as e:
            return Response(public_error_response(f'复制失败，错误信息：{e}'))


from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ...models import SceneApiExecutionResult
from ...case_test_views.serializers.scene_api_execution_result_serializer import SceneApiExecutionResultSerializer
import logging

logger = logging.getLogger('ats-console')

class SceneResultView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneApiExecutionResultSerializer
    
    def get(self, request):
        try:
            scene_code = request.query_params.get('scene_code')
            result = SceneApiExecutionResult.objects.get(parent_scene_code=scene_code)
            serializer = self.serializer_class(instance=result, many=False)
            return Response(public_success_response(serializer.data))
        except Exception as e:
            logger.error("SceneApiExecutionResult.DoesNotExist")
            return Response(public_error_response(str(e)))
from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...models import TestCase, SceneApiInfo, SceneApiExecutionResult
from ...case_test_views.serializers.scene_api_execution_result_serializer import SceneApiExecutionResultSerializer
from project.models import Environment
from project.project_views.serializers.environment_serializer import EnvironmentSerializer
import logging

logger = logging.getLogger('ats-console')

class SceneInfoAndApiResultView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneApiExecutionResultSerializer
    def get(self, request):
        scene_code = request.query_params.get('scene_code')

        if not scene_code:
            logger.error("scene_code can not be empty")
            return Response(public_error_response('场景编码不能为空'), status=200)
        try:
            scene_api_result = SceneApiExecutionResult.objects.get(parent_scene_code=scene_code)
        except SceneApiExecutionResult.DoesNotExist:
            logger.warning("SceneApiExecutionResult.DoesNotExist")
            scene_api_result_data = None
        else:
            serializer = self.serializer_class(instance=scene_api_result, many=False)
            scene_api_result_data = serializer.data
        scene_api_info = SceneApiInfo.objects.get(scene_code=scene_code)
        api_info = []
        for scene_api in scene_api_info.folder_id_of_api:
            try:
                api = TestCase.objects.get(folder_id=scene_api)
                data = {
                    'api_id': api.id,
                    'folder_id': api.folder_id,
                    'case_code': api.case_code,
                    'api_name': api.api_name,
                    'request_path': api.request_path,
                    'environment': api.environment,
                    'request_url': api.request_url,
                    'request_method': api.request_method,
                    'content_type': api.content_type,
                    'checked': True
                }
                api_info.append(data)
            except TestCase.DoesNotExist:
                logger.warning("TestCase.DoesNotExist continue")
                continue
        environment = Environment.objects.all().filter(project_id=scene_api_info.project_id).order_by('create_time')
        env_serializer = EnvironmentSerializer(instance=environment, many=True)
        env_data = env_serializer.data
        result = {
            'scene_api_info': api_info,
            'scene_api_result': scene_api_result_data,
            'env_list': env_data
        }
        return Response(public_success_response(result))

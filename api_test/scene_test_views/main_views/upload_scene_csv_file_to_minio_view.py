import csv
from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...api_test_views.api_test_run.minio_test.minio_oper import create_minio_client, create_bucket
from django.conf import settings
from ...models import SceneCsvFile, SceneApiInfo
from ..serializers.api_csv_serializer import SceneCsvFileSerializer

# 创建 MinIO 客户端
client = create_minio_client(
    settings.MINIO_ENDPOINT,
    settings.MINIO_ACCESS_KEY,
    settings.MINIO_SECRET_KEY
)

class ApiTestDataFileUploadView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneCsvFileSerializer
    bucket_name = 'api-test-data'

    # def read_csv_to_dict(self, file_obj):
    #     # 使用 csv.DictReader 从上传的文件对象中读取数据
    #     file_obj.seek(0)  # 确保从文件的开始处读取
    #     try:
    #         # 首先尝试 utf-8-sig 编码
    #         reader = csv.DictReader(file_obj.read().decode('utf-8-sig').splitlines())
    #     except UnicodeDecodeError:
    #         # 如果 utf-8-sig 失败，尝试使用 gbk 编码
    #         file_obj.seek(0)  # 重置文件指针
    #         reader = csv.DictReader(file_obj.read().decode('gbk').splitlines())
    #     return list(reader)
    def read_csv_to_dict(self, file_obj):
        # 使用 csv.DictReader 从上传的文件对象中读取数据
        file_obj.seek(0)  # 确保从文件的开始处读取
        encodings = ['utf-8-sig', 'gbk', 'utf-8', 'latin1']  # 添加更多可能的编码
        for encoding in encodings:
            try:
                file_obj.seek(0)  # 每次尝试前重置文件指针
                return list(csv.DictReader(file_obj.read().decode(encoding).splitlines()))
            except UnicodeDecodeError:
                continue
        raise Exception("无法解码文件，请检查文件编码格式")

    def post(self, request):
        file_type = request.data.get('file_type')
        parent_scene_code = request.data.get('parent_scene_code')
        if not file_type or not parent_scene_code:
            return Response(public_error_response('文件类型和场景编码不能为空'))
        try:
            file_obj = request.FILES['file']
            if file_obj.name.split('.')[-1] != 'csv':
                return Response(public_error_response(f"文件类型不正确, 请上传csv文件"))

        except Exception as exc:
            return Response(public_error_response(f"文件不存在: {exc}"))
        
        # 读取 CSV 文件并转换为字典
        try:
            csv_data = self.read_csv_to_dict(file_obj)
            print("csv_data", csv_data)  # 打印或处理字典数据
        except Exception as exc:
            return Response(public_error_response(f"文件解析失败: {exc}"))
        
        data = {
            "file_name": parent_scene_code + '_' + file_obj.name,
            "file_type": file_type,
            "file_size": file_obj.size,
            "file_path": None,
            "file_bucket": self.bucket_name,
            "parent_scene_code": parent_scene_code,
            "file_content": []
        }

        data['file_content'] = csv_data
        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            SceneCsvFile.objects.filter(parent_scene_code=data['parent_scene_code']).delete()
            serializer.save()
        else:
            return Response(public_error_response(f"保存失败: {serializer.errors}"))

        # 重置文件指针
        file_obj.seek(0)

        create_bucket(client, self.bucket_name)

        # 上传文件到 MinIO
        try:
            object_name = data['file_name']
            # 从内存中直接上传数据流到 MinIO 存储桶
            client.put_object(self.bucket_name, object_name, file_obj, file_obj.size)
            return Response(public_success_response(f"上传成功: {object_name}"))
        except Exception as exc:
            return Response(public_error_response(f"上传失败: {exc}"))

    def delete(self, request):
        parent_scene_code = request.query_params.get('parent_scene_code')
        object_name = request.query_params.get('object_name')
        client.remove_object(self.bucket_name, object_name)
        SceneCsvFile.objects.filter(parent_scene_code=parent_scene_code).delete()
        SceneApiInfo.objects.filter(scene_code=parent_scene_code).update(var_file=[])
        return Response(public_success_response("删除成功"))

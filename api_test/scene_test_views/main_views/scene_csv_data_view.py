from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...models import SceneCsvFile
from ..serializers.api_csv_serializer import SceneCsvFileSerializer

class SceneCsvDataView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = SceneCsvFileSerializer

    def get(self, request):
        scene_code = request.query_params.get('scene_code')
        if not scene_code:
            return Response(public_error_response('场景编码不能为空'))
        try:
            scene_csv_content_list = SceneCsvFile.objects.all().filter(parent_scene_code=scene_code).first().file_content
        except:
            return Response(public_error_response('场景csv文件不存在'))
        return Response(public_success_response(scene_csv_content_list))
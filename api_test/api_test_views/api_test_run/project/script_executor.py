import json
import requests
import time
import random
import logging
import textwrap
import ast
import math
import re
from datetime import datetime
from typing import Dict, Any, Optional

logger = logging.getLogger('ats-console')


class ScriptExecutor:
    """
    脚本执行器，用于安全执行前置和后置脚本
    类似于Postman的前后脚本执行逻辑
    """
    
    def __init__(self):
        self.allowed_modules = {
            'json': json,
            'requests': requests,
            'time': time,
            'random': random,
            'datetime': datetime,
            'math': math,
            're': re
        }
    
    def _clean_script(self, script_content: str) -> str:
        """
        清理脚本内容，修复常见的格式问题
        """
        if not script_content or not script_content.strip():
            return ""
        
        try:
            # 首先尝试使用dedent清理
            cleaned = textwrap.dedent(script_content).strip()
            
            # 尝试解析AST来验证语法
            try:
                ast.parse(cleaned)
                return cleaned
            except SyntaxError:
                # 如果AST解析失败，尝试手动修复
                return self._manual_fix_script(script_content)
                
        except Exception as e:
            logger.warning(f"脚本清理失败，使用原始内容: {str(e)}")
            return script_content
    
    def _manual_fix_script(self, script_content: str) -> str:
        """
        手动修复脚本的常见问题
        """
        try:
            lines = script_content.strip().split('\n')
            fixed_lines = []
            
            # 移除所有行前的空白，然后重新添加正确的缩进
            clean_lines = [line.strip() for line in lines if line.strip()]
            
            for line in clean_lines:
                if line.startswith('def '):
                    # 函数定义不需要缩进
                    fixed_lines.append(line)
                elif line and not line.startswith('#'):
                    # 其他非注释行都需要缩进
                    fixed_lines.append('    ' + line)
                else:
                    # 注释行保持原样
                    fixed_lines.append(line)
            
            return '\n'.join(fixed_lines)
            
        except Exception as e:
            logger.error(f"手动修复脚本失败: {str(e)}")
            return script_content
    
    def execute_pre_script(self, script_content: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行前置脚本
        
        Args:
            script_content: 前置脚本内容，应包含ats_script(context)函数
            context: 当前变量池，包括项目全局变量、环境变量等
            
        Returns:
            dict: 脚本执行返回的结果，格式为{"env": {...}, "project": {...}}
        """
        if not script_content or not script_content.strip():
            logger.info("前置脚本为空，跳过执行")
            return {}
            
        if context is None:
            context = {}
            
        try:
            logger.info("开始执行前置脚本")
            
            # 清理脚本内容
            cleaned_script = self._clean_script(script_content)
            logger.debug(f"清理后的前置脚本:\n{cleaned_script}")
            
            # 创建安全的执行环境
            script_globals = {
                '__builtins__': {
                    'print': print,
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'dict': dict,
                    'list': list,
                    'tuple': tuple,
                    'type': type,
                    'isinstance': isinstance,
                    'range': range,
                    'enumerate': enumerate
                    # '__import__': __import__,  # 添加import支持
                },
                **self.allowed_modules
            }
            
            script_locals = {}
            
            # 执行脚本
            exec(cleaned_script, script_globals, script_locals)
            
            # 调用ats_script函数
            if 'ats_script' in script_locals:
                # 传递context参数给前置脚本
                result = script_locals['ats_script'](context)
                if result is None:
                    result = {}
                elif not isinstance(result, dict):
                    logger.warning(f"前置脚本返回值类型错误，期望dict，实际：{type(result)}")
                    result = {}
                
                # 验证返回值格式
                if result and not all(key in ['env', 'project'] for key in result.keys() if result[key]):
                    logger.warning(f"前置脚本返回值格式建议使用 {{'env': {{...}}, 'project': {{...}}}} 格式")
                
                logger.info(f"前置脚本执行成功，返回结果：{result}")
                return result
            else:
                logger.warning("前置脚本中未找到ats_script函数")
                return {}
                
        except SyntaxError as e:
            logger.error(f"前置脚本语法错误: {str(e)}, 行号: {getattr(e, 'lineno', '未知')}")
            logger.debug(f"原始脚本内容:\n{script_content}")
            return {}
        except Exception as e:
            logger.error(f"前置脚本执行失败：{str(e)}")
            logger.debug(f"原始脚本内容:\n{script_content}")
            return {}
    
    def execute_post_script(self, script_content: str, response_data: Any, 
                          status_code: int, headers: Dict[str, str], url: str = "", context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行后置脚本
        
        Args:
            script_content: 后置脚本内容，应包含ats_script(res_json_data, res_url, res_headers, res_status_code, context)函数
            response_data: 接口响应数据
            status_code: HTTP状态码
            headers: 响应头信息
            url: 请求URL
            context: 当前变量池，包括项目全局变量、环境变量等
            
        Returns:
            dict: 脚本执行返回的结果，格式为{"env": {...}, "project": {...}}
        """
        if not script_content or not script_content.strip():
            logger.info("后置脚本为空，跳过执行")
            return {}
            
        if context is None:
            context = {}
            
        try:
            logger.info("开始执行后置脚本")
            
            # 清理脚本内容
            cleaned_script = self._clean_script(script_content)
            logger.debug(f"清理后的后置脚本:\n{cleaned_script}")
            
            # 创建安全的执行环境
            script_globals = {
                '__builtins__': {
                    'print': print,
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'dict': dict,
                    'list': list,
                    'tuple': tuple,
                    'type': type,
                    'isinstance': isinstance,
                    'range': range,
                    'enumerate': enumerate
                    # '__import__': __import__,  # 添加import支持
                },
                **self.allowed_modules
            }
            
            script_locals = {}
            
            # 执行脚本
            exec(cleaned_script, script_globals, script_locals)
            
            # 调用ats_script函数
            if 'ats_script' in script_locals:
                # 传递新的参数格式：res_json_data, res_url, res_headers, res_status_code, context
                result = script_locals['ats_script'](response_data, url, headers, status_code, context)
                if result is None:
                    result = {}
                elif not isinstance(result, dict):
                    logger.warning(f"后置脚本返回值类型错误，期望dict，实际：{type(result)}")
                    result = {}
                
                # 验证返回值格式
                if result and not all(key in ['env', 'project'] for key in result.keys() if result[key]):
                    logger.warning(f"后置脚本返回值格式建议使用 {{'env': {{...}}, 'project': {{...}}}} 格式")
                
                logger.info(f"后置脚本执行成功，返回结果：{result}")
                return result
            else:
                logger.warning("后置脚本中未找到ats_script函数")
                return {}
                
        except SyntaxError as e:
            logger.error(f"后置脚本语法错误: {str(e)}, 行号: {getattr(e, 'lineno', '未知')}")
            logger.debug(f"原始脚本内容:\n{script_content}")
            return {}
        except Exception as e:
            logger.error(f"后置脚本执行失败：{str(e)}")
            logger.debug(f"原始脚本内容:\n{script_content}")
            return {} 
import requests
import json
import datetime

import logging

logger = logging.getLogger('ats-console')


def transform_dict(input_dict):
    for key, value in input_dict.items():
        # 确保value是字符串类型，才能调用startswith方法
        if isinstance(value, str) and value.startswith('#file#'):
            input_dict[key] = open(value[6:], 'rb')
        else:
            input_dict[key] = ('', value)
    return input_dict
    # return {key: ('', value) for key, value in input_dict.items()}

def process_stream_response(r, start_time):
    original_message = ""
    final_message = ""
    stream_data_size = 0
    
    logger.info(f"处理流式响应，状态码: {r.status_code}")

    # 处理每一行数据
    for line in r.iter_lines(decode_unicode=True):  # 确保自动解码
        if line:
            original_message += line + "\n"  # 保持原始消息格式
            # 处理并转换消息
            final_message += line.replace("data: ", "").replace("<br />", "\n")
    # 计算数据大小
    stream_data_size = len(final_message.encode('utf-8'))
    end_time = datetime.datetime.now()
    stream_elapsed = int((end_time - start_time).total_seconds() * 1000)
    
    logger.info(f"流式响应处理完成，数据大小: {stream_data_size} 字节，耗时: {stream_elapsed}ms")

    # 调用process_response来处理和返回响应数据
    return process_response(r, original_message, final_message, stream_data_size, stream_elapsed)


# 接口请求
def process_response(r, original_message="", final_message="", stream_data_size=0, stream_elapsed=0):
    all_res_data = {}
    try:
        if not original_message:  # 如果没有原始消息，则尝试读取r.text
            all_res_data['res_text_data'] = r.text
            try:
                all_res_data['res_json_data'] = json.loads(r.text)
                logger.info(f"响应成功解析为JSON")
            except json.JSONDecodeError:
                all_res_data['res_json_data'] = r.text
                logger.info(f"响应不是有效的JSON格式，使用原始文本")
        if original_message:
            all_res_data['res_original_stream_data'] = original_message
        if final_message:
            all_res_data['res_text_data'] = final_message
            all_res_data['res_json_data'] = {'stream_data': final_message}

        all_res_data['res_url'] = r.url
        all_res_data['res_headers'] = dict(r.headers)
        all_res_data['res_cookies'] = r.cookies.get_dict()
        all_res_data['res_content'] = r.content.decode('utf-8') if not original_message else ''
        all_res_data['res_encoding'] = r.encoding
        all_res_data['res_elapsed'] = int(r.elapsed.total_seconds() * 1000) if not stream_elapsed else stream_elapsed
        all_res_data['res_status_code'] = r.status_code
        all_res_data['response_size'] = {}
        all_res_data['response_size']['body_size'] = int(all_res_data['res_headers'].get('Content-Length', 0))
        if stream_data_size:
            all_res_data['response_size']['body_size'] = stream_data_size
        all_res_data['response_size']['headers_size'] = len(str(r.headers).encode())
        all_res_data['response_size']['total'] = all_res_data['response_size']['body_size'] + \
                                                 all_res_data['response_size']['headers_size']
        all_res_data['json_postfix'] = True
        
        logger.info(f"响应处理完成 - 状态码: {r.status_code}, 响应大小: {all_res_data['response_size']['total']} 字节, 耗时: {all_res_data['res_elapsed']}ms")
    except Exception as e:
        logger.error(str(e))
        all_res_data['json_postfix'] = False
        all_res_data['res_json_data'] = None
        all_res_data['error_message'] = str(e)

    return json.dumps(all_res_data, ensure_ascii=False), all_res_data['json_postfix']


class ActionHandler:
    def __init__(self, api):
        self.case_code = api['case_code']
        self.module_name = api.get('module_name', 'null_module_name')
        self.case_name = api.get('case_name', 'null_case_name')
        self.api_name = api.get('api_name', 'null_api_name')
        self.case_level = api.get('case_level', 'null_case_level')
        self.request_method = api.get('request_method', 'GET')
        self.request_url = api.get('request_url', '')
        self.request_path = api.get('request_path', '')
        self.request_params = api.get('params', {})
        self.request_headers = api.get('headers', {})
        self.request_body = api.get('body', {})
        # self.assertion_code = api.get('tests', {}).get('status_code')
        # self.assertion_content = api.get('tests', {}).get('string_inclusion')
        self.assertion_code = api.get('tests', {})
        self.assertion_content = api.get('tests', {})
        self.postfix = api.get('postfix')
        self.timeout = api.get('execution', {}).get('timeout')

        self.url = self.request_url + self.request_path

        self.if_stream = api.get('if_stream')

        # 打印所有参数

        # print('-----------------')
        # print(self.case_code)
        # print(self.module_name)
        # print(self.case_name)
        # print(self.api_name)
        # print(self.case_level)
        # print(self.request_method)
        # print(self.request_url)
        # print(self.request_path)
        # print(self.request_params)
        # print(self.request_headers)
        # print(self.request_body)
        # print(self.assertion_code)
        # print(self.assertion_content)
        # print(self.postfix)
        # print(self.timeout)
        # print(self.if_stream)
        # print('-----------------')

    # @allure.step('接口信息')
    def send_request(self):
        try:
            start_time = datetime.datetime.now()
            logger.info(f"开始发送请求 - 用例: {self.case_name}, API: {self.api_name}, 方法: {self.request_method}, URL: {self.url}")
            logger.info(f"请求参数 - params: {json.dumps(self.request_params, ensure_ascii=False)}")
            
            # 记录请求体，但避免记录过大的内容
            if isinstance(self.request_body, dict):
                logger.info(f"请求体: {json.dumps(self.request_body, ensure_ascii=False)}")
                
            if self.request_headers.get('Content-Type') and (
                    'application/json' in self.request_headers['Content-Type'] or 'none' in self.request_headers[
                'Content-Type']):
                logger.info(f"Content-Type: {self.request_headers.get('Content-Type')}, 使用JSON请求")
                r = requests.request(self.request_method, self.url, params=self.request_params,
                                    headers=self.request_headers, json=self.request_body, timeout=self.timeout, stream=self.if_stream)
                if self.if_stream:
                    return process_stream_response(r, start_time)
            elif self.request_headers.get('Content-Type') and 'x-www-form-urlencoded' in self.request_headers[
                'Content-Type']:
                logger.info(f"Content-Type: {self.request_headers.get('Content-Type')}, 使用form-urlencoded请求")
                r = requests.request(self.request_method, self.url, params=self.request_params,
                                    headers=self.request_headers, data=self.request_body, timeout=self.timeout, stream=self.if_stream)
                if self.if_stream:
                    return process_stream_response(r, start_time)
            elif self.request_headers.get('Content-Type') and 'multipart/form-data' in self.request_headers['Content-Type']:
                logger.info(f"Content-Type: {self.request_headers.get('Content-Type')}, 使用multipart/form-data请求")
                data = transform_dict(self.request_body)
                self.request_headers.pop('Content-Type')
                r = requests.request(self.request_method, self.url, params=self.request_params,
                                    headers=self.request_headers, files=data, timeout=self.timeout, stream=self.if_stream)
                if self.if_stream:
                    return process_stream_response(r, start_time)

            else:
                logger.error(f"不支持的Content-Type: {self.request_headers.get('Content-Type')} 或缺少Content-Type")
                return {"error_info": "Unsupported Content-Type or missing Content-Type"}
            
            logger.info(f"请求已发送，状态码: {r.status_code}, 响应时间: {r.elapsed.total_seconds() * 1000}ms")
            return process_response(r)
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            return {"error_info": str(e)}

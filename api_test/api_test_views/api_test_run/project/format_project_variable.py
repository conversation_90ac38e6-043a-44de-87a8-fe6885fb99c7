import json, base64
from faker import Faker
from datetime import datetime, timedelta
from project.models import Environment_Auth, Environment_Data, Public_Data
from project.project_views.utils.database_utils import DatabaseConnectionManager

import logging

class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

def convert_datetime_to_string(data):
    """递归地将数据中的datetime对象转换为字符串"""
    if isinstance(data, datetime):
        return data.isoformat()
    elif isinstance(data, dict):
        return {key: convert_datetime_to_string(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_datetime_to_string(item) for item in data]
    else:
        return data

logger = logging.getLogger('ats-console')

fake = Faker(locale='zh-CN')
faker_data_method_dict = {
    "{{$uuid4}}": fake.uuid4,
    "{{$address}}": fake.address,
    "{{$email}}": fake.email,
    "{{$phone_number}}": fake.phone_number,
    "{{$ssn}}": fake.ssn,
    "{{$user_name}}": fake.user_name,
    "{{$password}}": fake.password,
    "{{$ipv4}}": fake.ipv4,
    "{{$url}}": fake.url,
    "{{$mac_address}}": fake.mac_address,
    "{{$random_str8}}": lambda: fake.lexify(text='????????', letters='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'),
    "{{$today}}": lambda: datetime.now().strftime('%Y%m%d'),
    "{{$yesterday}}": lambda: (datetime.now() - timedelta(days=1)).strftime('%Y%m%d'),
    "{{$tomorrow}}": lambda: (datetime.now() + timedelta(days=1)).strftime('%Y%m%d')
}

def generate_faker_data(faker_data_method):
    if faker_data_method == 'uuid4':
        return fake.uuid4()

def replace_placeholders(data, replacements):
    # logger.info(f"开始替换占位符，替换字典: {json.dumps(replacements, ensure_ascii=False, default=str)}")
    if isinstance(data, dict):
        result = {}
        for k, v in data.items():
            replaced_value = replace_placeholders(v, replacements)
            if isinstance(v, str) and isinstance(replaced_value, str) and v != replaced_value:
                logger.info(f"字典键 '{k}' 的值从 '{v}' 替换为 '{replaced_value}'")
            result[k] = replaced_value
        return result
    elif isinstance(data, list):
        result = []
        for i, item in enumerate(data):
            replaced_item = replace_placeholders(item, replacements)
            if isinstance(item, str) and isinstance(replaced_item, str) and item != replaced_item:
                logger.info(f"列表索引 {i} 的值从 '{item}' 替换为 '{replaced_item}'")
            result.append(replaced_item)
        return result
    elif isinstance(data, str):
        original_data = data
        # 检查字符串中是否包含占位符，并尝试替换
        for key, value in replacements.items():
            placeholder = f'{{{{{key}}}}}'  # 创建占位符格式，例如 {{obj}}
            if placeholder in data:
                # 如果字符串完全等于占位符，则返回原始类型的值
                if data.strip() == placeholder:
                    logger.info(f"完全匹配占位符 '{placeholder}'，替换为原始类型值: {value}")
                    return value
                # 如果不完全匹配，替换为字符串形式
                data = data.replace(placeholder, str(value))
                logger.info(f"部分匹配占位符 '{placeholder}'，替换后的字符串: '{data}'")
        
        if original_data != data:
            logger.info(f"字符串从 '{original_data}' 替换为 '{data}'")
        return data
    else:
        return data
    
def get_sql_result(sql_config_str):
    """
    执行SQL查询并获取结果
    Args:
        sql_config_str: SQL配置字符串，格式为 {"sql":"select id from data_source where name = '测试MySQL数据源1'","data_source_id":2}
    Returns:
        SQL查询结果，基础类型直接返回，复杂类型返回Python对象（list/dict）
    """
    try:
        sql_config = json.loads(sql_config_str)
        sql = sql_config.get('sql', '').strip()
        data_source_id = sql_config.get('data_source_id')
        if not sql:
            raise ValueError("SQL语句不能为空")
        if not data_source_id:
            raise ValueError("数据源ID不能为空")
        if not sql.strip().upper().startswith('SELECT'):
            raise ValueError("为了安全考虑，当前只支持SELECT查询语句")
        logger.info(f"执行SQL查询: {sql}, 数据源ID: {data_source_id}")
        result = DatabaseConnectionManager.execute_sql(data_source_id, sql)
        if not result['success']:
            raise ValueError(f"SQL执行失败: {result['message']}")
        data = result['data']
        if not data:
            raise ValueError("SQL查询结果为空，无法获取变量值")
        if isinstance(data, list) and len(data) == 1:
            first_row = data[0]
            if isinstance(first_row, dict):
                field_count = len(first_row.keys())
                if field_count == 1:
                    single_value = next(iter(first_row.values()))
                    # 检查是否是datetime类型
                    if isinstance(single_value, datetime):
                        converted_value = single_value.isoformat()
                        logger.info(f"SQL查询成功，单行单字段datetime结果: {converted_value}")
                        return converted_value
                    elif isinstance(single_value, (str, int, float, bool)) and not isinstance(single_value, (dict, list)):
                        logger.info(f"SQL查询成功，单行单字段基本类型结果: {single_value}")
                        return single_value
                    else:
                        logger.info(f"SQL查询成功，单行单字段复杂类型，返回完整数组: {data}")
                        return convert_datetime_to_string(data)
                else:
                    logger.info(f"SQL查询成功，单行多字段，返回完整数组: {data}")
                    return convert_datetime_to_string(data)
            else:
                # 检查是否是datetime类型
                if isinstance(first_row, datetime):
                    converted_value = first_row.isoformat()
                    logger.info(f"SQL查询成功，单行非字典datetime结果: {converted_value}")
                    return converted_value
                elif isinstance(first_row, (str, int, float, bool)) and not isinstance(first_row, (dict, list)):
                    logger.info(f"SQL查询成功，单行非字典基本类型结果: {first_row}")
                    return first_row
                else:
                    logger.info(f"SQL查询成功，单行非字典复杂类型，返回完整数组: {data}")
                    return convert_datetime_to_string(data)
        else:
            # 多行数据或其他格式，返回完整数组
            logger.info(f"SQL查询成功，多行数据或其他格式，返回完整数组: {data}")
            return convert_datetime_to_string(data)
    except json.JSONDecodeError as e:
        error_msg = f"SQL配置JSON解析失败: {str(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)
    except Exception as e:
        error_msg = f"获取SQL结果失败: {str(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)

def detect_variable_usage_in_data(data, var_keys):
    """
    检测数据中是否使用了指定的变量
    
    Args:
        data: 要检测的数据（可以是dict, list, str）
        var_keys: 变量键列表
        
    Returns:
        set: 实际使用的变量键集合
    """
    used_vars = set()
    
    def _scan_data(obj):
        if isinstance(obj, dict):
            for value in obj.values():
                _scan_data(value)
        elif isinstance(obj, list):
            for item in obj:
                _scan_data(item)
        elif isinstance(obj, str):
            for var_key in var_keys:
                placeholder = f'{{{{{var_key}}}}}'
                if placeholder in obj:
                    used_vars.add(var_key)
    
    _scan_data(data)
    return used_vars

def generate_var_dict(envOrProjectOrTempVarsObj):
    var_dict = {}
    for var in envOrProjectOrTempVarsObj:
        if var.variable_type == '1':
            var_dict[var.key] = var.value
        elif var.variable_type == '2':
            var_dict[var.key] = int(var.value)
        elif var.variable_type == '3' or var.variable_type == '5':
            try:
                var_dict[var.key] = json.loads(var.value)
            except Exception as e:
                logger.error(f"解析JSON变量 '{var.key}' 失败: {e}")
                var_dict[var.key] = var.value
        elif var.variable_type == '4':
            var_dict[var.key] = bool(var.value.lower() == 'true')
        elif var.variable_type == '6':
            var_dict[var.key] = float(var.value)
        elif var.variable_type == '7':
            try:
                sql_result = get_sql_result(var.value)
                var_dict[var.key] = sql_result
                logger.info(f"SQL变量处理成功: 键='{var.key}', SQL结果='{sql_result}'")
            except ValueError as e:
                error_msg = f"SQL变量处理失败: 键='{var.key}', 错误='{str(e)}'"
                logger.error(error_msg)
                raise ValueError(error_msg)
        else:
            var_dict[var.key] = var.value
        logger.info(f"生成变量字典: 键='{var.key}', 值类型='{var.variable_type}', 值='{var_dict[var.key]}'")
    return var_dict

def generate_var_dict_lazy(envOrProjectOrTempVarsObj, request_data=None):
    """
    按需生成变量字典，只查询实际使用的SQL变量
    
    Args:
        envOrProjectOrTempVarsObj: 变量对象列表
        request_data: 请求数据，用于检测变量使用情况
        
    Returns:
        dict: 变量字典
    """
    var_dict = {}
    
    # 如果没有传入请求数据，回退到原始逻辑
    if request_data is None:
        logger.warning("未传入请求数据，将查询所有SQL变量（建议传入request_data优化性能）")
        return generate_var_dict(envOrProjectOrTempVarsObj)
    
    # 收集所有变量键
    all_var_keys = [var.key for var in envOrProjectOrTempVarsObj]
    
    # 检测请求数据中实际使用的变量
    used_var_keys = detect_variable_usage_in_data(request_data, all_var_keys)
    logger.info(f"检测到使用的变量: {used_var_keys}")
    
    for var in envOrProjectOrTempVarsObj:
        if var.variable_type == '1':
            var_dict[var.key] = var.value
        elif var.variable_type == '2':
            var_dict[var.key] = int(var.value)
        elif var.variable_type == '3' or var.variable_type == '5':
            try:
                var_dict[var.key] = json.loads(var.value)
            except Exception as e:
                logger.error(f"解析JSON变量 '{var.key}' 失败: {e}")
                var_dict[var.key] = var.value
        elif var.variable_type == '4':
            var_dict[var.key] = bool(var.value.lower() == 'true')
        elif var.variable_type == '6':
            var_dict[var.key] = float(var.value)
        elif var.variable_type == '7':
            # SQL变量：只有当检测到使用时才查询
            if var.key in used_var_keys:
                try:
                    sql_result = get_sql_result(var.value)
                    var_dict[var.key] = sql_result
                    logger.info(f"SQL变量处理成功: 键='{var.key}', SQL结果='{sql_result}'")
                except ValueError as e:
                    error_msg = f"SQL变量处理失败: 键='{var.key}', 错误='{str(e)}'"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            else:
                # 未使用的SQL变量，跳过查询，设置占位符值
                var_dict[var.key] = f"{{{{UNUSED_SQL_VAR_{var.key}}}}}"
                logger.info(f"SQL变量 '{var.key}' 未被使用，跳过SQL查询")
        else:
            var_dict[var.key] = var.value
            
        if var.key in used_var_keys or var.variable_type != '7':
            logger.info(f"生成变量字典: 键='{var.key}', 值类型='{var.variable_type}', 值='{var_dict[var.key]}'")
    
    return var_dict


def format_environment_vars(environment_id, original_api_request_data):
    logger.info(f"开始格式化环境变量: environment_id={environment_id}")
    environment_vars = Environment_Data.objects.filter(environment_id=environment_id)
    logger.info(f"找到环境变量数量: {len(environment_vars)}")
    
    environment_auth = Environment_Auth.objects.filter(environment_id=environment_id).first()
    if environment_auth:
        environment_auth_type = environment_auth.type
        logger.info(f"找到环境认证信息: type={environment_auth_type}")

    auth_dict = original_api_request_data.get('authorization', {})
    if auth_dict['type'] == 'parent_auth' and environment_auth:
        logger.info(f"使用父级认证")
        if environment_auth_type == "1":
            credentials = f"{environment_auth.auth_username}:{environment_auth.auth_password}"
            encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
            original_api_request_data['headers']['authorization'] = f"Basic {encoded_credentials}"
            logger.info(f"设置Basic认证: username={environment_auth.auth_username}")
        elif environment_auth_type == "2":
            credentials = f"Bearer {environment_auth.token_value}"
            original_api_request_data['headers']['authorization'] = credentials
            logger.info(f"设置Bearer认证: token={environment_auth.token_value[:5]}...")
    
    original_api_request_data_body = original_api_request_data.pop('body')
    logger.info(f"提取请求体进行处理")

    # 使用按需查询的变量字典生成
    var_dict = generate_var_dict_lazy(environment_vars, original_api_request_data)
    logger.info(f"开始替换请求体中的环境变量")
    api_request_data_body = replace_placeholders(original_api_request_data_body, var_dict)
    
    data_str = json.dumps(original_api_request_data)
    var_dict_str = {var.key: var.value for var in environment_vars if var.variable_type != '3' and var.variable_type != '5'}
    
    logger.info(f"开始替换请求头和其他参数中的环境变量")
    for key, value in var_dict_str.items():
        if f'{{{{{key}}}}}' in data_str:
            logger.info(f"替换环境变量: key='{key}', value='{value}'")
            data_str = data_str.replace(f'{{{{{key}}}}}', str(value))
    
    api_request_data = json.loads(data_str)
    api_request_data['body'] = api_request_data_body
    logger.info(f"环境变量处理完成")
    
    # 返回格式化数据、环境变量查询结果和已处理的变量字典
    return api_request_data, environment_vars, var_dict


def format_project_vars(project_id, request_data, environment_vars=None, environment_vars_dict=None):
    logger.info(f"开始格式化项目变量: project_id={project_id}")
    project_vars = Public_Data.objects.filter(project_id=project_id)
    logger.info(f"找到项目变量数量: {len(project_vars)}")
    
    project_auth_type = Public_Data.objects.filter(project_id=project_id, type__in=['2', '3']).first()
    if project_auth_type:
        logger.info(f"找到项目认证信息: type={project_auth_type.type}")

    auth_dict = request_data.get('authorization', {})
    headers_dict = request_data.get('headers', {})
    
    if auth_dict['type'] == 'parent_auth' and headers_dict.get('authorization') is None:
        logger.info(f"使用父级认证")
        if project_auth_type and project_auth_type.type == '2':
            credentials = project_auth_type.value
            credentials_dict = json.loads(credentials)
            username, password = next(iter(credentials_dict.items()))
            credentials = f"{username}:{password}"
            encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
            request_data['headers']['authorization'] = f"Basic {encoded_credentials}"
            logger.info(f"设置项目Basic认证: username={username}")
        elif project_auth_type and project_auth_type.type == '3':
            credentials = f"Bearer {project_auth_type.value}"
            request_data['headers']['authorization'] = credentials
            logger.info(f"设置项目Bearer认证: token={project_auth_type.value[:5]}...")
    elif auth_dict['type'] == 'basic_auth':
        credentials = f"{request_data.get('authorization').get('username')}:{request_data.get('authorization').get('password')}"
        encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
        request_data['headers']['authorization'] = f"Basic {encoded_credentials}"
        logger.info(f"设置Basic认证: username={request_data.get('authorization').get('username')}")
    
    elif auth_dict['type'] == 'bearer_token':
        credentials = f"Bearer {request_data.get('authorization').get('token')}"
        request_data['headers']['authorization'] = credentials
        logger.info(f"设置Bearer认证: token={request_data.get('authorization').get('token')[:5]}...")
    
    request_data['headers']['Content-Type'] = request_data.get('content_type', 'application/json')
    if request_data['headers'].get('User-Agent') is None:
        request_data['headers']['User-Agent'] = 'Mozilla/5.0'
    logger.info(f"设置Content-Type: {request_data['headers']['Content-Type']}")
    
    # 删除这个 authorization 字段
    del request_data['authorization']
    logger.info(f"删除原始authorization字段")
    
    original_api_request_data_body = request_data.pop('body')
    logger.info(f"提取请求体进行处理")

    # 修复：将body合并到request_data副本中，保证SQL变量检测能覆盖body
    request_data_for_var_detect = dict(request_data)
    request_data_for_var_detect['body'] = original_api_request_data_body

    # 使用按需查询的变量字典生成
    project_var_dict = generate_var_dict_lazy(project_vars, request_data_for_var_detect)
    var_dict = project_var_dict  # 只使用项目变量，不包含临时变量
    
    logger.info(f"开始替换请求体中的项目变量")
    formatted_api_request_data_body = replace_placeholders(original_api_request_data_body, var_dict)
    
    # 构建用于字符串替换的变量字典，排除复杂类型（JSON对象/数组）
    var_dict_project_str = {}
    for var in project_vars:
        if var.variable_type not in ['3', '5']:  # 排除JSON类型
            if var.variable_type == '7':
                # SQL变量，使用已处理的结果，但只有基础类型才参与字符串替换
                sql_result = project_var_dict.get(var.key)
                if isinstance(sql_result, (str, int, float, bool)) and not isinstance(sql_result, (dict, list)):
                    var_dict_project_str[var.key] = sql_result
                    logger.info(f"SQL变量参与字符串替换: key='{var.key}', value='{sql_result}'")
                else:
                    logger.info(f"SQL变量为复杂类型，不参与字符串替换: key='{var.key}'")
            else:
                var_dict_project_str[var.key] = var.value
    
    var_dict_str = var_dict_project_str  # 只使用项目变量，不包含临时变量
    data_str = json.dumps(request_data)
    
    logger.info(f"开始替换请求头和其他参数中的项目变量（不包含临时变量）")
    for key, value in var_dict_str.items():
        if f'{{{{{key}}}}}' in data_str:
            logger.info(f"替换项目变量: key='{key}', value='{value}'")
            data_str = data_str.replace(f'{{{{{key}}}}}', str(value))

    formatted_api_request_data_body_str = json.dumps(formatted_api_request_data_body)

    logger.info(f"开始替换Faker数据")
    for key, faker_method in faker_data_method_dict.items():
        if key in data_str and callable(faker_method):
            faker_value = faker_method()  # 调用 Faker 方法生成数据
            data_str = data_str.replace(key, faker_value)
            logger.info(f"替换Faker数据: key='{key}', value='{faker_value}'")
            
    for key, faker_method in faker_data_method_dict.items():
        if key in formatted_api_request_data_body_str and callable(faker_method):
            faker_value = faker_method()  # 调用 Faker 方法生成数据
            formatted_api_request_data_body_str = formatted_api_request_data_body_str.replace(key, faker_value)
            logger.info(f"替换请求体中的Faker数据: key='{key}', value='{faker_value}'")

    var_file_data_dict = request_data.get('var_file_data_dict', {})
    if var_file_data_dict:
        logger.info(f"开始替换文件变量数据")
        for key, value in var_file_data_dict.items():
            if f'{{{{{key}}}}}' in data_str or f'{{{{{key}}}}}' in formatted_api_request_data_body_str:
                logger.info(f"替换文件变量: key='{key}', value='{value}'")
                
                if f'{{{{{key}}}}}' in data_str:
                    data_str = data_str.replace(f'{{{{{key}}}}}', str(value))
                
                if f'{{{{{key}}}}}' in formatted_api_request_data_body_str:
                    formatted_api_request_data_body_str = formatted_api_request_data_body_str.replace(f'{{{{{key}}}}}', str(value))

    formatted_api_request_data = json.loads(data_str)
    formatted_api_request_data['body'] = json.loads(formatted_api_request_data_body_str)

    logger.info(f"项目变量处理完成")
    
    # 返回格式化数据和变量查询结果（仅包含项目变量和环境变量）
    # 同时返回已处理的变量字典以避免重复处理
    var_data = {
        'project_vars': project_vars,
        'environment_vars': environment_vars or [],
        'project_var_dict': project_var_dict,  # 已处理的项目变量字典
        'env_var_dict': environment_vars_dict or {}  # 已处理的环境变量字典
    }
    return formatted_api_request_data, var_data


def format_request_data(data):
    logger.info(f"开始格式化请求数据")
    project_id = data['project_id']
    environment_id = data.get('environment_id', None)
    original_api_request_data = data['original_api_request_data']

    if environment_id:
        logger.info(f"请求选择了环境: environment_id={environment_id}")
        envrionment_formatted_api_request_data, environment_vars, environment_vars_dict = format_environment_vars(environment_id, original_api_request_data)
        logger.info(f"环境变量处理完成，继续处理项目变量")
        return format_project_vars(project_id, envrionment_formatted_api_request_data, environment_vars, environment_vars_dict)
    
    logger.info(f"请求未选择环境，直接处理项目变量")
    return format_project_vars(project_id, original_api_request_data)

def build_context_from_vars(var_data, api_id=None, api_model_name=None):
    """
    根据变量数据构建context变量池，只包含项目变量和环境变量
    直接使用已处理的变量字典，避免重复处理（特别是SQL查询）
    
    Args:
        var_data: 包含project_var_dict, env_var_dict的字典
        api_id: API ID (暂时保留参数以保持兼容性)
        api_model_name: API模型名称 (暂时保留参数以保持兼容性)
    
    Returns:
        dict: 构建好的context变量池
    """
    logger.info(f"开始构建context变量池（直接使用已处理的变量字典，避免重复处理）")
    context = {}
    
    # 添加项目变量（使用已处理的字典）
    try:
        project_var_dict = var_data.get('project_var_dict', {})
        context.update(project_var_dict)
        logger.info(f"添加项目变量到context: {len(project_var_dict)}个")
    except Exception as e:
        logger.warning(f"构建项目变量context失败: {str(e)}")
    
    # 添加环境变量（使用已处理的字典）
    try:
        env_var_dict = var_data.get('env_var_dict', {})
        if env_var_dict:
            context.update(env_var_dict)
            logger.info(f"添加环境变量到context: {len(env_var_dict)}个")
    except Exception as e:
        logger.warning(f"构建环境变量context失败: {str(e)}")
    
    logger.info(f"context变量池构建完成，共 {len(context)} 个变量: {list(context.keys())}")
    return context

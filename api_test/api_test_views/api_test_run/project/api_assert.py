import jsonpath
import logging

logger = logging.getLogger('ats-console')

class ApiAssert:

    """断言"""

    def __init__(self, res_json_data_dict, tests_list):
        self.res_json_data_dict = res_json_data_dict
        self.tests_list = tests_list

        # print("self.res_json_data_dict", self.res_json_data_dict)
        # print("self.tests_list", self.tests_list)

        self.res_code = self.res_json_data_dict['res_status_code']
        self.res_headers = self.res_json_data_dict['res_headers']
        self.res_body = self.res_json_data_dict['res_text_data']
        self.res_json_data = self.res_json_data_dict['res_json_data']

        self.tests_code_list = []
        self.tests_header_list = []
        self.tests_body_list = []
        self.tests_json_path_list = []
        for item in self.tests_list:
            if item['obj'] == '1':
                self.tests_code_list.append(item)
            elif item['obj'] == '2':
                self.tests_header_list.append(item)
            elif item['obj'] == '3':
                self.tests_body_list.append(item)
            elif item['obj'] == '4':
                self.tests_json_path_list.append(item)
        
        logger.info(f"开始执行断言 - 状态码断言: {len(self.tests_code_list)}个, 响应头断言: {len(self.tests_header_list)}个, 响应体断言: {len(self.tests_body_list)}个, JSONPath断言: {len(self.tests_json_path_list)}个")
        
        self.result_list = []

        if self.tests_code_list:
            self.validate_status_code()
        if self.tests_header_list:
            self.validate_header()
        if self.tests_body_list:
            self.validate_body()
        if self.tests_json_path_list:
            self.validate_json_path()
            
        # 统计断言结果
        pass_count = sum(1 for result in self.result_list if result['status'] == '通过')
        fail_count = sum(1 for result in self.result_list if result['status'] == '失败')
        logger.info(f"断言完成 - 总计: {len(self.result_list)}个, 通过: {pass_count}个, 失败: {fail_count}个")

    # @allure.step('响应码断言')
    def validate_status_code(self):
        logger.info(f"执行响应码断言 - 当前响应码: {self.res_code}")
        for item in self.tests_code_list:
            logger.info(f"断言: 响应码 {item['operator']} {item['data']}")
            if item['operator'] == '等于':
                try:
                    assert self.res_code == int(item['data'])
                    self.result_list.append({'title': item.get('name', '响应码断言(等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: 响应码 {self.res_code} 等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"预期:{item['data']}与实际:{self.res_code}不相等"
                    self.result_list.append({'title': item.get('name', '响应码断言(等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"响应码断言失败: {error_log}")
            elif item['operator'] == '不等于':
                try:
                    assert self.res_code != int(item['data'])
                    self.result_list.append({'title': item.get('name', '响应码断言(不等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: 响应码 {self.res_code} 不等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"预期:{item['data']}与实际:{self.res_code}相等"
                    self.result_list.append({'title': item.get('name', '响应码断言(不等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"响应码断言失败: {error_log}")


    def validate_header(self):
        logger.info(f"执行响应头断言 - 当前响应头: {list(self.res_headers.keys())}")
        for item in self.tests_header_list:
            header_key = item['header_key']
            logger.info(f"断言: 响应头 '{header_key}' {item['operator']} {item['data']}")
            if header_key not in self.res_headers.keys():
                self.result_list.append({'title': item.get('name', '响应头断言'), 'status': '失败', 'error_log': f"响应头{header_key}不存在"})
                logger.warning(f"响应头断言失败: 响应头{header_key}不存在")
                continue
            if item['operator'] == '包含':
                try:
                    assert item['data'] in self.res_headers[item['header_key']]
                    self.result_list.append({'title': item.get('name', '响应头断言(包含)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: 响应头 {header_key} 包含 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"响应头{item['header_key']}不包含{item['data']}"
                    self.result_list.append({'title': item.get('name', '响应头断言(包含)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"响应头断言失败: {error_log}")
            elif item['operator'] == '存在':
                try:
                    assert item['header_key'] in self.res_headers.keys()
                    self.result_list.append({'title': item.get('name', '响应头断言(存在)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: 响应头 {header_key} 存在")
                except Exception as e:
                    error_log = str(e) if str(e) else f"响应头{item['header_key']}不存在"
                    self.result_list.append({'title': item.get('name', '响应头断言(存在)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"响应头断言失败: {error_log}")
            elif item['operator'] == '等于':
                try:
                    assert item['data'] == self.res_headers[item['header_key']]
                    self.result_list.append({'title': item.get('name', '响应头断言(等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: 响应头 {header_key} 等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"预期:{item['data']}与实际:{self.res_headers[item['header_key']]}不相等"
                    self.result_list.append({'title': item.get('name', '响应头断言(等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"响应头断言失败: {error_log}")
            
    def validate_body(self):
        logger.info(f"执行响应体断言")
        for item in self.tests_body_list:
            logger.info(f"断言: 响应体 {item['operator']} {item['data']}")
            if item['operator'] == '包含':
                try:
                    assert item['data'] in self.res_body
                    self.result_list.append({'title': item.get('name', '响应体断言(包含)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: 响应体包含 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"实际响应体中不包含预期值"
                    self.result_list.append({'title': item.get('name', '响应体断言(包含)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"响应体断言失败: {error_log}")
            elif item['operator'] == '等于':
                try:
                    assert item['data'] == self.res_body
                    self.result_list.append({'title': item.get('name', '响应体断言(等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: 响应体等于预期值")
                except Exception as e:
                    error_log = str(e) if str(e) else f"实际响应体与预期值不相等"
                    self.result_list.append({'title': item.get('name', '响应体断言(等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"响应体断言失败: {error_log}")
    
    def validate_json_path(self):
        logger.info(f"执行JSONPath断言")
        for item in self.tests_json_path_list:
            logger.info(f"断言: JSONPath '{item['jsonpath']}' {item['operator']} {item['data']}")
            json_path_result = jsonpath.jsonpath(self.res_json_data, item['jsonpath'])
            if not json_path_result:
                self.result_list.append({'title': item.get('name', 'JSONPath断言'), 'status': '失败', 'error_log': f"JSONPath '{item['jsonpath']}' 未找到匹配的结果"})
                logger.warning(f"JSONPath断言失败: JSONPath '{item['jsonpath']}' 未找到匹配的结果")
                continue
            if item['operator'] == '包含':
                try:
                    assert item['data'] in str(jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0])
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(包含)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 结果包含 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:{item['data']} 实际:{str(jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0])}"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(包含)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '等于':
                try:
                    res = jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]
                    if isinstance(res, int):
                        assert int(item['data']) == res
                    elif isinstance(res, float):
                        assert float(item['data']) == res
                    else:
                        assert item['data'] == res
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 结果等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:[{item['data']}] 和 实际:[{jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]}] 不相等"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '不等于':
                try:
                    res = jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]
                    if isinstance(res, int):
                        assert int(item['data']) != res
                    elif isinstance(res, float):
                        assert float(item['data']) != res
                    else:
                        assert item['data'] != res
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(不等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 结果不等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:[{item['data']}] 和 实际:[{jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]}] 相等"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(不等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '大于':
                try:
                    res = jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]
                    if isinstance(res, int):
                        assert int(item['data']) < res
                    elif isinstance(res, float):
                        assert float(item['data']) < res
                    else:
                        assert False
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(大于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 结果大于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:{item['data']} 实际:{jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]}"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(大于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '小于':
                try:
                    res = jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]
                    if isinstance(res, int):
                        assert int(item['data']) > res
                    elif isinstance(res, float):
                        assert float(item['data']) > res
                    else:
                        assert False
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(小于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 结果小于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:{item['data']} 实际:{jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]}"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(小于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '大于等于':
                try:
                    res = jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]
                    if isinstance(res, int):
                        assert int(item['data']) <= res
                    elif isinstance(res, float):
                        assert float(item['data']) <= res
                    else:
                        assert False
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(大于等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 结果大于等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:{item['data']} 实际:{jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]}"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(大于等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '小于等于':
                try:
                    res = jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]
                    if isinstance(res, int):
                        assert int(item['data']) >= res
                    elif isinstance(res, float):
                        assert float(item['data']) >= res
                    else:
                        assert False
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(小于等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 结果小于等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:{item['data']} 实际:{jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]}"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(小于等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '存在':
                try:
                    assert jsonpath.jsonpath(self.res_json_data, item['jsonpath']) is not False
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(存在)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 存在")
                except Exception as e:
                    error_log = str(e) if str(e) else f"JSONPath:{item['jsonpath']} 预期:{item['data']} 实际:{jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0]}"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(存在)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: {error_log}")
            elif item['operator'] == '类型等于':
                type_map = {
                    'string': str,
                    'int': int,
                    'object': dict,
                    'array': list,
                    'boolean': bool,
                    'number': float,
                    'NoneType': type(None)  # 对应于JSON的null
                }
                try:
                    assert isinstance(jsonpath.jsonpath(self.res_json_data, item['jsonpath'])[0], type_map[item['data']])
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(类型等于)'), 'status': '通过', 'error_log': ''})
                    logger.info(f"断言通过: JSONPath '{item['jsonpath']}' 类型等于 {item['data']}")
                except Exception as e:
                    error_log = str(e) if str(e) else f"类型不匹配"
                    self.result_list.append({'title': item.get('name', 'JSONPath断言(类型等于)'), 'status': '失败', 'error_log': f'{error_log}'})
                    logger.warning(f"JSONPath断言失败: 类型不匹配")
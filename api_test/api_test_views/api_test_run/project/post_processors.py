import json
import jsonpath
import logging

logger = logging.getLogger('ats-console')

class ResponseProcessor:
    """
    用jsonpath表达式提取响应体中的具体数值
    """

    def __init__(self, res_json_data_dict, postfix_list, environment_id, project_id, api_id, api_model_name, api_ids=None):
        self.response_json = res_json_data_dict['res_json_data']
        self.postfix_list = postfix_list
        self.environment_id = environment_id
        self.project_id = project_id
        self.api_id = api_id
        self.api_ids = api_ids  # 添加这个可选参数
        self.api_model_name = api_model_name
        if self.api_model_name == 'QuickTest':
            self.api_id_key_name = "quick_api_id"
        elif self.api_model_name == 'TestCase':
            self.api_id_key_name = "api_case_id"
        elif self.api_model_name == 'TestCaseChild':
            self.api_id_key_name = "api_case_child_id"

        logger.info(f"开始后置处理 - API类型: {self.api_model_name}, API ID: {self.api_id}, 提取变量数量: {len(self.postfix_list)}")

        # 初始化列表
        self.environment_result_list = []
        self.project_result_list = []
        self.tmp_result_list = []

        # 处理数据
        self.process_data()
        
        # 记录处理结果
        logger.info(f"后置处理完成 - 环境变量: {len(self.environment_result_list)}个, 项目变量: {len(self.project_result_list)}个, 临时变量: {len(self.tmp_result_list)}个")

    def process_data(self):
        for item in self.postfix_list:
            logger.info(f"提取变量 - 键名: {item['key']}, JSONPath: {item['value']}, 存储类型: {item['type']}")
            json_path_value = jsonpath.jsonpath(self.response_json, item['value'])
            variable_type = "1"
            if not json_path_value:
                logger.error(f"未能通过jsonpath提取值: {item['value']}")
                pass
            else:
                # 如果json_path_value是列表且只有一个元素，则直接使用该元素
                if isinstance(json_path_value, list) and len(json_path_value) == 1:
                    json_path_value = json_path_value[0]
                else:
                    json_path_value = json_path_value
                # 将布尔类型的检查移到了整数类型检查之前。这样，如果 json_path_value 是 True 或 False，它将被正确地识别为布尔类型，而不是整数。
                if isinstance(json_path_value, bool):
                    variable_type = "4"
                    logger.info(f"提取到布尔值: {json_path_value}")
                elif isinstance(json_path_value, int):
                    variable_type = "2"
                    logger.info(f"提取到整数值: {json_path_value}")
                elif isinstance(json_path_value, dict):
                    json_path_value = json.dumps(json_path_value)
                    variable_type = "5"
                    logger.info(f"提取到对象值: {json_path_value[:50]}...")
                elif isinstance(json_path_value, list):
                    json_path_value = json.dumps(json_path_value)
                    variable_type = "3"
                    logger.info(f"提取到数组值: {json_path_value[:50]}...")
                elif isinstance(json_path_value, float):
                    variable_type = "6"
                    logger.info(f"提取到浮点值: {json_path_value}")
                else:
                    logger.info(f"提取到字符串值: {json_path_value}")

                if item['type'] == '2':
                    if self.environment_id:
                        environment_data = {"key": item['key'], "type": "1", "value": json_path_value,
                                            "environment_id": self.environment_id, "variable_type": variable_type}
                        self.environment_result_list.append(environment_data)
                        logger.info(f"提取到环境变量: {item['key']} = {str(json_path_value)[:50]}, 环境ID: {self.environment_id}")
                    else:
                        logger.warning(f"环境ID为空，跳过环境变量提取: {item['key']}")
                elif item['type'] == '3' and self.project_id is not None:
                    project_data = {"key": item['key'], "type": "1", "value": json_path_value,
                                    "project_id": self.project_id, "variable_type": variable_type}
                    self.project_result_list.append(project_data)
                    logger.info(f"提取到项目变量: {item['key']} = {str(json_path_value)[:50]}, 项目ID: {self.project_id}")
                else:
                    tmp_data = {"key": item['key'], "value": json_path_value, "project_id": self.project_id, 
                                "variable_type": variable_type, self.api_id_key_name: self.api_id}
                    self.tmp_result_list.append(tmp_data)
                    logger.info(f"提取到临时变量: {item['key']} = {str(json_path_value)[:50]}, API ID: {self.api_id}")

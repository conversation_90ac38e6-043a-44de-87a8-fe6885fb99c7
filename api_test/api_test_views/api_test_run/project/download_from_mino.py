from api_test.api_test_views.api_test_run.minio_test.minio_oper import create_minio_client, download_file
from django.conf import settings

local_path = './'

# # 创建 MinIO 客户端
# client = create_minio_client(
#     settings.MINIO_ENDPOINT,
#     settings.MINIO_ACCESS_KEY,
#     settings.MINIO_SECRET_KEY
# )

if __name__ == '__main__':
    client = create_minio_client(
    settings.MINIO_ENDPOINT,
    settings.MINIO_ACCESS_KEY,
    settings.MINIO_SECRET_KEY
    )
    download_file(client, 'api-test-multipart-data', 'sadfsaf|连云港人力资源_3.docx', local_path)

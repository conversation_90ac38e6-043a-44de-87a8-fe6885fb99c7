from rest_framework import serializers
from api_test.models import QuickTestApiParams, QuickTestApiAuth, QuickTestApiHeaders, QuickTestApiBody, \
    QuickTestApiExtract, QuickTestApiAssertion


class QuickTestApiParamsSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickTestApiParams
        fields = '__all__'


class QuickTestApiAuthSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickTestApiAuth
        fields = '__all__'


class QuickTestApiHeadersSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickTestApiHeaders
        fields = '__all__'


class QuickTestApiBodySerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickTestApiBody
        fields = '__all__'


class QuickTestApiExtractSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickTestApiExtract
        fields = '__all__'


class QuickTestApiAssertionSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickTestApiAssertion
        fields = '__all__'

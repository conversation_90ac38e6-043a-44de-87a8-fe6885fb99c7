from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.response import Response
from ..serializers.api_tree_folder_serializer import ApiTreeFolderSerializer
from ...models import Api_Tree_Folder, QuickTest


class ApiTreeFolderView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = ApiTreeFolderSerializer

    def get_child_folder(self, item):
        # 匹配下级子菜单
        second_level_folders = Api_Tree_Folder.objects.filter(parent_id=item.id)

        # 继续递归匹配是否有多级子菜单
        children = [self.get_child_folder(child) for child in second_level_folders]

        return {
            "id": item.id,
            "name": item.name,
            "type": item.type,
            "parent_id": item.parent_id,
            "if_stream": item.if_stream,
            "children": children or None
        }

    def get(self, request):
        # 需要项目id筛选查询
        project_id = request.query_params.get('project_id')
        # 获取顶级目录
        first_level_folder = Api_Tree_Folder.objects.get(project_id=project_id, name='顶级目录', parent=None)

        # 递归下级目录
        child_folder = self.get_child_folder(first_level_folder)

        return Response(public_success_response(child_folder))

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))


class ApiTreeFolderDetailView(APIView, MyAuthentication):
    serializer_class = ApiTreeFolderSerializer

    def put(self, request, pk):
        folder = Api_Tree_Folder.objects.get(pk=pk)
        old_name = folder.name
        serializer = self.serializer_class(instance=folder, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            # 同时更新接口名
            if QuickTest.objects.filter(api_name=old_name).exists():
                QuickTest.objects.filter(api_name=old_name).update(api_name=request.data['name'])
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))

    def delete(self, request, pk):
        folder = Api_Tree_Folder.objects.get(pk=pk)
        if folder.name != '顶级目录':
            folder.delete()
            return Response(public_success_response('删除成功'))
        return Response(public_error_response('顶级目录不允许删除！'))



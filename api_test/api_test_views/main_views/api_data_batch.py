import json

from ...models import QuickTestApiExtract, QuickTestApiHeaders, QuickTestApiAssertion, QuickTestApiBody, \
    QuickTestApiAuth, QuickTestApiParams, QuickTest

import logging

logger = logging.getLogger('ats-console')

def handle_request_data(data):
    try:
        params = data['params']
        headers = data['headers']
        content_type = data['content_type']
        body = data['body']
        authorization = data['authorization']
        # pre_script = data['pre_script']
        # post_script = data['post_script']
        postfix = data['postfix']
        tests = data['tests']
        api_id = data['folder']
    except Exception as e:
        logger.error(f"handle_request_data error: {str(e)}")

    params_res = {item['key']: item['value'] for item in params}
    headers_res = {item['key']: item['value'] for item in headers}
    postfix_res = [{k: v for k, v in item.items() if k not in ['id', 'desc']} for item in postfix]
    tests_res = [{k: v for k, v in item.items() if k not in ['id']} for item in tests]

    # print(content_type)

    # 如果非json就传key value对象
    if content_type == 'application/x-www-form-urlencoded' or content_type == 'multipart/form-data':
        body_res = {item['key']: item['value'] for item in body}
    elif content_type == 'application/json':
        body_res = json.loads(data['body'])
    else:
        body_res = None

    api_data = {
        "case_code": data['case_code'],
        "case_name": data['case_name'],
        "module_name": data['module_name'],
        "api_name": data['api_name'],
        "request_url": data['request_url'],
        "request_path": data['request_path'],
        "request_method": data['request_method'],
        "params": params_res,
        "authorization": authorization,
        "content_type": data['content_type'],
        "headers": headers_res,
        "body": body_res,
        "pre_script": data['pre_script'],
        "post_script": data['post_script'],
        "folder": api_id,
        "creator": data['creator'],
        "tests": tests_res,
        "postfix": postfix_res,
        "api_id": data['folder'],
        "environment": data['environment']
    }

    # print(type(body_res))
    # print("api_data", api_data)
    return api_data


def handle_request_sub_data(data):
    try:
        api_id = data['folder']
        api = QuickTest.objects.get(folder_id=api_id, api_name=data['api_name'])
    except Exception as e:
        logger.error(f"handle_request_sub_data error: {str(e)}")

    def handle_data(model, key, api, value, content_type=None, body=None):
        if key in ['params', 'headers', 'postfix', 'tests']:
            existing_ids = set(model.objects.filter(api_id=api).values_list('id', flat=True))
            for item in value:
                data_id = item.pop('id', None)
                if data_id and data_id != -1:
                    model.objects.filter(id=data_id).update(api_id=api, **item)
                    existing_ids.discard(data_id)
                else:
                    model.objects.create(api=api, **item)
            model.objects.filter(id__in=existing_ids).delete()
        elif key == 'authorization':
            model.objects.filter(api=api).delete()
            model.objects.create(api=api, **value)
        elif key == 'body':
            if content_type == 'multipart/form-data':
                existing_ids = set(model.objects.filter(api_id=api).values_list('id', flat=True))
                for item in value:
                    data_id = item.pop('id', None)
                    if data_id and data_id != -1:
                        model.objects.filter(id=data_id).update(api=api, **item)
                        existing_ids.discard(data_id)
                    else:
                        model.objects.create(api=api, **item)
                model.objects.filter(id__in=existing_ids).delete()
                model.objects.filter(api_id=api).exclude(body_type='2').delete()
            elif content_type == 'application/x-www-form-urlencoded':
                existing_ids = set(model.objects.filter(api_id=api).values_list('id', flat=True))
                for item in value:
                    data_id = item.pop('id', None)
                    if data_id and data_id != -1:
                        model.objects.filter(id=data_id).update(api=api, **item)
                        existing_ids.discard(data_id)
                    else:
                        model.objects.create(api=api, **item)
                model.objects.filter(id__in=existing_ids).delete()
                model.objects.filter(api_id=api).exclude(body_type='3').delete()
            elif content_type == 'application/json':
                model.objects.filter(api=api).delete()
                model.objects.create(api=api, body_type='1', json_value=json.loads(body))
            elif content_type == 'none':
                model.objects.filter(api=api).delete()
                model.objects.create(api=api, body_type='0')

    for k, v in data.items():
        if k == 'params':
            handle_data(QuickTestApiParams, k, api, v)
        elif k == 'headers':
            handle_data(QuickTestApiHeaders, k, api, v)
        elif k == 'postfix':
            handle_data(QuickTestApiExtract, k, api, v)
        elif k == 'authorization':
            handle_data(QuickTestApiAuth, k, api, v)
        elif k == 'tests':
            handle_data(QuickTestApiAssertion, k, api, v)
        elif k == 'body':
            handle_data(QuickTestApiBody, k, api, v, content_type=data['content_type'], body=data['body'])
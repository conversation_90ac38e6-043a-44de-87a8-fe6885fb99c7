import os
from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from django.conf import settings
from ..serializers.multipart_file_serializer import MultipartFileSerializer
from ...models import MultipartFile

path = settings.MEDIA_ROOT + 'form_data_files/'

class MultipartFileUploadView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = MultipartFileSerializer

    def post(self, request):
        key = request.data.get('key')
        files = request.FILES.getlist('file')
        project_id = request.data.get('project_id')
        MultipartFile.objects.filter(file_key=key, project_id=project_id).delete()
        if not os.path.exists(path + project_id + '/' + key):
            os.makedirs(path + project_id + '/' + key)
        else:
            for filename in os.listdir(path + project_id + '/' + key):
                os.remove(os.path.join(path + project_id + '/' + key, filename))

        upload_results = []
        for file_obj in files:
            file_path = os.path.join(path + project_id + '/' + key, file_obj.name)

            data = {
                "file_name": file_obj.name,
                "file_size": file_obj.size,
                "file_path": file_path,
                "file_key": key,
                "project_id": project_id,
            }

            serializer = self.serializer_class(data=data)
            if serializer.is_valid():
                serializer.save()
            else:
                return Response(public_error_response(f"保存失败: {serializer.errors}"))

            try:
                object_name = file_obj.name
                with open(file_path, 'wb') as f:
                    for chunk in file_obj.chunks():
                        f.write(chunk)
                # 从内存中直接上传数据流到本地
                upload_results.append(f"上传成功: {object_name}")
            except Exception as exc:
                upload_results.append(f"上传失败: {object_name}, 错误: {exc}")
            
        # 返回所有文件的上传结果
        if all("成功" in result for result in upload_results):
            return Response(public_success_response(upload_results))
        else:
            return Response(public_error_response(upload_results))

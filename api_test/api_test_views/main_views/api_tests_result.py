from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from rest_framework.views import APIView
from rest_framework.response import Response
from ...models import QuickTestsResult, QuickTest
from ..serializers.api_tests_result_serializer import ApiTestsResultSerializer

class ApiTestsResultView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = ApiTestsResultSerializer

    def get(self, request):
        folder_id = request.query_params.get('folder_id')
        if folder_id:
            try:
                api_id = QuickTest.objects.get(folder_id=folder_id).id
                queryset = QuickTestsResult.objects.filter(api_id=api_id)
                serializer = self.serializer_class(queryset, many=True)
                return Response(public_success_response(serializer.data))
            except QuickTest.DoesNotExist:
                return Response(public_error_response('该树节点下没有API'))
        else:
            return Response(public_error_response('获取树节点失败'))


from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...api_test_views.api_test_run.minio_test.minio_oper import create_minio_client, create_bucket
from django.conf import settings
from ..serializers.multipart_file_serializer import MultipartFileSerializer

# 创建 MinIO 客户端
client = create_minio_client(
    settings.MINIO_ENDPOINT,
    settings.MINIO_ACCESS_KEY,
    settings.MINIO_SECRET_KEY
)

class MultipartFileUploadView1(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = MultipartFileSerializer
    bucket_name = 'api-test-multipart-data'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 在类初始化时创建存储桶，避免每次请求都创建
        create_bucket(client, self.bucket_name)

    def post(self, request):
        key = request.data.get('key')
        files = request.FILES.getlist('file')
        # print(key)
        # print(files)
        upload_results = []
        for file_obj in files:

            data = {
                "file_name": key + '|' + file_obj.name,
                "file_size": file_obj.size,
                "file_path": None,
                "file_bucket": self.bucket_name
            }

            serializer = self.serializer_class(data=data)
            if serializer.is_valid():
                serializer.save()
                return_data = serializer.data
            else:
                return Response(public_error_response(f"保存失败: {serializer.errors}"))

            create_bucket(client, self.bucket_name)

            # 上传文件到 MinIO
            try:
                object_name = data['file_name']
                # 从内存中直接上传数据流到 MinIO 存储桶
                client.put_object(self.bucket_name, object_name, file_obj, file_obj.size)
                upload_results.append(f"上传成功: {object_name}")
            except Exception as exc:
                upload_results.append(f"上传失败: {object_name}, 错误: {exc}")
            
        # 返回所有文件的上传结果
        if all("成功" in result for result in upload_results):
            return Response(public_success_response(upload_results))
        else:
            return Response(public_error_response(upload_results))

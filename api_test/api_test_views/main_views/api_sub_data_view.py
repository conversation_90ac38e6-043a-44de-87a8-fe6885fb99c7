from rest_framework.views import APIView
from rest_framework.response import Response
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...models import QuickTest, QuickTestApiHeaders, QuickTestApiAssertion, \
    QuickTestApiBody, QuickTestApiAuth, QuickTestApiParams, QuickTestApiExtract
from ..serializers.api_data_batch_serializer import QuickTestApiParamsSerializer, QuickTestApiHeadersSerializer, \
    QuickTestApiAuthSerializer, QuickTestApiExtractSerializer, QuickTestApiAssertionSerializer, \
    QuickTestApiBodySerializer

import logging

logger = logging.getLogger('ats-console')


class ApiSubDataDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def get(self, request):
        api_name = request.query_params.get('api_name')
        try:
            api = QuickTest.objects.get(api_name=api_name)

            params_query_set = QuickTestApiParams.objects.filter(api=api)
            params_serializer = QuickTestApiParamsSerializer(params_query_set, many=True)
            params = params_serializer.data

            headers_query_set = QuickTestApiHeaders.objects.filter(api=api)
            headers_serializer = QuickTestApiHeadersSerializer(headers_query_set, many=True)
            headers = headers_serializer.data

            authorization_query_set = QuickTestApiAuth.objects.get(api=api)
            authorization_serializer = QuickTestApiAuthSerializer(authorization_query_set, many=False)
            authorization = authorization_serializer.data

            postfix_query_set = QuickTestApiExtract.objects.filter(api=api)
            postfix_serializer = QuickTestApiExtractSerializer(postfix_query_set, many=True)
            postfix = postfix_serializer.data

            tests_query_set = QuickTestApiAssertion.objects.filter(api=api)
            tests_serializer = QuickTestApiAssertionSerializer(tests_query_set, many=True)
            tests = tests_serializer.data

            body_query_set = QuickTestApiBody.objects.filter(api=api)
            body_serializer = QuickTestApiBodySerializer(body_query_set, many=True)
            body = body_serializer.data

            data = {
                'case_code': api.case_code,
                'api_name': api.api_name,
                'request_path': api.request_path,
                'environment': api.environment,
                'request_url': api.request_url,
                'request_method': api.request_method,
                'params': params,
                'headers': headers,
                'content_type': api.content_type,
                'body': body,
                'authorization': authorization,
                'pre_script': api.pre_script,
                'post_script': api.post_script,
                'postfix': postfix,
                'tests': tests,
                'creator': api.creator
            }
            return Response(public_success_response(data))
        except QuickTest.DoesNotExist:
            logger.error("QuickTest.DoesNotExist")
            return Response(public_error_response('接口不存在'), status=200)

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from ..serializers.api_request_serializer import ApiRe<PERSON>Serializer, QuickTestExecutionSerializer
from ...models import QuickTest, QuickTestExecution
from .api_data_batch import handle_request_data, handle_request_sub_data
import logging

logger = logging.getLogger('ats-console')


class ApiRequestView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = ApiRequestSerializer

    def get(self, request):
        apis = QuickTest.objects.all().order_by('create_time')
        response_data = []

        for api in apis:
            api_data = self.serializer_class(api).data
            try:
                execution_data = QuickTestExecution.objects.get(quick_test=api)
                execution_serializer = QuickTestExecutionSerializer(execution_data)
                api_data['execution'] = execution_serializer.data
            except QuickTestExecution.DoesNotExist:
                logger.error("QuickTestExecution.DoesNotExist")
                api_data['execution'] = {}  # 提供一个空字典作为默认值

            response_data.append(api_data)

        return Response(public_success_response(response_data))

    def post(self, request):
        orl_data = request.data
        
        quick_test_data = {key: value for key, value in request.data.items() if
                           key not in ['execution_count', 'timeout', 'if_execution']}
        # print(quick_test_data, "quick_test_data")
        quick_test_data = handle_request_data(quick_test_data)
        # print(quick_test_data)
        execution_data = {key: value for key, value in request.data.items() if
                          key in ['execution_count', 'timeout', 'if_execution']}
        serializer = self.serializer_class(data=quick_test_data)
        if serializer.is_valid():
            try:
                quick_test = QuickTest.objects.get(api_name=quick_test_data['api_name'])
                for key, value in serializer.validated_data.items():
                    setattr(quick_test, key, value)
                quick_test.save()
            except QuickTest.DoesNotExist:
                logger.error("QuickTest.DoesNotExist")
                quick_test = QuickTest.objects.create(**serializer.validated_data)
                QuickTestExecution.objects.create(quick_test=quick_test, **execution_data)
            handle_request_sub_data(orl_data)
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        else:
            return Response(public_error_response(serializer.errors))


class ApiRequestDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = ApiRequestSerializer

    def put(self, request, pk):
        try:
            api = QuickTest.objects.get(pk=pk)
            execution = QuickTestExecution.objects.get(quick_test=api)
        except QuickTest.DoesNotExist:
            logger.error("QuickTest.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

        quick_test_data = {key: value for key, value in request.data.items() if
                           key not in ['execution_count', 'timeout', 'if_execution']}
        execution_data = {key: value for key, value in request.data.items() if
                          key in ['execution_count', 'timeout', 'if_execution']}

        serializer = self.serializer_class(instance=api, data=quick_test_data)
        if serializer.is_valid():
            serializer.save()
            for key, value in execution_data.items():
                setattr(execution, key, value)
            execution.save()
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        else:
            return Response(public_error_response(serializer.errors))

    def get(self, request, pk):
        try:
            api = QuickTest.objects.get(pk=pk)
            serializer = self.serializer_class(api)
            api_data = serializer.data.copy()

            try:
                execution_data = QuickTestExecution.objects.get(quick_test=api)
                execution_serializer = QuickTestExecutionSerializer(execution_data)
                api_data['execution'] = execution_serializer.data
            except QuickTestExecution.DoesNotExist:
                api_data['execution'] = {}  # 提供一个空字典作为默认值

            return Response(public_success_response(api_data))

        except QuickTest.DoesNotExist:
            logger.error("QuickTest.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, pk):
        try:
            api = QuickTest.objects.get(pk=pk)
            api.delete()
            return Response(public_success_response('删除成功！'), status=status.HTTP_200_OK)
        except QuickTest.DoesNotExist:
            logger.error("QuickTest.DoesNotExist")
            return Response(public_error_response('API对象不存在'), status=status.HTTP_404_NOT_FOUND)

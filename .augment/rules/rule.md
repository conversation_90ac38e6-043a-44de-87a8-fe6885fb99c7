---
type: "always_apply"
---

You are an expert in Python, Django, Django REST Framework, and scalable web application development.
Key Principles
- Write clear, technical responses with precise Django REST Framework examples.
- Use Django REST Framework's built-in features and tools wherever possible to leverage its full capabilities.
- Prioritize readability and maintainability; follow Django's coding style guide (PEP 8 compliance).
- Use descriptive variable and function names; adhere to naming conventions (e.g•, lowercase with underscores for functions and variables).
- Structure your project in a modular way using Django apps to promote reusability and separation of concerns. 
  
Django/Python/Django REST Framework

- Allways use Django REST Framework's class-based views (APIView) for views.
- Leverage Django REST Framework's ORM for database interactions; avoid raw SQL queries unless necessary for performance.
- All asynchronous tasks are implemented using Celery, and the ats_celery app already exists in my project for you to use
- When creating a model, consider scalability and refer to my existing model format
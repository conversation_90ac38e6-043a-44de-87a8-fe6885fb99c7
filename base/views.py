from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from ats.public.public_class.my_modelviewset import MyModelViewSet
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from rest_framework.permissions import IsAuthenticated
from .models import MyUser, LoginLog
from .sys_admin_views.serializers.login_log_serializer import LoginLogSerializer
from ats.public.public_class.myresponse import public_success_response, public_error_response


class LoginView(APIView):
    def post(self, request):
        username = request.data.get("username")
        try:
            user = MyUser.objects.get(username=username)
        except MyUser.DoesNotExist:
            return Response({
                "code": 2001,
                "status": "error",
                "msg": "用户名或密码错误！"
            }, status=status.HTTP_401_UNAUTHORIZED)
        password = request.data.get("password")
        user = authenticate(username=username, password=password)
        login_ip = request.META.get('HTTP_X_FORWARDED_FOR').split(',')[0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR')

        if user is None:
            LoginLog.objects.create(user_name=username, login_ip=login_ip, login_status=0)
            return Response({
                "code": 2001,
                "status": "error",
                "msg": "用户名或密码错误！"
            }, status=status.HTTP_401_UNAUTHORIZED)

        refresh = RefreshToken.for_user(user)
        group_ids = user.groups.values_list('id', flat=True)
        LoginLog.objects.create(user_name=username, login_ip=login_ip, login_status=1)
        return Response({
            "code": 2000,
            "status": "success",
            "msg": "登录成功！",
            "user_id": user.id,
            "user_groupids": group_ids,
            "refresh_token": str(refresh),
            "access_token": str(refresh.access_token),
        })


class LogoutView(APIView):
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        try:
            refresh_token = request.data["refresh_token"]
            token = RefreshToken(refresh_token)
            token.blacklist()

            return Response({
                "code": 2000,
                "status": "success",
                "msg": "退出登录成功！",
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "code": 2001,
                "status": "error",
                "msg": "退出登录失败，无效的token！",
                "error": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

class LoginLogView(MyModelViewSet, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = LoginLogSerializer

    def get_queryset(self):
        queryset = LoginLog.objects.all().order_by('-login_time')  # 按时间降序排序
        return queryset
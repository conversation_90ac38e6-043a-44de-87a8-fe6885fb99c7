from rest_framework.views import APIView
from rest_framework.response import Response
from base.models import <PERSON>us, Button, MyGroup, GroupMenu_Permission, UserMenu_Permission
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
import logging

logger = logging.getLogger('ats-console')

# 用户组绑定菜单和按钮
class GroupAddMenuButtonPermissions(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def post(self, request):
        if not request.data:
            return Response(public_error_response('缺少请求参数 groupId、menuCode、buttonCodes '))

        group_id = request.data.get('groupId')
        menu_id = request.data.get('menuCode')
        button_codes = request.data.get('buttonCodes')

        try:
            group = MyGroup.objects.get(id=group_id)
            menu = Menus.objects.get(menu_id=menu_id)
            buttons = Button.objects.filter(button_code__in=button_codes)
        except (MyGroup.DoesNotExist, Menus.DoesNotExist, Button.DoesNotExist):
            logger.error("Group, menu or button does not exist")
            return Response(public_error_response('Group, menu or button does not exist'))

        permission = GroupMenu_Permission(group=group, menu=menu)
        permission.save()
        permission.buttons.set(buttons)

        return Response(public_success_response('操作成功！'))


class MenuRoleViewA(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def get_menu_data(self, item, group_menu_permissions):
        # 匹配一级子菜单
        children = Menus.objects.filter(p_menu_code=item.menu_code,
                                        id__in=[gmp.menu.id for gmp in group_menu_permissions])
        # 继续递归匹配是否又多级子菜单
        children_data = [self.get_menu_data(child, group_menu_permissions) for child in children]

        # 找到对应的 GroupMenu_Permission 对象
        gmp = next((gmp for gmp in group_menu_permissions if gmp.menu.id == item.id), None)
        if gmp:
            # 从 GroupMenu_Permission 对象中获取按钮权限
            buttons = [button.button_name for button in gmp.buttons.all()]
        else:
            buttons = []

        return {
            "path": item.menu_router,
            "name": item.menu_router,
            "component": item.component,
            "menuOrder": item.menu_order,
            # item.buttons 是一个按钮对象的集合，而不是一个字符串列表。
            # 需要将按钮对象的集合转换为按钮名称的列表。
            "buttons": buttons,
            "meta": {
                "title": item.menu_name,
                "icon": item.menu_icon_path,
                "if_hidden": item.if_hidden
            },
            "children": children_data or None
        }

    def get(self, request):
        user_id = request.user.id

        # 获取用户关联的菜单和按钮
        user_menu_permissions = UserMenu_Permission.objects.filter(user_id=user_id)

        # 获取用户所在的所有用户组
        user_groups = request.user.groups.all()

        data = []
        # 处理用户权限
        for ump in user_menu_permissions:
            if ump.menu.p_menu_code == 'TOP_MENU':
                data.append(self.get_menu_data(ump.menu, user_menu_permissions))

        # 处理用户组权限
        for group in user_groups:
            group_menu_permissions = GroupMenu_Permission.objects.filter(group=group)
            for gmp in group_menu_permissions:
                if gmp.menu.p_menu_code == 'TOP_MENU':
                    menu_data = self.get_menu_data(gmp.menu, group_menu_permissions)
                    # 如果菜单数据不在 data 中，则添加到 data 中
                    if menu_data not in data:
                        data.append(menu_data)

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": data
        }
        return Response(response_data)

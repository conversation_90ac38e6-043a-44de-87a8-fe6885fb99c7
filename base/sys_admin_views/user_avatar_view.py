from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser
from base.models import MyUser
from .serializers.user_view_serializers import UserAvatarSerializer
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from django.http import FileResponse
from django.conf import settings
import os
import logging

logger = logging.getLogger('ats-console')

class UserAvatarUploadView(APIView):
    parser_classes = [MultiPartParser]

    def put(self, request, pk=None):
        self.authentication_classes = MyAuthentication.authentication_classes
        self.permission_classes = MyAuthentication.permission_classes
        self.filter_backends = MyAuthentication.filter_backends
        self.check_permissions(request)
        serializer = UserAvatarSerializer(data=request.data)
        user = MyUser.objects.get(pk=pk)
        if serializer.is_valid():
            user.avatar = serializer.validated_data['avatar']
            user.save()
            serializer = UserAvatarSerializer(user)
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))

    def get(self, request, pk=None):
        # full_url = request.build_absolute_uri()
        user = MyUser.objects.get(pk=pk)
        filename = user.avatar.path
        file_path = os.path.join(settings.MEDIA_ROOT, 'avatar', filename)
        if os.path.exists(file_path):
            # 直接获取文件
            return FileResponse(open(file_path, 'rb'))
            # return Response(public_success_response(full_url))
        else:
            logger.error("Avatar file does not exist!")
            return Response(public_error_response('Avatar file does not exist!'))

from rest_framework import status
from rest_framework.response import Response
from django.contrib.auth.hashers import make_password, check_password
from rest_framework.views import APIView
from base.models import MyUser
from ..serializers.user_view_serializers import UserCenterPutSerializer, UserCenterPutPasswordSerializer
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
import logging

logger = logging.getLogger('ats-console')


class UserCenterProfileDetail(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def put(self, request, pk=None):
        logger.info(f"Attempting to update user profile for user ID: {pk}")
        user = MyUser.objects.get(pk=pk)
        serializer = UserCenterPutSerializer(user, data=request.data)
        if serializer.is_valid():
            serializer.save()  # 使用 serializer.save() 保存更改后的数据
            logger.info(f"User profile updated successfully for user ID: {pk}")
            return Response(public_success_response(serializer.data))
        logger.error(f"Error updating user profile for user ID: {pk}, Errors: {serializer.errors}")
        return Response(public_error_response(serializer.errors))


class UserCenterPasswordModify(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def put(self, request, pk=None):
        logger.info(f"Attempting to change password for user ID: {pk}")
        user = MyUser.objects.get(pk=pk)
        # print(request.data.get('old_password'))
        # print(user.password)
        checked = check_password(request.data.get('old_password'), user.password)
        # print(checked)

        if checked:
            serializer = UserCenterPutPasswordSerializer(user, data=request.data)
            if serializer.is_valid():
                password = request.data.get('password')
                hashed_password = make_password(password)
                serializer.validated_data['password'] = hashed_password
                serializer.save()
                logger.info(f"Password changed successfully for user ID: {pk}")
                return Response(public_success_response('密码修改成功！'))
            logger.error(f"Error changing password for user ID: {pk}, Errors: {serializer.errors}")
            return Response(public_error_response(serializer.errors))
        else:
            logger.warning(f"Failed password change attempt for user ID: {pk} due to incorrect old password")
            return Response(public_error_response('旧密码不正确！'), status=status.HTTP_400_BAD_REQUEST)

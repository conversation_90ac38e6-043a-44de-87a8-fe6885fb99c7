from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from base.models import <PERSON><PERSON>, <PERSON>ton, MyGroup, GroupMenu_Permission
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
import logging

logger = logging.getLogger('ats-console')

# 用户组绑定菜单和按钮
class GroupAddMenuButtonPermissions(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def put(self, request):
        try:
            group_id = request.data.get('groupId')
            selected_menu_button = request.data.get('selectedMenuButton')

            if group_id is None or selected_menu_button is None:
                return Response(public_error_response('请求参数不存在'), status.HTTP_400_BAD_REQUEST)

            # 删除用户组与所有菜单和按钮的关系
            # Django会自动删除与该对象有外键关系的所有相关对象。这是因为在数据库中，这些相关对象的存在依赖于被删除的对象。
            GroupMenu_Permission.objects.filter(group__id=group_id).delete()
        except ValueError as e:
            logger.error(f"Failed to delete user group and all menus and buttons: {str(e)}")
            return Response(public_error_response(str(e)))

        # 原始列表
        lst = selected_menu_button

        # 创建空字典存储菜单和按钮
        menu_dict = {}

        # 遍历原始列表
        for item in lst:
            if "|" in item:
                menu, button = item.split("|")  # 提取菜单和按钮
                if menu in menu_dict:
                    menu_dict[menu].append(button)  # 添加按钮到已存在的菜单
                else:
                    menu_dict[menu] = [button]  # 创建新的菜单并添加按钮
            else:
                if item not in menu_dict:
                    menu_dict[item] = []  # 单独的菜单项没有按钮

        # 提取菜单和按钮列表
        # menu_list = list(menu_dict.keys())
        # button_list = list(menu_dict.values())

        print("菜单列表:")
        print(menu_dict)

        try:
            for menuCode, buttonName in menu_dict.items():
                print(menu_dict.items())
                print(menuCode, buttonName)
                group = MyGroup.objects.get(id=group_id)
                print("group", group.name)
                menu = Menus.objects.get(menu_code=menuCode)
                print("menu", menu.menu_name)
                buttons = Button.objects.filter(button_name__in=buttonName)
                print("buttons", buttons)

                permission = GroupMenu_Permission(group=group, menu=menu)
                permission.save()
                permission.buttons.set(buttons)
        except (MyGroup.DoesNotExist, Menus.DoesNotExist, Button.DoesNotExist):
            logger.error(f"Group, menu or button does not exist")
            return Response(public_error_response('组，菜单或按钮不存在'))

        return Response(public_success_response('操作成功！'))


class MenuRoleView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def get_menu_data(self, item, group_menu_permissions):
        # 匹配一级子菜单
        children = Menus.objects.filter(p_menu_code=item.menu_code,
                                        id__in=[gmp.menu.id for gmp in group_menu_permissions])
        # 继续递归匹配是否又多级子菜单
        children_data = [self.get_menu_data(child, group_menu_permissions) for child in children]

        # 找到对应的 GroupMenu_Permission 对象
        gmp = next((gmp for gmp in group_menu_permissions if gmp.menu.id == item.id), None)
        if gmp:
            # 从 GroupMenu_Permission 对象中获取按钮权限
            buttons = [button.button_name for button in gmp.buttons.all()]
        else:
            buttons = []

        return {
            "code": item.menu_code,
            "path": item.menu_router,
            "name": item.menu_router,
            "component": item.component,
            "menuOrder": item.menu_order,
            # item.buttons 是一个按钮对象的集合，而不是一个字符串列表。
            # 需要将按钮对象的集合转换为按钮名称的列表。
            "buttons": buttons,
            "meta": {
                "title": item.menu_name,
                "icon": item.menu_icon_path,
                "if_hidden": item.if_hidden
            },
            "children": children_data or None
        }

    def get(self, request):
        group_id = request.query_params.get('groupId')
        # print("group_id", group_id)
        if group_id:
            # 获取指定的用户组
            group = MyGroup.objects.get(id=group_id)

            # 获取用户组关联的菜单和按钮
            group_menu_permissions = GroupMenu_Permission.objects.filter(group=group)

            data = []
            for gmp in group_menu_permissions:
                # 将所有顶级菜单进行递归
                if gmp.menu.p_menu_code == 'TOP_MENU':
                    data.append(self.get_menu_data(gmp.menu, group_menu_permissions))

            response_data = {
                "code": 2000,
                "status": "success",
                "msg": "操作成功！",
                "data": data
            }
            return Response(response_data)

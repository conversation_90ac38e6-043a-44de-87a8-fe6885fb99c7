from ats.public.public_class.my_modelviewset import MyModelViewSet
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.pagination import PageNumberPagination
from ..models import Dictionary
from .serializers.dictionary_serializer import DictionarySerializer

class CustomPageNumberPagination(PageNumberPagination):
    def get_page_size(self, request):
        return request.query_params.get('page_size', super().get_page_size(request))

class DictionaryView(MyModelViewSet, MyAuthentication):
    # authentication_classes = MyAuthentication.authentication_classes
    # permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = DictionarySerializer
    pagination_class = CustomPageNumberPagination  # 使用自定义的分页类

    # # 重写initial方法，跳过认证和权限检查, 给定时任务用
    # def initial(self, request, *args, **kwargs):
    #     # 检查是否是内部调用，例如检查一个特定的请求头
    #     if request.headers.get('Internal-Call') == 'True':
    #         # 如果是内部调用，跳过认证和权限检查
    #         return
    #     # 否则，执行正常的认证和权限检查
    #     super().initial(request, *args, **kwargs)

    def get_queryset(self):
        queryset = Dictionary.objects.all().order_by('create_time')
        query_dic_name = self.request.query_params.get('dic_name', None)
        query_dic_key = self.request.query_params.get('dic_key', None)
        if query_dic_name:
            queryset = queryset.filter(dic_name__icontains=query_dic_name)  # 页面用模糊查询
        if query_dic_key:
            queryset = queryset.filter(dic_key=query_dic_key) #前端用
        return queryset


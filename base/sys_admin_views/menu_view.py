from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.pagination import PageNumberPagination
from base.sys_admin_views.serializers.menu_serializer import MenusSerializer
from base.models import Menus, Button
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
import logging

logger = logging.getLogger('ats-console')

# 获取父子菜单
class MenusTreeView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = MenusSerializer
    pagination_class = PageNumberPagination

    def build_tree(self, menus, parent="TOP_MENU"):
        tree = []
        for menu in menus:
            if menu['p_menu_code'] == parent or (not menu['p_menu_code'] and not parent):
                children = self.build_tree(menus, menu['menu_code'])
                if children:
                    menu['children'] = children
                if menu['menu_type'] == 2:
                    menu['disabled'] = True
                tree.append(menu)
        return tree

    def get(self, request):
        try:
            menus = Menus.objects.values('menu_name', 'menu_code', 'menu_type', 'p_menu_code')
            menus = list(menus)
            tree = self.build_tree(menus)

            # 创建顶级菜单
            top_menu = {
                'menu_name': '顶级菜单',
                'menu_code': 'TOP_MENU',
                'children': tree  # 将原来的树作为顶级菜单的子菜单
            }

            response_data = {
                "code": 2000,
                "status": "success",
                "msg": "操作成功！",
                "data": [top_menu]  # 将顶级菜单作为数据返回
            }
            return Response(response_data)

        except Exception as e:
            logger.error(f"Failed to retrieve menus: {str(e)}")
            return Response({
                "code": 2001,
                "status": "error",
                "msg": "操作失败！",
                "data": str(e)},
                status=status.HTTP_404_NOT_FOUND)


# 菜单管理
class AllMenusView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = MenusSerializer
    pagination_class = PageNumberPagination

    def get(self, request):
        # 获取顶级菜单
        # menus = Menus.objects.filter(Q(p_menu_code__isnull=True) | Q(p_menu_code=''))
        menus = Menus.objects.filter(p_menu_code='TOP_MENU').order_by('menu_order')
        page_size = request.query_params.get('page_size', default=10)
        page_number = request.query_params.get('page', default=1)

        try:
            paginator = PageNumberPagination()
            paginator.page_size = int(page_size)
            paginator.page = int(page_number)
        except ValueError:
            logger.error("Pagination parameter format error!")
            return Response({
                "code": 2002,
                "status": "error",
                "msg": "分页参数格式错误！",
            }, status.HTTP_400_BAD_REQUEST)

        page = paginator.paginate_queryset(menus, request)
        serializer = MenusSerializer(page, many=True)

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serializer.data
        }
        return paginator.get_paginated_response(response_data)

    def post(self, request):
        serializer = MenusSerializer(data=request.data)
        if serializer.is_valid():
            # 保存菜单数据
            menu = serializer.save()
            # 获取buttons传参
            button_ids = request.data.get('buttons', [])
            # 获取这些按钮
            buttons = Button.objects.filter(id__in=button_ids)
            # 将这些按钮添加到菜单中, 维护关系表
            menu.buttons.add(*buttons)
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)


class AllMenusDetailView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = MenusSerializer
    pagination_class = PageNumberPagination

    def get(self, request, pk):
        # 查子集
        if request.query_params.get("getSub") == "true":
            try:
                menu = Menus.objects.get(pk=pk)
                children = Menus.objects.filter(p_menu_code=menu.menu_code)
                serializer = MenusSerializer(children, many=True)
                return Response(public_success_response(serializer.data))
            except Exception as e:
                return Response(public_error_response(str(e)))

        # 查当前菜单
        try:
            menu = Menus.objects.get(pk=pk)
            serializer = MenusSerializer(menu, many=False)
            return Response(public_success_response(serializer.data))
        except Exception as e:
            logger.error(f"Failed to retrieve menu details: {str(e)}")
            return Response(public_error_response(str(e)))

    def put(self, request, pk):
        try:
            menu = Menus.objects.get(pk=pk)
            # 开启局部更新，给前端菜单隐藏 partial=True
            serializer = MenusSerializer(menu, data=request.data, partial=True)
            if serializer.is_valid():
                # 保存菜单
                menu = serializer.save(creator_id="1")
                # 检查是否传递了按钮ID列表
                if 'buttons' in request.data:
                    # 获取buttons传参
                    button_ids = request.data.get('buttons', [])
                    # 获取这些按钮
                    buttons = Button.objects.filter(id__in=button_ids)
                    # 清除原有的关系
                    menu.buttons.clear()
                    # 将这些按钮添加到菜单中, 维护关系表
                    menu.buttons.add(*buttons)
            return Response(public_success_response(serializer.data))
        except Exception as e:
            logger.error(f"Failed to update menu: {str(e)}")
            return Response(public_error_response(str(e)))

    def delete(self, request, pk):
        try:
            Menus.objects.get(pk=pk).delete()
            return Response(public_success_response("删除成功！"), status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Failed to delete menu: {str(e)}")
            return Response(public_error_response(str(e)))


# 获取所有菜单和按钮权限
class ShowAllMenuRoleView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = MenusSerializer
    pagination_class = PageNumberPagination

    def get_menu_data(self, item):
        if not item.component:
            component = "#"
        else:
            component = item.component
        # 匹配一级子菜单
        children = Menus.objects.filter(p_menu_code=item.menu_code)
        # 继续递归匹配是否又多级子菜单
        children_data = [self.get_menu_data(child) for child in children]

        return {
            "code": item.menu_code,
            "path": item.menu_router,
            "name": item.menu_router,
            "component": component,
            "menuOrder": item.menu_order,
            # item.buttons 是一个按钮对象的集合，而不是一个字符串列表。
            # 需要将按钮对象的集合转换为按钮名称的列表。
            "buttons": [button.button_name for button in item.buttons.all()],
            "meta": {
                "title": item.menu_name,
                "icon": item.menu_icon_path,
                "if_hidden": item.if_hidden
            },
            "children": children_data or None
        }

    def get(self, request):
        # 获取顶级菜单
        menus = Menus.objects.filter(p_menu_code='TOP_MENU')
        # 将所有顶级菜单进行递归
        data = [self.get_menu_data(menu) for menu in menus]

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": data
        }
        return Response(response_data)




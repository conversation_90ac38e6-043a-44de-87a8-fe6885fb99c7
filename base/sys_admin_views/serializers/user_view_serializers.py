from rest_framework import serializers
from base.models import MyUser


# 用户中心get序列化器
class UserSerializer(serializers.ModelSerializer):
    group = serializers.SerializerMethodField()

    class Meta:
        model = MyUser
        fields = ['id', 'username', 'name', 'email', 'last_login', 'is_superuser', 'is_staff', 'is_active', 'date_joined',
                  'phone', 'department', 'job_title', 'avatar', 'group']

    def get_group(self, obj):
        return obj.groups.values_list('id', flat=True)


# 用户中心新增序列化器
class UserPostSerializer(serializers.ModelSerializer):
    class Meta:
        model = MyUser
        fields = ['id', 'username', 'password', 'name', 'email', 'is_superuser', 'is_staff', 'is_active', 'phone', 'department',
                  'job_title', 'avatar']
        # 序列化器在序列化输出时，忽略该字段，不包含在返回数据中
        extra_kwargs = {
            'password': {'write_only': True},
        }


# 用户中心修改序列化器
class UserPutSerializer(serializers.ModelSerializer):
    class Meta:
        model = MyUser
        fields = ['email', 'is_superuser', 'name', 'is_staff', 'is_active', 'phone', 'department', 'job_title']


class UserCenterPutSerializer(serializers.ModelSerializer):
    class Meta:
        model = MyUser
        fields = ['email', 'phone', 'name']


class UserCenterPutPasswordSerializer(serializers.ModelSerializer):
    class Meta:
        model = MyUser
        fields = ['password']

        extra_kwargs = {
            'password': {'write_only': True},
        }


class UserAvatarSerializer(serializers.Serializer):
    avatar = serializers.ImageField(max_length=None, use_url=True)

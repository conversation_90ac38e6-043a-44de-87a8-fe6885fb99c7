from base.models import Menus
from rest_framework import serializers


class MenusSerializer(serializers.ModelSerializer):
    # 以下默认返回的是ID
    #     buttons = serializers.PrimaryKeyRelatedField(many=True, queryset=Button.objects.all(), allow_empty=True,
    #                                                  required=False)

    # 这边返回id对应的name给前端渲染，重写get_buttons方法
    buttons = serializers.SerializerMethodField()

    class Meta:
        model = Menus
        fields = '__all__'

    def get_buttons(self, obj):
        buttons = obj.buttons.all()
        return [button.button_name for button in buttons]

from base.models import Dictionary
from rest_framework import serializers


class DictionarySerializer(serializers.ModelSerializer):
    class Meta:
        model = Dictionary
        fields = '__all__'
    
    def validate_dic_key(self, value):
        # 检查同名
        if self.instance is not None:
            # 编辑模式
            if self.instance.dic_key == value:
                # dic_key未改变，无需校验
                return value
        if Dictionary.objects.filter(dic_key=value).exists():
            raise serializers.ValidationError("dic_key已存在")
        return value
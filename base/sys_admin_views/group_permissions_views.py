from rest_framework import status
from rest_framework.response import Response
from rest_framework.generics import GenericAP<PERSON><PERSON>iew
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import Permission
from base.models import MyGroup
from ats.public.public_class.my_modelviewset import MyModelViewSet
from base.sys_admin_views.serializers.group_permissions_serializers import GroupSerializer, PermissionSerializer
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_error_response, public_success_response
import logging

logger = logging.getLogger('ats-console')

# 组管理-学习用GenericAPIView
class GroupsView(GenericAPIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    queryset = MyGroup.objects.all().order_by('create_time')
    serializer_class = GroupSerializer

    def get(self, request):
        try:
            page_size = request.query_params.get('page_size', default=10)
            page_number = request.query_params.get('page', default=1)
        except Exception as e:
            error_message = str(e)
            logger.error(f"Failed to retrieve user groups: {error_message}")
            return Response(public_error_response(error_message))

        try:
            paginator = PageNumberPagination()
            paginator.page_size = int(page_size)
            paginator.page = int(page_number)

        except ValueError as e:
            error_message = str(e)
            logger.error(f"Failed to paginate user groups: {error_message}")
            return Response(public_error_response(error_message), status.HTTP_400_BAD_REQUEST)

        page = paginator.paginate_queryset(self.queryset, request)
        serializer = self.get_serializer(page, many=True)

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serializer.data
        }
        return paginator.get_paginated_response(response_data)

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(public_success_response(serializer.data))
        else:
            return Response(public_error_response(serializer.errors), status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        ids = request.data.get('ids', [])
        if not ids:
            logger.error("No user ID provided!")
            return Response(public_error_response("No user ID provided!"), status=status.HTTP_400_BAD_REQUEST)
        self.queryset.filter(id__in=ids).delete()
        return Response(public_success_response("批量删除成功！"))


# 组管理-学习用GenericAPIView
class GroupsDetailView(GenericAPIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]

    queryset = MyGroup.objects.all()
    serializer_class = GroupSerializer

    def get(self, request, pk):
        serializer = self.get_serializer(instance=self.get_object(), many=False)
        return Response(public_success_response(serializer.data))

    def put(self, request, pk):
        serializer = self.get_serializer(instance=self.get_object(), data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(public_success_response(serializer.data))
        else:
            return Response(public_error_response(serializer.errors), status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        try:
            self.get_object().delete()
            return Response(public_success_response())
        except Exception as e:
            logger.error(f"Failed to delete user group: {str(e)}")
            return Response(public_error_response(str(e)), status.HTTP_400_BAD_REQUEST)


# 权限管理-封装一下MyModelViewSet，增加了一些返回字段-学习ModelViewSet
class PermissionsView(MyModelViewSet, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    pagination_class = PageNumberPagination

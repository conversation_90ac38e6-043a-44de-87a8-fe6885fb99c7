from base.sys_admin_views.serializers.button_serializer import ButtonSerializer
from base.models import Button
from ats.public.public_class.my_modelviewset import MyModelViewSet
from ats.public.public_class.myauthentication import MyAuthentication


class ButtonView(MyModelViewSet, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = ButtonSerializer
    queryset = Button.objects.all()
    pagination_class = None

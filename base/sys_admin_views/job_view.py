from rest_framework.pagination import PageNumberPagination
from base.sys_admin_views.serializers.jobposition_serializer import JobSerializer
from base.models import JobPosition
from ats.public.public_class.my_modelviewset import MyModelViewSet
from ats.public.public_class.myauthentication import MyAuthentication


class JobsView(MyModelViewSet, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = JobSerializer
    pagination_class = PageNumberPagination

    # 根据入参适配queryset
    def get_queryset(self):
        queryset = JobPosition.objects.all().order_by('create_time')
        query_job_name = self.request.query_params.get('jobNameQuery', None)
        query_all = self.request.query_params.get('all', None)
        if query_job_name is not None:
            queryset = queryset.filter(job_title__icontains=query_job_name)  # 模糊查询
        if query_all == "true":
            self.pagination_class = None  # 禁用分页
        return queryset

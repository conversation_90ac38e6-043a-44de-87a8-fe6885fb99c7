from django.contrib.auth.hashers import make_password
from django.conf import settings
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.pagination import PageNumberPagination
from rest_framework.filters import OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from base.models import MyUser, MyGroup
from ats.public.public_class.myresponse import public_success_response, public_error_response
from base.sys_admin_views.serializers.user_view_serializers import UserSerializer, UserPostSerializer, UserPutSerializer
import logging

logger = logging.getLogger('ats-console')

# 用户管理-学习基础的APIView
class UserListView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['date_joined']

    def get(self, request, pk=None):
        # get单个用户
        if pk:
            try:
                user = MyUser.objects.get(pk=pk)
                user_group = user.groups.values_list('id', flat=True)
                serializer = UserSerializer(user, many=False)
                #适配实际生产环境ipport
                full_url = f"http://{settings.SERVER_IP}:{settings.NGINX_PORT}/api/avatar/upload/{pk}/"
                # 特别匹配一下查询头像的接口，给前端方便一些
                avatar_url = full_url
                # 不改变序列化数据
                data = serializer.data.copy()
                data['avatar'] = avatar_url
                data['group'] = user_group
                return Response({
                    "code": 2000,
                    "status": "success",
                    "msg": "操作成功！",
                    "data": data
                })
            except MyUser.DoesNotExist:
                logger.error("User does not exist!")
                return Response({
                    "code": 2001,
                    "status": "error",
                    "msg": "用户不存在！"},
                    status=status.HTTP_404_NOT_FOUND)

        if request.query_params.get('userNameQuery'):
            # get搜索用户
            users = MyUser.objects.all().filter(username__contains=request.query_params.get('userNameQuery'))
        else:
            # get全部用户
            users = MyUser.objects.all().order_by(request.query_params.get('ordering', 'date_joined'))
        page_size = request.query_params.get('page_size', default=10)
        page_number = request.query_params.get('page', default=1)

        try:
            paginator = PageNumberPagination()
            paginator.page_size = int(page_size)
            paginator.page = int(page_number)
        except ValueError:
            logger.error("Pagination parameter format error!")
            return Response({
                "code": 2002,
                "status": "error",
                "msg": "分页参数格式错误！",
            }, status.HTTP_400_BAD_REQUEST)

        page = paginator.paginate_queryset(users, request)
        serializer = UserSerializer(page, many=True)

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serializer.data
        }
        return paginator.get_paginated_response(response_data)

    def post(self, request):
        serializer = UserPostSerializer(data=request.data)
        if serializer.is_valid():
            password = request.data.get('password')
            if password and password != '':
                hashed_password = make_password(password)
                serializer.validated_data['password'] = hashed_password
            serializer.save()
            group_id = request.data.get('groupId')
            username = request.data.get('username')
            if group_id and group_id != '':
                group = MyGroup.objects.get(id=group_id)
                # print("group.name", group.name)
                user = MyUser.objects.get(username=username)
                # print("user.username", user.username)
                user.groups.add(group)
                # group_ids = user.groups.values_list('id', flat=True)
                # print("group_ids", group_ids)
            return Response({
                "code": 2000,
                "status": "success",
                "msg": "用户创建成功！",
                "data": serializer.data,
            }, status=status.HTTP_200_OK)
        return Response(public_error_response(serializer.errors))

    def put(self, request, pk):
        try:
            user = MyUser.objects.get(pk=pk)
        except MyUser.DoesNotExist:
            return Response({
                "code": 2001,
                "status": "error",
                "msg": "用户不存在！"},
                status=status.HTTP_404_NOT_FOUND)

        serializer = UserPutSerializer(user, data=request.data)
        if serializer.is_valid():
            password = request.data.get('password')
            if password and password != '':
                hashed_password = make_password(password)
                serializer.validated_data['password'] = hashed_password
            serializer.save()
            group_id = request.data.get('groupId')
            if group_id and group_id != '':
                user.groups.clear()  # 删除用户组关系
                group = MyGroup.objects.get(id=group_id)
                user.groups.add(group)
                # group_ids = user.groups.values_list('id', flat=True)
            if group_id == '':
                user.groups.clear()  # 删除用户组关系
            return Response({
                "code": 2000,
                "status": "success",
                "msg": "用户信息更新成功！",
                "data": serializer.data
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk=None):
        if pk:
            # 删除单个用户
            try:
                user = MyUser.objects.get(pk=pk)
            except MyUser.DoesNotExist:
                return Response({
                    "code": 2001,
                    "status": "error",
                    "msg": "用户不存在！"},
                    status=status.HTTP_404_NOT_FOUND)
            user.delete()
            return Response({
                "code": 2000,
                "status": "success",
                "msg": "用户删除成功！",
            }, status=status.HTTP_200_OK)
        else:
            # 批量删除用户
            ids = request.data.get('ids', [])
            if not ids:
                return Response({
                    "code": 2001,
                    "status": "error",
                    "msg": "未提供用户ID！",
                }, status=status.HTTP_400_BAD_REQUEST)
            MyUser.objects.filter(id__in=ids).delete()
            return Response({
                "code": 2000,
                "status": "success",
                "msg": "用户批量删除成功！",
            }, status=status.HTTP_200_OK)

{"openapi": "3.1.0", "info": {"title": "ats", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/api/userCenterProfile/1/": {"put": {"summary": "改基本资料", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"phone": {"type": "string"}, "email": {"type": "string"}}, "required": ["phone", "email"]}, "example": {"phone": "18878787870", "email": "<EMAIL>"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"email": {"type": "string"}, "phone": {"type": "string"}, "name": {"type": "string"}}, "required": ["email", "phone", "name"]}}, "required": ["code", "status", "msg", "data"]}, "example": {"code": 2000, "status": "success", "msg": "操作成功！", "data": {"email": "<EMAIL>", "phone": "18878787870", "name": "波士顿1"}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/api/userCenterPasswordModify/1/": {"put": {"summary": "改密码", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"old_password": {"type": "string"}, "password": {"type": "string"}}, "required": ["old_password", "password"]}, "example": {"old_password": "admin888", "password": "admin888"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "string"}}, "required": ["code", "status", "msg", "data"]}, "example": {"code": 2000, "status": "success", "msg": "操作成功！", "data": "密码修改成功！"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/api/userInfo/": {"get": {"summary": "根据用户名搜索", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "page_size", "in": "query", "description": "", "required": true, "example": "10", "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "", "required": true, "example": "1", "schema": {"type": "string"}}, {"name": "userNameQuery", "in": "query", "description": "", "required": true, "example": "admin", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": "null"}, "previous": {"type": "null"}, "results": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "username": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "last_login": {"type": ["string", "null"]}, "is_superuser": {"type": "boolean"}, "is_staff": {"type": "boolean"}, "is_active": {"type": "boolean"}, "date_joined": {"type": "string"}, "phone": {"type": "string"}, "department": {"type": ["string", "null"]}, "job_title": {"type": "string"}, "avatar": {"type": "string"}, "group": {"type": "array", "items": {"type": ["integer", "null"]}}}}}}, "required": ["code", "status", "msg", "data"]}}, "required": ["count", "next", "previous", "results"]}, "example": {"count": 1, "next": null, "previous": null, "results": {"code": 2000, "status": "success", "msg": "操作成功！", "data": [{"id": 1, "username": "admin", "name": "波士顿1", "email": "<EMAIL>", "last_login": "2023-11-03T09:44:53.079442", "is_superuser": true, "is_staff": true, "is_active": true, "date_joined": "2023-10-20T17:27:31.059131", "phone": "18919119222", "department": "测试部门1", "job_title": "管理员", "avatar": "/avatar/%E8%B5%84%E6%96%99%E5%87%BA%E5%BA%93.png", "group": [1]}]}}}}, "headers": {}}}, "security": [{"bearer": []}]}, "post": {"summary": "添加用户", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}, "is_staff": {"type": "boolean"}, "phone": {"type": "string"}, "department": {"type": "string"}, "job_title": {"type": "string"}, "is_active": {"type": "integer"}, "groupId": {"type": "integer"}}, "required": ["username", "email", "name", "password", "is_staff", "phone", "department", "job_title", "is_active", "groupId"]}, "example": {"username": "gph88681ss", "email": "<EMAIL>", "name": "测试", "password": "12312131123", "is_staff": true, "phone": "19919119919", "department": "21122", "job_title": "测试", "is_active": 1, "groupId": 22}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "username": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "is_superuser": {"type": "boolean"}, "is_staff": {"type": "boolean"}, "is_active": {"type": "boolean"}, "phone": {"type": "string"}, "department": {"type": ["string", "null"]}, "job_title": {"type": "string"}, "avatar": {"type": "string"}}, "required": ["id", "username", "name", "email", "is_superuser", "is_staff", "is_active", "phone", "department", "job_title", "avatar"]}}, "required": ["code", "status", "msg", "data"]}, "example": {"code": 2000, "status": "success", "msg": "用户创建成功！", "data": {"id": 574, "username": "gph88681ss", "name": "测试", "email": "<EMAIL>", "is_superuser": false, "is_staff": true, "is_active": true, "phone": "19919119919", "department": "21122", "job_title": "测试", "avatar": "/avatar/avatar.jpeg"}}}}, "headers": {}}}, "security": [{"bearer": []}]}, "delete": {"summary": "批量删除用户", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}}}, "required": ["ids"]}, "example": {"ids": [574]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}}, "required": ["code", "status", "msg"]}, "example": {"code": 2000, "status": "success", "msg": "用户批量删除成功！"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/api/userInfo/1/": {"get": {"summary": "获取单个用户列表", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "username": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "last_login": {"type": ["string", "null"]}, "is_superuser": {"type": "boolean"}, "is_staff": {"type": "boolean"}, "is_active": {"type": "boolean"}, "date_joined": {"type": "string"}, "phone": {"type": "string"}, "department": {"type": ["string", "null"]}, "job_title": {"type": "string"}, "avatar": {"type": "string"}, "group": {"type": "array", "items": {"type": "integer"}}}, "required": ["id", "username", "name", "email", "last_login", "is_superuser", "is_staff", "is_active", "date_joined", "phone", "department", "job_title", "avatar", "group"]}}, "required": ["code", "status", "msg", "data"]}, "example": {"code": 2000, "status": "success", "msg": "操作成功！", "data": {"id": 1, "username": "admin", "name": "波士顿1", "email": "<EMAIL>", "last_login": "2023-11-03T09:44:53.079442", "is_superuser": true, "is_staff": true, "is_active": true, "date_joined": "2023-10-20T17:27:31.059131", "phone": "18919119222", "department": "测试部门1", "job_title": "管理员", "avatar": "http://127.0.0.1:8000/api/avatar/upload/1/", "group": [1]}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/api/userInfo/574/": {"put": {"summary": "修改用户", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "is_superuser": {"type": "boolean"}, "is_staff": {"type": "boolean"}, "is_active": {"type": "boolean"}, "phone": {"type": "string"}, "department": {"type": "string"}, "groupId": {"type": "integer"}}, "required": ["email", "is_superuser", "is_staff", "is_active", "phone", "department", "groupId"]}, "example": {"email": "<EMAIL>", "password": "123123", "is_superuser": true, "is_staff": true, "is_active": false, "phone": "100000", "department": "jjsgl", "groupId": 1}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"email": {"type": "string"}, "is_superuser": {"type": "boolean"}, "name": {"type": "string"}, "is_staff": {"type": "boolean"}, "is_active": {"type": "boolean"}, "phone": {"type": "string"}, "department": {"type": "string"}, "job_title": {"type": "string"}}, "required": ["email", "is_superuser", "name", "is_staff", "is_active", "phone", "department", "job_title"]}}, "required": ["code", "status", "msg", "data"]}, "example": {"code": 2000, "status": "success", "msg": "用户信息更新成功！", "data": {"email": "<EMAIL>", "is_superuser": true, "name": "测试", "is_staff": true, "is_active": false, "phone": "100000", "department": "jjsgl", "job_title": "测试"}}}}, "headers": {}}}, "security": [{"bearer": []}]}, "delete": {"summary": "删除用户单个", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}, "msg": {"type": "string"}}, "required": ["code", "status", "msg"]}, "example": {"code": 2000, "status": "success", "msg": "用户删除成功！"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/api/avatar/upload/2/": {"put": {"summary": "头像上传", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"avatar": {"type": "string", "format": "binary"}}, "required": ["avatar"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/api/avatar/upload/1/": {"get": {"summary": "头像查看", "deprecated": false, "description": "", "tags": [], "parameters": [], "responses": {"200": {"description": "", "content": {"*/*": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": [{"bearer": []}]}}}, "components": {"schemas": {}, "securitySchemes": {"bearer": {"type": "http", "scheme": "bearer"}}}, "servers": [{"url": "http://127.0.0.1:8000", "description": "本地"}], "security": [{"bearer": []}]}
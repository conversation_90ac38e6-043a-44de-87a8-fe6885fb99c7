{"openapi": "3.1.0", "info": {"title": "测试项目", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/post": {"post": {"summary": "post", "deprecated": false, "description": "### 示例接口文档\n### 示例接口文档\n### 示例接口文档\n### 示例接口文档\n### 示例接口文档", "tags": [], "parameters": [{"name": "params1", "in": "query", "description": "", "required": false, "example": "params1", "schema": {"type": "string"}}, {"name": "params2", "in": "query", "description": "", "required": false, "example": "params2", "schema": {"type": "string"}}, {"name": "header1", "in": "header", "description": "", "required": false, "example": "header1", "schema": {"type": "string"}}, {"name": "header2", "in": "header", "description": "", "required": false, "example": "header2", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"user": {"id": 123456, "name": "张三", "email": "<EMAIL>", "is_active": true, "created_at": "2025-03-18T12:00:00Z", "profile": {"age": 30, "gender": "male", "address": {"street": "中山路100号", "city": "北京", "postal_code": "100000", "country": "中国"}, "preferences": {"notifications": {"email": true, "sms": false}, "language": "zh-CN", "theme": "dark"}}, "roles": ["admin", "editor"], "metadata": null}, "orders": [{"order_id": "ORD-001", "date": "2025-03-17", "total_amount": 299.99, "currency": "CNY", "items": [{"product_id": "P-1001", "name": "无线鼠标", "quantity": 2, "price": 99.99, "attributes": {"color": "black", "wireless": true}}, {"product_id": "P-1002", "name": "机械键盘", "quantity": 1, "price": 199.99, "attributes": {"switch_type": "红轴", "backlight": "RGB"}}]}], "settings": {"timezone": "Asia/Shanghai", "backup": {"enabled": true, "frequency": "daily", "last_backup": "2025-03-16T23:59:59Z"}, "api_keys": [{"name": "main_api", "key": "abcd-1234-efgh-5678", "expires_at": "2025-12-31T23:59:59Z"}, {"name": "backup_api", "key": "ijkl-9876-mnop-5432", "expires_at": null}]}, "logs": [{"timestamp": "2025-03-18T10:00:00Z", "level": "INFO", "message": "用户登录成功"}, {"timestamp": "2025-03-18T10:30:00Z", "level": "ERROR", "message": "支付失败", "details": {"error_code": 500, "description": "服务器内部错误"}}]}}}}, "responses": {"200": {"description": "http://***************:8085/post", "content": {"application/json": {"schema": {"type": "object", "properties": {"args": {"type": "object", "properties": {"params1": {"type": "string"}, "params2": {"type": "string"}}, "required": ["params1", "params2"]}, "data": {"type": "string"}, "files": {"type": "object", "properties": {}}, "form": {"type": "object", "properties": {}}, "headers": {"type": "object", "properties": {"Accept": {"type": "string"}, "Accept-Encoding": {"type": "string"}, "Connection": {"type": "string"}, "Content-Length": {"type": "string"}, "Content-Type": {"type": "string"}, "Header1": {"type": "string"}, "Header2": {"type": "string"}, "Host": {"type": "string"}, "User-Agent": {"type": "string"}}, "required": ["Accept", "Accept-Encoding", "Connection", "Content-Length", "Content-Type", "Header1", "Header2", "Host", "User-Agent"]}, "json": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "object", "properties": {"level": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "string"}, "details": {"type": "object", "properties": {"description": {"type": "string"}, "error_code": {"type": "integer"}}, "required": ["description", "error_code"]}}, "required": ["level", "message", "timestamp"]}}, "orders": {"type": "array", "items": {"type": "object", "properties": {"currency": {"type": "string"}, "date": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"attributes": {"type": "object", "properties": {"color": {"type": "string"}, "wireless": {"type": "boolean"}, "backlight": {"type": "string"}, "switch_type": {"type": "string"}}, "required": ["backlight", "switch_type"]}, "name": {"type": "string"}, "price": {"type": "number"}, "product_id": {"type": "string"}, "quantity": {"type": "integer"}}, "required": ["attributes", "name", "price", "product_id", "quantity"]}}, "order_id": {"type": "string"}, "total_amount": {"type": "number"}}}}, "settings": {"type": "object", "properties": {"api_keys": {"type": "array", "items": {"type": "object", "properties": {"expires_at": {"type": ["string", "null"]}, "key": {"type": "string"}, "name": {"type": "string"}}, "required": ["expires_at", "key", "name"]}}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "frequency": {"type": "string"}, "last_backup": {"type": "string"}}, "required": ["enabled", "frequency", "last_backup"]}, "timezone": {"type": "string"}}, "required": ["api_keys", "backup", "timezone"]}, "user": {"type": "object", "properties": {"created_at": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "metadata": {"type": "null"}, "name": {"type": "string"}, "profile": {"type": "object", "properties": {"address": {"type": "object", "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "postal_code": {"type": "string"}, "street": {"type": "string"}}, "required": ["city", "country", "postal_code", "street"]}, "age": {"type": "integer"}, "gender": {"type": "string"}, "preferences": {"type": "object", "properties": {"language": {"type": "string"}, "notifications": {"type": "object", "properties": {"email": {"type": "boolean"}, "sms": {"type": "boolean"}}, "required": ["email", "sms"]}, "theme": {"type": "string"}}, "required": ["language", "notifications", "theme"]}}, "required": ["address", "age", "gender", "preferences"]}, "roles": {"type": "array", "items": {"type": "string"}}}, "required": ["created_at", "email", "id", "is_active", "metadata", "name", "profile", "roles"]}}, "required": ["logs", "orders", "settings", "user"]}, "origin": {"type": "string"}, "url": {"type": "string"}}, "required": ["args", "data", "files", "form", "headers", "json", "origin", "url"]}, "examples": {"1": {"summary": "成功示例", "value": {"args": {"params1": "params1", "params2": "params2"}, "data": "{\n  \"user\": {\n    \"id\": 123456,\n    \"name\": \"张三\",\n    \"email\": \"<EMAIL>\",\n    \"is_active\": true,\n    \"created_at\": \"2025-03-18T12:00:00Z\",\n    \"profile\": {\n      \"age\": 30,\n      \"gender\": \"male\",\n      \"address\": {\n        \"street\": \"中山路100号\",\n        \"city\": \"北京\",\n        \"postal_code\": \"100000\",\n        \"country\": \"中国\"\n      },\n      \"preferences\": {\n        \"notifications\": {\n          \"email\": true,\n          \"sms\": false\n        },\n        \"language\": \"zh-CN\",\n        \"theme\": \"dark\"\n      }\n    },\n    \"roles\": [\"admin\", \"editor\"],\n    \"metadata\": null\n  },\n  \"orders\": [\n    {\n      \"order_id\": \"ORD-001\",\n      \"date\": \"2025-03-17\",\n      \"total_amount\": 299.99,\n      \"currency\": \"CNY\",\n      \"items\": [\n        {\n          \"product_id\": \"P-1001\",\n          \"name\": \"无线鼠标\",\n          \"quantity\": 2,\n          \"price\": 99.99,\n          \"attributes\": {\n            \"color\": \"black\",\n            \"wireless\": true\n          }\n        },\n        {\n          \"product_id\": \"P-1002\",\n          \"name\": \"机械键盘\",\n          \"quantity\": 1,\n          \"price\": 199.99,\n          \"attributes\": {\n            \"switch_type\": \"红轴\",\n            \"backlight\": \"RGB\"\n          }\n        }\n      ]\n    }\n  ],\n  \"settings\": {\n    \"timezone\": \"Asia/Shanghai\",\n    \"backup\": {\n      \"enabled\": true,\n      \"frequency\": \"daily\",\n      \"last_backup\": \"2025-03-16T23:59:59Z\"\n    },\n    \"api_keys\": [\n      {\n        \"name\": \"main_api\",\n        \"key\": \"abcd-1234-efgh-5678\",\n        \"expires_at\": \"2025-12-31T23:59:59Z\"\n      },\n      {\n        \"name\": \"backup_api\",\n        \"key\": \"ijkl-9876-mnop-5432\",\n        \"expires_at\": null\n      }\n    ]\n  },\n  \"logs\": [\n    {\n      \"timestamp\": \"2025-03-18T10:00:00Z\",\n      \"level\": \"INFO\",\n      \"message\": \"用户登录成功\"\n    },\n    {\n      \"timestamp\": \"2025-03-18T10:30:00Z\",\n      \"level\": \"ERROR\",\n      \"message\": \"支付失败\",\n      \"details\": {\n        \"error_code\": 500,\n        \"description\": \"服务器内部错误\"\n      }\n    }\n  ]\n}", "files": {}, "form": {}, "headers": {"Accept": "*/*", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Length": "2036", "Content-Type": "application/json", "Header1": "header1", "Host": "***************:8085", "User-Agent": "Apifox/1.0.0 (https://apifox.com)"}, "json": {"logs": [{"level": "INFO", "message": "用户登录成功", "timestamp": "2025-03-18T10:00:00Z"}, {"details": {"description": "服务器内部错误", "error_code": 500}, "level": "ERROR", "message": "支付失败", "timestamp": "2025-03-18T10:30:00Z"}], "orders": [{"currency": "CNY", "date": "2025-03-17", "items": [{"attributes": {"color": "black", "wireless": true}, "name": "无线鼠标", "price": 99.99, "product_id": "P-1001", "quantity": 2}, {"attributes": {"backlight": "RGB", "switch_type": "红轴"}, "name": "机械键盘", "price": 199.99, "product_id": "P-1002", "quantity": 1}], "order_id": "ORD-001", "total_amount": 299.99}], "settings": {"api_keys": [{"expires_at": "2025-12-31T23:59:59Z", "key": "abcd-1234-efgh-5678", "name": "main_api"}, {"expires_at": null, "key": "ijkl-9876-mnop-5432", "name": "backup_api"}], "backup": {"enabled": true, "frequency": "daily", "last_backup": "2025-03-16T23:59:59Z"}, "timezone": "Asia/Shanghai"}, "user": {"created_at": "2025-03-18T12:00:00Z", "email": "<EMAIL>", "id": 123456, "is_active": true, "metadata": null, "name": "张三", "profile": {"address": {"city": "北京", "country": "中国", "postal_code": "100000", "street": "中山路100号"}, "age": 30, "gender": "male", "preferences": {"language": "zh-CN", "notifications": {"email": true, "sms": false}, "theme": "dark"}}, "roles": ["admin", "editor"]}}, "origin": "*************", "url": "http://***************:8085/post?params1=params1&params2=params2"}}, "2": {"summary": "成功示例", "value": {"data": [], "status": "error"}}}}}, "headers": {}}, "500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string"}}, "required": ["data", "status"]}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [{"url": "http://***************:8085", "description": "测试环境"}], "security": []}
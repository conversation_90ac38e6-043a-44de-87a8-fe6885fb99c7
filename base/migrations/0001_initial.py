# Generated by Django 4.2.5 on 2025-03-11 23:39

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='MyUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('name', models.CharField(blank=True, db_comment='姓名', default='', max_length=20, null=True)),
                ('phone', models.CharField(blank=True, db_comment='手机号', max_length=20, null=True)),
                ('department', models.CharField(blank=True, db_comment='部门', max_length=50, null=True)),
                ('job_title', models.CharField(blank=True, db_comment='岗位', max_length=50, null=True)),
                ('avatar', models.ImageField(blank=True, default='avatar/avatar.jpeg', null=True, upload_to='avatar')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Button',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('button_code', models.CharField(db_comment='按钮编码', max_length=50)),
                ('button_name', models.CharField(db_comment='按钮名称', max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Dictionary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dic_name', models.CharField(db_comment='字典名', max_length=100)),
                ('dic_key', models.CharField(db_comment='字典键', max_length=100)),
                ('dic_value', models.TextField(db_comment='字典值')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
            ],
        ),
        migrations.CreateModel(
            name='JobPosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_title', models.CharField(blank=True, db_comment='岗位名', max_length=100, null=True)),
                ('sort_order', models.IntegerField(blank=True, db_comment='排序', null=True)),
                ('if_enable', models.IntegerField(blank=True, db_comment='1:启用; 0:禁用', default=1, null=True)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
            ],
        ),
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_name', models.CharField(db_comment='用户名', max_length=100)),
                ('login_time', models.DateTimeField(auto_now_add=True, db_comment='登录时间')),
                ('login_ip', models.CharField(db_comment='登录IP', max_length=100)),
                ('login_status', models.IntegerField(db_comment='1:登陆成功; 0:登陆失败', default=1)),
            ],
        ),
        migrations.CreateModel(
            name='Menus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('menu_code', models.CharField(db_comment='菜单编码', max_length=50)),
                ('menu_name', models.CharField(db_comment='菜单名称', max_length=100)),
                ('p_menu_code', models.CharField(blank=True, db_comment='父级菜单code', default=None, max_length=50, null=True)),
                ('component', models.CharField(blank=True, db_comment='前端组件名称', default='#', max_length=200, null=True)),
                ('menu_type', models.IntegerField(db_comment='1:文件夹; 2:菜单')),
                ('menu_router', models.CharField(db_comment='菜单路由', max_length=255)),
                ('menu_icon_path', models.CharField(db_comment='菜单图标路径', max_length=255)),
                ('if_enable', models.IntegerField(db_comment='1:启用; 0:禁用', default=1)),
                ('if_hidden', models.IntegerField(db_comment='1:显示; 0:隐藏', default=1)),
                ('menu_order', models.IntegerField(db_comment='排序', default=1)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('creator_id', models.CharField(db_comment='创建人', default=1, max_length=20)),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
                ('buttons', models.ManyToManyField(to='base.button')),
            ],
        ),
        migrations.CreateModel(
            name='MyGroup',
            fields=[
                ('group_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='auth.group')),
                ('desc', models.CharField(blank=True, db_comment='描述', max_length=200, null=True)),
                ('sort_order', models.IntegerField(blank=True, db_comment='排序', null=True)),
                ('if_enable', models.IntegerField(blank=True, db_comment='1:启用; 0:禁用', default=1, null=True)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
            ],
            bases=('auth.group',),
            managers=[
                ('objects', django.contrib.auth.models.GroupManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserMenu_Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('buttons', models.ManyToManyField(to='base.button')),
                ('menu', models.ForeignKey(db_comment='菜单', on_delete=django.db.models.deletion.CASCADE, to='base.menus')),
                ('user', models.ForeignKey(db_comment='用户', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='GroupMenu_Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('buttons', models.ManyToManyField(to='base.button')),
                ('group', models.ForeignKey(db_comment='用户组', on_delete=django.db.models.deletion.CASCADE, to='base.mygroup')),
                ('menu', models.ForeignKey(db_comment='菜单', on_delete=django.db.models.deletion.CASCADE, to='base.menus')),
            ],
        ),
    ]

from django.contrib.auth.models import AbstractUser, Group
from django.db import models


# 继承源Userbase类，并添加字段
class MyUser(AbstractUser):
    name = models.CharField(max_length=20, null=True, blank=True, default='', db_comment='姓名')
    phone = models.CharField(max_length=20, null=True, blank=True, db_comment='手机号')
    department = models.CharField(max_length=50, null=True, blank=True, db_comment='部门')
    job_title = models.CharField(max_length=50, null=True, blank=True, db_comment='岗位')
    avatar = models.ImageField(upload_to='avatar', null=True, blank=True, default='avatar/avatar.jpeg')


class Button(models.Model):
    button_code = models.CharField(max_length=50, db_comment='按钮编码')
    button_name = models.CharField(max_length=100, db_comment='按钮名称')


class Menus(models.Model):
    menu_code = models.Char<PERSON>ield(max_length=50, db_comment='菜单编码')
    menu_name = models.CharField(max_length=100, db_comment='菜单名称')
    p_menu_code = models.CharField(max_length=50, null=True, default=None, blank=True, db_comment='父级菜单code')
    component = models.CharField(max_length=200, null=True, blank=True, default='#', db_comment='前端组件名称')
    menu_type = models.IntegerField(db_comment='1:文件夹; 2:菜单')
    menu_router = models.CharField(max_length=255, db_comment='菜单路由')
    menu_icon_path = models.CharField(max_length=255, db_comment='菜单图标路径')
    if_enable = models.IntegerField(default=1, db_comment='1:启用; 0:禁用')
    if_hidden = models.IntegerField(default=1, db_comment='1:显示; 0:隐藏')
    menu_order = models.IntegerField(default=1, db_comment='排序')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    creator_id = models.CharField(default=1, max_length=20, db_comment='创建人')
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')
    # 多对多
    buttons = models.ManyToManyField(Button)


class JobPosition(models.Model):
    job_title = models.CharField(max_length=100, null=True, blank=True, db_comment='岗位名')
    sort_order = models.IntegerField(null=True, blank=True, db_comment='排序')
    if_enable = models.IntegerField(default=1, null=True, blank=True, db_comment='1:启用; 0:禁用')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')


# 扩展一下group表，后面直接用这张，也包含原来的Group中的name
# 虽然name字段实际上存储在auth_group表中，但是你可以像访问MyGroup模型的本地字段一样访问它
class MyGroup(Group):
    # 添加你需要的自定义字段
    desc = models.CharField(max_length=200, null=True, blank=True, db_comment='描述')
    sort_order = models.IntegerField(null=True, blank=True, db_comment='排序')
    if_enable = models.IntegerField(default=1, null=True, blank=True, db_comment='1:启用; 0:禁用')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')


# 用户组菜单绑定权限
class GroupMenu_Permission(models.Model):
    group = models.ForeignKey(MyGroup, on_delete=models.CASCADE, db_comment='用户组')
    menu = models.ForeignKey(Menus, on_delete=models.CASCADE, db_comment='菜单')
    buttons = models.ManyToManyField(Button)


# 用户直接绑定菜单权限
class UserMenu_Permission(models.Model):
    user = models.ForeignKey(MyUser, on_delete=models.CASCADE, db_comment='用户')
    menu = models.ForeignKey(Menus, on_delete=models.CASCADE, db_comment='菜单')
    buttons = models.ManyToManyField(Button)

# 字典表
class Dictionary(models.Model):
    dic_name = models.CharField(max_length=100, db_comment='字典名')
    dic_key = models.CharField(max_length=100, db_comment='字典键')
    dic_value = models.TextField(db_comment='字典值')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')

class LoginLog(models.Model):
    user_name = models.CharField(max_length=100, db_comment='用户名')
    login_time = models.DateTimeField(auto_now_add=True, db_comment='登录时间')
    login_ip = models.CharField(max_length=100, db_comment='登录IP')
    login_status = models.IntegerField(default=1, db_comment='1:登陆成功; 0:登陆失败')

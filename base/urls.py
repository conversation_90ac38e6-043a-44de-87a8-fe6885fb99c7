from django.urls import path, re_path
from . import views
from base.sys_admin_views import user_view, group_permissions_views, job_view, menu_view, user_avatar_view, button_view,\
    group_menu_button_views, dictionary_view
from base.sys_admin_views.user_center_views import user_profile_modify

urlpatterns = [
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('login_log/', views.LoginLogView.as_view({'get': 'list'}), name='login_log'),
    # 用户中心的增删改查
    re_path(r'^userInfo/(?:(?P<pk>\d+)/)?$', user_view.UserListView.as_view(), name='user_info'),
    path('groups/', group_permissions_views.GroupsView.as_view(), name='group_info'),
    re_path(r'groups/(?:(?P<pk>\d+)/)?$', group_permissions_views.GroupsDetailView.as_view(), name='group_detail_info'),
    path('permissions/',
         group_permissions_views.PermissionsView.as_view({"get": "list", "post": "create", "delete": "destroys"}),
         name='permission_info'),
    re_path(r'permissions/(?:(?P<pk>\d+)/)?$',
            group_permissions_views.PermissionsView.as_view({"get": "retrieve", "put": "update", "delete": "destroy"}),
            name='permission_detail_info'),
    path('jobs/',
         job_view.JobsView.as_view({"get": "list", "post": "create", "delete": "destroys"}),
         name='jobs'),
    re_path(r'jobs/(?:(?P<pk>\d+)/)?$',
            job_view.JobsView.as_view({"get": "retrieve", "put": "update", "delete": "destroy"}),
            name='jobs_detail'),
    path('allMenuButtons/', menu_view.ShowAllMenuRoleView.as_view(), name='all_menu_button'),
    path('menus/', menu_view.AllMenusView.as_view(), name='all_menus'),
    path('menuTree/', menu_view.MenusTreeView.as_view(), name='menu_tree'),
    re_path(r'menus/(?:(?P<pk>\d+)/)?$', menu_view.AllMenusDetailView.as_view(), name='get_children_menus'),
    re_path(r'avatar/upload/(?:(?P<pk>\d+)/)?$', user_avatar_view.UserAvatarUploadView.as_view(), name='avatar-upload'),
    re_path(r'userCenterProfile/(?:(?P<pk>\d+)/)?$', user_profile_modify.UserCenterProfileDetail.as_view(),
            name='user_center_profile_modify'),
    re_path(r'userCenterPasswordModify/(?:(?P<pk>\d+)/)?$', user_profile_modify.UserCenterPasswordModify.as_view(),
            name='user_center_password_modify'),
    path('button/',
         button_view.ButtonView.as_view({"get": "list", "post": "create", "delete": "destroys"}),
         name='button'),
    re_path(r'button/(?:(?P<pk>\d+)/)?$',
            button_view.ButtonView.as_view({"get": "retrieve", "put": "update", "delete": "destroy"}),
            name='button_detail'),
    path('groupAddMenuButton/', group_menu_button_views.GroupAddMenuButtonPermissions.as_view(),
         name='group_add_menu_button'),
    path('menuRole/', group_menu_button_views.MenuRoleView.as_view(), name='menu_role'),
    path('dictionary/',
         dictionary_view.DictionaryView.as_view({"get": "list", "post": "create", "delete": "destroys"})),
    re_path(r'dictionary/(?:(?P<pk>\d+)/)?$',
            dictionary_view.DictionaryView.as_view({"get": "retrieve", "put": "update", "delete": "destroy"})),
]

from django.urls import path, re_path
from .project_views.main_views import project_views, environment_views, project_members_views, \
    project_public_data_views, environment_data_view, environment_data_batch_view, environment_url_view, \
    environment_auth_view, default_project_views, project_openai_config_views, data_source_views

urlpatterns = [
    path('', project_views.ProjectListView.as_view(), name="project"),
    re_path(r'^(?:(?P<pk>\d+)/)?$', project_views.ProjectListDetailView.as_view(), name="project_detail"),
    path('environment/', environment_views.EnvironmentListView.as_view(), name="environment"),
    re_path(r'^environment/(?:(?P<pk>\d+)/)?$', environment_views.EnvironmentListDetailView.as_view(),
            name="environment_detail"),
    path('members/', project_members_views.Project_Members_Views.as_view(), name="project_members"),
    path('publicData/',
         project_public_data_views.ProjectPublicDataView.as_view({"get": "list", "post": "create", "delete": "destroys"}),
         name='public_data'),
    re_path(r'publicData/(?:(?P<pk>\d+)/)?$',
            project_public_data_views.ProjectPublicDataView.as_view({"get": "retrieve", "put": "update", "delete": "destroy"}),
            name='public_data_detail'),
    path('environmentData/', environment_data_view.EnvironmentDataListView.as_view(), name='environment_data'),
    re_path(r'^environmentData/(?:(?P<pk>\d+)/)?$', environment_data_view.EnvironmentDataDetailView.as_view(),
            name="environment_data_detail"),
    path('environmentDataBatch/', environment_data_batch_view.BatchEnvironmentDataView.as_view(), name='environment_data_batch'),
    path('environmentUrl/',environment_url_view.EnvironmentUrlView.as_view({"get": "list", "post": "create", "delete": "destroys"}),
         name='env_url'),
    re_path(r'environmentUrl/(?:(?P<pk>\d+)/)?$',environment_url_view.EnvironmentUrlView.as_view({"get": "retrieve", "put": "update", "delete": "destroy"}),
            name='env_url_detail'),
    path('environmentAuth/',
         environment_auth_view.EnvironmentUrlView.as_view({"get": "list", "post": "create", "delete": "destroys"}),
         name='env_url'),
    re_path(r'environmentAuth/(?:(?P<pk>\d+)/)?$',
            environment_auth_view.EnvironmentUrlView.as_view({"get": "retrieve", "put": "update", "delete": "destroy"}),
            name='env_url_detail'),
    path('defaultProject/<str:username>/', default_project_views.DefaultProjectDetailView.as_view()),
    path('defaultProject/', default_project_views.DefaultProjectView.as_view()),
    path('projectMembers/', project_members_views.Project_Members_Views.as_view()),
    path('openai-configs/', project_openai_config_views.OpenaiConfigView.as_view(), name='openai-config-list'),
    path('openai-configs/<int:pk>/', project_openai_config_views.OpenaiConfigDetailView.as_view(), name='openai-config-detail'),
    
    # 数据源相关路由
    path('datasources/', data_source_views.DataSourceListView.as_view(), name='datasource-list'),
    path('datasources/<int:pk>/', data_source_views.DataSourceDetailView.as_view(), name='datasource-detail'),
    path('datasources/test-connection/', data_source_views.DataSourceTestConnectionView.as_view(), name='datasource-test-connection'),
    path('datasources/<int:pk>/test-connection/', data_source_views.DataSourceTestByIdView.as_view(), name='datasource-test-by-id'),
    path('datasources/<int:pk>/execute-sql/', data_source_views.DataSourceExecuteSqlView.as_view(), name='datasource-execute-sql'),
]

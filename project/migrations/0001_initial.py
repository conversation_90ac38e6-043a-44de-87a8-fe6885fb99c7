# Generated by Django 4.2.5 on 2025-03-11 23:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Environment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='环境名称', max_length=100)),
                ('creator', models.Char<PERSON>ield(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('desc', models.Char<PERSON>ield(blank=True, db_comment='描述', max_length=200, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='项目名称', max_length=100)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('desc', models.CharField(blank=True, db_comment='描述', max_length=200, null=True)),
                ('members', models.ManyToManyField(blank=True, related_name='projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'project',
            },
        ),
        migrations.CreateModel(
            name='Switch_Projet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(db_comment='用户名', max_length=20)),
                ('project', models.ForeignKey(db_comment='默认项目', null=True, on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'default_project',
            },
        ),
        migrations.CreateModel(
            name='Public_Data',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='公共数据名', max_length=100)),
                ('type', models.CharField(db_comment='公共数据类型;1:变量;2:basic_auth认证;3:bearer_token认证;4:base_url;', max_length=1)),
                ('variable_type', models.CharField(db_comment='变量类型;1:string;2:int;3:数组;4:boolean;5:object;6:number', max_length=1, null=True)),
                ('desc', models.CharField(blank=True, db_comment='公共数据说明', max_length=255, null=True)),
                ('value', models.CharField(db_comment='公共数据内容', max_length=500)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='public_data', to='project.project')),
            ],
        ),
        migrations.CreateModel(
            name='Environment_Url',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.CharField(blank=True, db_comment='环境域名', max_length=100, null=True)),
                ('desc', models.CharField(blank=True, db_comment='描述', max_length=100, null=True)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('environment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='project.environment')),
            ],
        ),
        migrations.CreateModel(
            name='Environment_Data',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_comment='环境变量名', max_length=100)),
                ('type', models.CharField(db_comment='环境数据类型;1:变量;', max_length=1)),
                ('variable_type', models.CharField(db_comment='变量类型;1:string;2:int;3:数组;4:boolean;5:object;6:number', max_length=1, null=True)),
                ('desc', models.CharField(blank=True, db_comment='公共数据说明', max_length=255, null=True)),
                ('value', models.CharField(db_comment='环境变量值', max_length=500)),
                ('value_generate', models.CharField(blank=True, db_comment='环境变量生成方式', max_length=50, null=True)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('environment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='project.environment')),
            ],
        ),
        migrations.CreateModel(
            name='Environment_Auth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('auth_username', models.CharField(blank=True, db_comment='账号', max_length=100, null=True)),
                ('type', models.CharField(db_comment='类型;1:BASIC_AUTH;2:token', max_length=1)),
                ('auth_password', models.CharField(blank=True, db_comment='密码', max_length=500, null=True)),
                ('token_value', models.CharField(blank=True, db_comment='token值', max_length=500, null=True)),
                ('desc', models.CharField(blank=True, db_comment='备注', max_length=255, null=True)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('environment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='project.environment')),
            ],
        ),
        migrations.AddField(
            model_name='environment',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='environments', to='project.project'),
        ),
    ]

# Generated by Django 4.2.5 on 2025-04-17 15:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Openai_Config',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(db_comment='名称', max_length=100)),
                ('type', models.<PERSON>r<PERSON><PERSON>(db_comment='类型;1:api_test;2:ui_test', max_length=1)),
                ('value', models.J<PERSON><PERSON>ield(blank=True, db_comment='值', null=True)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('project', models.ForeignKey(db_comment='项目', null=True, on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'openai_config',
                'db_table_comment': 'openai配置表',
            },
        ),
    ]

# Generated by Django 4.2.5 on 2025-07-09 10:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0003_alter_public_data_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='DataSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(db_comment='数据源名称', max_length=100)),
                ('database_type', models.CharField(choices=[('mysql', 'MySQL'), ('postgresql', 'PostgreSQL')], db_comment='数据库类型', max_length=20)),
                ('host', models.Char<PERSON>ield(db_comment='主机地址', max_length=200)),
                ('port', models.PositiveIntegerField(db_comment='端口号')),
                ('database_name', models.<PERSON>r<PERSON><PERSON>(db_comment='数据库名称', max_length=100)),
                ('username', models.CharField(db_comment='用户名', max_length=100)),
                ('password', models.CharField(db_comment='密码', max_length=500)),
                ('charset', models.CharField(db_comment='字符集', default='utf8mb4', max_length=20)),
                ('desc', models.CharField(blank=True, db_comment='描述', max_length=500, null=True)),
                ('is_active', models.BooleanField(db_comment='是否启用', default=True)),
                ('creator', models.CharField(blank=True, db_comment='创建人', max_length=100, null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
                ('project', models.ForeignKey(db_comment='所属项目', on_delete=django.db.models.deletion.CASCADE, related_name='data_sources', to='project.project')),
            ],
            options={
                'db_table': 'data_source',
                'db_table_comment': '数据源表',
                'unique_together': {('name', 'project')},
            },
        ),
    ]

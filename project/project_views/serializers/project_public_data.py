from project.models import Public_Data
from rest_framework import serializers
import json

class ProjectPublicDataSerializer(serializers.ModelSerializer):
    value = serializers.JSONField(allow_null=True)

    class Meta:
        model = Public_Data
        fields = '__all__'

    def to_internal_value(self, data):
        value = data.get('value')
        if isinstance(value, (dict, list)):
            data['value'] = json.dumps(value, ensure_ascii=False)
        return super().to_internal_value(data)

    def validate(self, data):
        request_method = self.context['request'].method
        project_id = data.get('project')
        type = data.get('type')
        key_name = data.get('key')
        if request_method == 'POST' and project_id and type in ['2', '3']:
            if Public_Data.objects.filter(project_id=project_id, type__in=[2, 3]).exists():
                raise serializers.ValidationError("已存在认证类型")
        
        if request_method in ['PUT', 'POST'] and key_name:
            # 获取当前实例（如果是PUT请求）
            instance = self.instance if request_method == 'PUT' else None
            
            # 检查是否存在同名key，排除当前实例
            existing = Public_Data.objects.filter(project_id=project_id, key=key_name)
            if instance:
                existing = existing.exclude(pk=instance.pk)
            
            if existing.exists():
                raise serializers.ValidationError("已存在同名key")
        return data

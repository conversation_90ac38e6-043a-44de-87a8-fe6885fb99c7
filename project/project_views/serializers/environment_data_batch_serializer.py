from rest_framework import serializers
from project.models import Environment_Data, Environment_Url, Environment_Auth


class EnvironmentBatchDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = Environment_Data
        exclude = ['environment']


class EnvironmentBatchUrlSerializer(serializers.ModelSerializer):
    class Meta:
        model = Environment_Url
        exclude = ['environment']


class EnvironmentBatchAuthSerializer(serializers.ModelSerializer):
    class Meta:
        model = Environment_Auth
        exclude = ['environment']


class TabSerializer(serializers.Serializer):
    tabId = serializers.CharField()
    items = serializers.ListField()

    def validate(self, attrs):
        tabId = attrs['tabId']
        items = attrs['items']

        if tabId == 'data':
            serializer_class = EnvironmentBatchDataSerializer
        elif tabId == 'url':
            serializer_class = EnvironmentBatchUrlSerializer
        elif tabId == 'auth':
            serializer_class = EnvironmentBatchAuthSerializer
        else:
            raise serializers.ValidationError('错误的tabId')

        for item in items:
            serializer = serializer_class(data=item)
            if not serializer.is_valid():
                raise serializers.ValidationError(serializer.errors)

        return attrs


class BatchUploadSerializer(serializers.Serializer):
    data = TabSerializer(many=True)

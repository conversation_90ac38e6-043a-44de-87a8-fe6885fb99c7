from project.models import Openai_Config
from rest_framework import serializers


class ProjectOpenaiConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = Openai_Config
        fields = '__all__'  # 不能同时设置fields和exclude
        # exclude = ['value']


class ProjectOpenaiConfigDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Openai_Config
        fields = '__all__'
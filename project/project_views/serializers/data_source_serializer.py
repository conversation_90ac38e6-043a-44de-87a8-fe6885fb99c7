from rest_framework import serializers
from project.models import DataSource, Project


class DataSourceSerializer(serializers.ModelSerializer):
    """数据源序列化器"""
    project_name = serializers.CharField(source='project.name', read_only=True)
    
    class Meta:
        model = DataSource
        fields = [
            'id', 'name', 'database_type', 'host', 'port', 'database_name',
            'username', 'password', 'charset', 'desc', 'is_active',
            'project', 'project_name', 'creator', 'create_time', 'update_time'
        ]
        read_only_fields = ['id', 'creator', 'create_time', 'update_time', 'project_name']
    
    def validate_port(self, value):
        """验证端口号"""
        if not (1 <= value <= 65535):
            raise serializers.ValidationError("端口号必须在1-65535之间")
        return value
    
    def validate_name(self, value):
        """验证数据源名称在项目内的唯一性"""
        project_id = self.context.get('project_id')
        if project_id:
            existing = DataSource.objects.filter(
                name=value, 
                project_id=project_id
            ).exclude(
                id=self.instance.id if self.instance else None
            )
            if existing.exists():
                raise serializers.ValidationError("当前项目下已存在同名数据源")
        return value
    
    def validate(self, attrs):
        """整体验证"""
        # 根据数据库类型设置默认端口
        if 'database_type' in attrs and 'port' not in attrs:
            if attrs['database_type'] == 'mysql':
                attrs['port'] = 3306
            elif attrs['database_type'] == 'postgresql':
                attrs['port'] = 5432
        return attrs


class DataSourceTestConnectionSerializer(serializers.Serializer):
    """数据源连接测试序列化器"""
    database_type = serializers.ChoiceField(choices=DataSource.DATABASE_TYPES)
    host = serializers.CharField(max_length=200)
    port = serializers.IntegerField(min_value=1, max_value=65535)
    database_name = serializers.CharField(max_length=100)
    username = serializers.CharField(max_length=100)
    password = serializers.CharField(max_length=500)
    charset = serializers.CharField(max_length=20, default='utf8mb4')


class DataSourceListSerializer(serializers.ModelSerializer):
    """数据源列表序列化器（不包含敏感信息）"""
    project_name = serializers.CharField(source='project.name', read_only=True)
    
    class Meta:
        model = DataSource
        fields = [
            'id', 'name', 'database_type', 'host', 'port', 'database_name',
            'username', 'password', 'charset', 'desc', 'is_active',
            'project', 'project_name', 'creator', 'create_time', 'update_time'
        ]
        # 列表时不显示密码 
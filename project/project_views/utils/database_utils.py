import pymysql
import psycopg2
import logging
from django.db import connections
from django.conf import settings
from project.models import DataSource

logger = logging.getLogger('ats-console')


class DatabaseConnectionManager:
    """数据库连接管理器"""
    
    @staticmethod
    def test_connection(database_type, host, port, database_name, username, password, charset='utf8mb4'):
        """
        测试数据库连接
        
        Args:
            database_type: 数据库类型 (mysql/postgresql)
            host: 主机地址
            port: 端口
            database_name: 数据库名
            username: 用户名
            password: 密码
            charset: 字符集
            
        Returns:
            dict: {"success": bool, "message": str}
        """
        try:
            if database_type == 'mysql':
                return DatabaseConnectionManager._test_mysql_connection(
                    host, port, database_name, username, password, charset
                )
            elif database_type == 'postgresql':
                return DatabaseConnectionManager._test_postgresql_connection(
                    host, port, database_name, username, password
                )
            else:
                return {"success": False, "message": f"不支持的数据库类型: {database_type}"}
        except Exception as e:
            logger.error(f"数据库连接测试失败: {str(e)}")
            return {"success": False, "message": f"连接测试失败: {str(e)}"}
    
    @staticmethod
    def _test_mysql_connection(host, port, database_name, username, password, charset):
        """测试MySQL连接"""
        try:
            connection = pymysql.connect(
                host=host,
                port=int(port),
                user=username,
                password=password,
                database=database_name,
                charset=charset,
                connect_timeout=10
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
            connection.close()
            
            if result:
                return {"success": True, "message": "MySQL连接测试成功"}
            else:
                return {"success": False, "message": "MySQL连接测试失败"}
                
        except pymysql.Error as e:
            error_msg = f"MySQL连接错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @staticmethod
    def _test_postgresql_connection(host, port, database_name, username, password):
        """测试PostgreSQL连接"""
        try:
            connection = psycopg2.connect(
                host=host,
                port=int(port),
                user=username,
                password=password,
                database=database_name,
                connect_timeout=10
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
            connection.close()
            
            if result:
                return {"success": True, "message": "PostgreSQL连接测试成功"}
            else:
                return {"success": False, "message": "PostgreSQL连接测试失败"}
                
        except psycopg2.Error as e:
            error_msg = f"PostgreSQL连接错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @staticmethod
    def get_connection(datasource_id):
        """
        根据数据源ID获取数据库连接
        
        Args:
            datasource_id: 数据源ID
            
        Returns:
            connection: 数据库连接对象
        """
        try:
            datasource = DataSource.objects.get(id=datasource_id, is_active=True)
            
            if datasource.database_type == 'mysql':
                return pymysql.connect(
                    host=datasource.host,
                    port=datasource.port,
                    user=datasource.username,
                    password=datasource.password,
                    database=datasource.database_name,
                    charset=datasource.charset,
                    cursorclass=pymysql.cursors.DictCursor  # 返回字典格式结果
                )
            elif datasource.database_type == 'postgresql':
                return psycopg2.connect(
                    host=datasource.host,
                    port=datasource.port,
                    user=datasource.username,
                    password=datasource.password,
                    database=datasource.database_name
                )
            else:
                raise ValueError(f"不支持的数据库类型: {datasource.database_type}")
                
        except DataSource.DoesNotExist:
            raise ValueError(f"数据源不存在或已禁用: {datasource_id}")
    
    @staticmethod
    def execute_sql(datasource_id, sql, params=None):
        """
        执行SQL语句
        
        Args:
            datasource_id: 数据源ID
            sql: SQL语句
            params: 参数
            
        Returns:
            dict: {"success": bool, "data": list, "message": str, "affected_rows": int}
        """
        connection = None
        try:
            datasource = DataSource.objects.get(id=datasource_id, is_active=True)
            connection = DatabaseConnectionManager.get_connection(datasource_id)
            
            # 格式化SQL语句，处理保留关键字
            formatted_sql = DatabaseConnectionManager.format_sql_for_database(sql, datasource.database_type)
            
            with connection.cursor() as cursor:
                if datasource.database_type == 'mysql':
                    cursor.execute(formatted_sql, params)
                    if sql.strip().upper().startswith('SELECT'):
                        data = cursor.fetchall()
                        return {
                            "success": True,
                            "data": data,
                            "message": "查询执行成功",
                            "affected_rows": len(data)
                        }
                    else:
                        connection.commit()
                        return {
                            "success": True,
                            "data": [],
                            "message": "SQL执行成功",
                            "affected_rows": cursor.rowcount
                        }
                        
                elif datasource.database_type == 'postgresql':
                    cursor.execute(formatted_sql, params)
                    if sql.strip().upper().startswith('SELECT'):
                        columns = [desc[0] for desc in cursor.description]
                        data = [dict(zip(columns, row)) for row in cursor.fetchall()]
                        return {
                            "success": True,
                            "data": data,
                            "message": "查询执行成功",
                            "affected_rows": len(data)
                        }
                    else:
                        connection.commit()
                        return {
                            "success": True,
                            "data": [],
                            "message": "SQL执行成功",
                            "affected_rows": cursor.rowcount
                        }
                        
        except Exception as e:
            error_msg = f"SQL执行失败: {str(e)}"
            logger.error(error_msg)
            if connection:
                connection.rollback()
            return {
                "success": False,
                "data": [],
                "message": error_msg,
                "affected_rows": 0
            }
        finally:
            if connection:
                connection.close()
    
    @staticmethod
    def format_sql_for_database(sql, database_type):
        """
        根据数据库类型格式化SQL语句，处理保留关键字
        
        Args:
            sql: 原始SQL语句
            database_type: 数据库类型 (mysql/postgresql)
            
        Returns:
            str: 格式化后的SQL语句
        """
        # 常见的保留关键字
        common_keywords = {
            'key', 'order', 'group', 'desc', 'asc', 'index', 'table', 'database', 
            'user', 'password', 'host', 'port', 'name', 'type', 'value', 'status',
            'create', 'update', 'delete', 'drop', 'alter', 'insert', 'select',
            'from', 'where', 'and', 'or', 'not', 'in', 'like', 'between', 'is',
            'null', 'true', 'false', 'limit', 'offset', 'count', 'sum', 'avg',
            'max', 'min', 'distinct', 'as', 'join', 'left', 'right', 'inner',
            'outer', 'on', 'having', 'union', 'all', 'any', 'some', 'exists'
        }
        
        if database_type == 'mysql':
            quote_char = '`'
        elif database_type == 'postgresql':
            quote_char = '"'
        else:
            return sql
        
        # 简单的关键字处理：在SELECT子句中处理字段名
        import re
        
        def quote_keywords_in_select(match):
            """在SELECT子句中为关键字添加引号"""
            select_clause = match.group(1)
            # 分割字段名（按逗号分割）
            fields = [field.strip() for field in select_clause.split(',')]
            quoted_fields = []
            
            for field in fields:
                # 处理字段别名 (field AS alias)
                if ' as ' in field.lower():
                    parts = field.split(' as ', 1)
                    field_name = parts[0].strip()
                    alias = parts[1].strip()
                    
                    # 检查字段名是否为关键字
                    if field_name.lower() in common_keywords:
                        field_name = f"{quote_char}{field_name}{quote_char}"
                    
                    quoted_fields.append(f"{field_name} AS {alias}")
                else:
                    # 简单字段名
                    if field.lower() in common_keywords:
                        field = f"{quote_char}{field}{quote_char}"
                    quoted_fields.append(field)
            
            return f"SELECT {', '.join(quoted_fields)} FROM"
        
        # 处理SELECT子句
        sql = re.sub(r'\bSELECT\s+(.*?)\s+FROM', quote_keywords_in_select, sql, flags=re.IGNORECASE)
        
        return sql 
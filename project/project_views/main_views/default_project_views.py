from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from project.models import Switch_Projet
from ..serializers.default_project_serializer import DefaultProjectSerializer
import logging

logger = logging.getLogger('ats-console')


class DefaultProjectView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def post(self, request):
        data = request.data
        serializer = DefaultProjectSerializer(data=data)
        if serializer.is_valid():
            username = data['username']
            try:
                default_project = Switch_Projet.objects.get(username=username)
                default_project.delete()
                logger.info(f"Deleted default project for user {username}")
            except Exception as e:
                logger.error(f"Failed to delete default project for user {username}: {e}")
            serializer.save()
            return Response(public_success_response(serializer.data))
        logger.error(f"Serializer validation failed with errors: {serializer.errors}")
        return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)


class DefaultProjectDetailView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def get(self, request, username):
        try:
            default_project = Switch_Projet.objects.get(username=username)
        except Switch_Projet.DoesNotExist:
            logger.warning(f"No default project found for user {username}")
            return Response(public_error_response('该用户没有设置默认项目'))
        except Exception as e:
            logger.error(f"Error get project for user {username}: {e}")
            return Response(public_error_response(str(e)))
        serializer = DefaultProjectSerializer(default_project, many=False)
        return Response(public_success_response(serializer.data))

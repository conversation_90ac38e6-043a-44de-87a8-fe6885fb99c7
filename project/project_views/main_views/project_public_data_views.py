from rest_framework.exceptions import NotFound
from ats.public.public_class.my_modelviewset import MyModelViewSet
from ats.public.public_class.myauthentication import MyAuthentication
from ..serializers.project_public_data import ProjectPublicDataSerializer
from ...models import Public_Data


class ProjectPublicDataView(MyModelViewSet, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = ProjectPublicDataSerializer
    pagination_class = None

    # 根据入参适配queryset
    def get_queryset(self):
        if self.request.method == 'PUT' or self.request.method == 'DELETE':
            query_project_id = self.request.data.get('project', None)
        else:
            query_project_id = self.request.query_params.get('project_id', None)

        if query_project_id is not None:
            return Public_Data.objects.filter(project_id=query_project_id)
        else:
            raise NotFound('project_id未传值')

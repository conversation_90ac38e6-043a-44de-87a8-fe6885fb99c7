from rest_framework.views import APIView
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from rest_framework.response import Response
from rest_framework import status
from ..serializers.environment_data_batch_serializer import BatchUploadSerializer
from ...models import Environment_Url, Environment_Data, Environment_Auth, Environment

class BatchEnvironmentDataView(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    def post(self, request):
        env_id = request.data.get('envId')
        data = request.data.get('data')
        serializer = BatchUploadSerializer(data={"data": data})
        # print(request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            print(serializer.validated_data)
            # 映射模型类
            model_map = {
                "data": Environment_Data,
                "url": Environment_Url,
                "auth": Environment_Auth
            }
            for tab in data['data']:
                tab_id = tab['tabId']
                env_id = env_id
                # 获取环境实例
                environment = Environment.objects.get(id=env_id)
                model_class = model_map.get(tab_id)
                if model_class:
                    # 获取当前环境下所有的相关对象的ID
                    existing_ids = set(model_class.objects.filter(environment=environment).values_list('id', flat=True))
                    print("existing_ids", existing_ids)
                    for item in tab['items']:
                        # 获取数据的ID
                        data_id = item.pop('id', None)
                        # 前端传-1代表新增
                        if data_id and data_id != -1:
                            # 如果ID存在，更新数据
                            model_class.objects.filter(id=data_id).update(environment=environment, **item)
                            # 从existing_ids中移除该ID
                            existing_ids.discard(data_id)
                        else:
                            # 如果ID不存在，创建新的数据
                            # 如果已经存在关联记录，跳过创建
                            if (model_class == Environment_Url or model_class == Environment_Auth) \
                                    and model_class.objects.filter(environment=environment).exists():
                                continue
                            model_class.objects.create(environment=environment, **item)
                    # 删除existing_ids中剩下的对象
                    model_class.objects.filter(id__in=existing_ids).delete()
            return Response(public_success_response('批量保存成功！'))
        return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)



# 纯新增方式
# class BatchEnvironmentDataView(MyAuthentication, APIView):
#     def post(self, request):
#         env_id = request.data.get('envId')
#         data = request.data.get('data')
#         serializer = BatchUploadSerializer(data={"data": data})
#         print(request.data)
#         if serializer.is_valid():
#             data = serializer.validated_data
#             # 映射模型类
#             model_map = {
#                 "data": Environment_Data,
#                 "url": Environment_Url
#             }
#             for tab in data['data']:
#                 tabId = tab['tabId']
#                 envId = env_id
#                 # 获取环境实例
#                 environment = Environment.objects.get(id=envId)
#                 model_class = model_map.get(tabId)
#                 if model_class:
#                     # 删除当前环境下所有的相关对象
#                     model_class.objects.filter(environment=environment).delete()
#                     for item in tab['items']:
#                         # 创建新的数据
#                         model_class.objects.create(environment=environment, **item)
#             return Response(public_success_response('批量保存成功！'))
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from project.models import Openai_Config
from ..serializers.project_openai_config_serializer import ProjectOpenaiConfigSerializer, ProjectOpenaiConfigDetailSerializer
import logging

logger = logging.getLogger('ats-console')


class OpenaiConfigView(MyAuthentication, APIView):
    """OpenAI配置视图类 - 支持列表获取和创建"""
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def get(self, request):
        """获取所有OpenAI配置"""
        try:
            project_id = request.query_params.get('project_id')
            if not project_id:
                logger.warning("获取OpenAI配置列表失败: project_id 参数缺失")
                return Response(public_error_response("project_id 参数必传"))
                
            configs = Openai_Config.objects.filter(Q(project_id=project_id) | Q(id=1))
            serializer = ProjectOpenaiConfigSerializer(configs, many=True)
            return Response(public_success_response(serializer.data))
        except Exception as e:
            logger.error(f"获取OpenAI配置列表失败: {str(e)}")
            return Response(public_error_response(str(e)))

    def post(self, request):
        """创建新的OpenAI配置"""
        serializer = ProjectOpenaiConfigSerializer(data=request.data)
        if serializer.is_valid():
            try:
                serializer.save()
                logger.info(f"创建OpenAI配置成功: {serializer.data}")
                return Response(public_success_response(serializer.data))
            except Exception as e:
                logger.error(f"创建OpenAI配置失败: {str(e)}")
                return Response(public_error_response(str(e)))
        logger.error(f"OpenAI配置数据验证失败: {serializer.errors}")
        return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)


class OpenaiConfigDetailView(MyAuthentication, APIView):
    """OpenAI配置详情视图类 - 支持获取、更新和删除单个配置"""
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def get_object(self, pk):
        try:
            return Openai_Config.objects.get(pk=pk)
        except Openai_Config.DoesNotExist:
            return None

    def get(self, request, pk):
        """获取单个OpenAI配置"""
        config = self.get_object(pk)
        if not config:
            logger.warning(f"OpenAI配置不存在，ID: {pk}")
            return Response(public_error_response("配置不存在"))
        
        serializer = ProjectOpenaiConfigDetailSerializer(config)
        return Response(public_success_response(serializer.data))

    def put(self, request, pk):
        """更新OpenAI配置"""
        config = self.get_object(pk)
        if not config:
            logger.warning(f"要更新的OpenAI配置不存在，ID: {pk}")
            return Response(public_error_response("配置不存在"))

        serializer = ProjectOpenaiConfigSerializer(config, data=request.data)
        if serializer.is_valid():
            try:
                serializer.save()
                logger.info(f"更新OpenAI配置成功，ID: {pk}")
                return Response(public_success_response(serializer.data))
            except Exception as e:
                logger.error(f"更新OpenAI配置失败，ID: {pk}, 错误: {str(e)}")
                return Response(public_error_response(str(e)))
        logger.error(f"OpenAI配置数据验证失败: {serializer.errors}")
        return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        """删除OpenAI配置"""
        config = self.get_object(pk)
        if not config:
            logger.warning(f"要删除的OpenAI配置不存在，ID: {pk}")
            return Response(public_error_response("配置不存在"))

        try:
            config.delete()
            logger.info(f"删除OpenAI配置成功，ID: {pk}")
            return Response(public_success_response("删除成功"))
        except Exception as e:
            logger.error(f"删除OpenAI配置失败，ID: {pk}, 错误: {str(e)}")
            return Response(public_error_response(str(e)))

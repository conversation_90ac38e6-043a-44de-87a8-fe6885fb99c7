from rest_framework.response import Response
from rest_framework import status
from project.models import Project
from base.models import MyUser
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from ats.public.public_class.myauthentication import MyAuthentication
from base.sys_admin_views.serializers.user_view_serializers import UserSerializer
from ats.public.public_class.myresponse import public_success_response, public_error_response
import logging

logger = logging.getLogger('ats-console')

class Project_Members_Views(MyAuthentication, APIView):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def get(self, request):
        exist_members = [int(x) for x in request.query_params.get('exist_members', '').split(',') if x.isdigit()]
        users = MyUser.objects.all().exclude(id__in=exist_members)
        query_name = request.query_params.get('query_name', '')
        if query_name:
            users = users.filter(name__icontains=query_name)
        page_size = request.query_params.get('page_size', default=10)
        page_number = request.query_params.get('page', default=1)

        try:
            paginator = PageNumberPagination()
            paginator.page_size = int(page_size)
            paginator.page = int(page_number)
        except ValueError:
            logger.error("Pagination parameter format error!")
            return Response(public_error_response('分页参数格式错误！'), status=status.HTTP_400_BAD_REQUEST)

        page = paginator.paginate_queryset(users, request)
        serializer = UserSerializer(page, many=True)
        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serializer.data
        }
        return paginator.get_paginated_response(response_data)

    def post(self, request):
        project_id = request.data.get('project_id')
        members_ids = request.data.get('members_ids')
        # print(members_ids)
        if project_id is None:
            return Response(public_error_response('project_id未传值'), status=status.HTTP_400_BAD_REQUEST)
        project = Project.objects.get(id=project_id)
        # print(project.name)
        for member_id in members_ids:
            try:
                MyUser.objects.get(id=member_id)
            except MyUser.DoesNotExist:
                logger.error(f"User with ID {member_id} not found")
                return Response(public_error_response(f'用户id:{member_id}不存在'), status=status.HTTP_400_BAD_REQUEST)
        project.members.add(*members_ids)
        logger.info(f"Added members to project {project_id}: {members_ids}")
        return Response(public_success_response('添加成功！'))

    def delete(self, request):
        project_id = request.data.get('project_id')
        members_ids = request.data.get('members_ids')

        if project_id is None:
            return Response(public_error_response('project_id未传值'), status=status.HTTP_400_BAD_REQUEST)
        project = Project.objects.get(id=project_id)

        project.members.remove(*members_ids)
        return Response(public_success_response('移除成功！'))

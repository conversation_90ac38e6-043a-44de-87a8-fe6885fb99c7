from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.filters import OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from project.models import Environment
from project.project_views.serializers.environment_serializer import EnvironmentSerializer

import logging

logger = logging.getLogger('ats-console')


class EnvironmentListView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    pagination_class = PageNumberPagination
    serializer_class = EnvironmentSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['create_time']

    # 全量环境查看
    def get(self, request):
        # projects = Project.objects.filter(members__id=user_id)
        project_id = request.query_params.get('project_id')
        # print("project_id", project_id)
        if project_id:
            environment = Environment.objects.all().filter(project_id=project_id).order_by('create_time')
        else:
            return Response(public_error_response('project_id未传值'))

        serializer = self.serializer_class(environment, many=True)

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serializer.data
        }
        return Response(response_data)

    def post(self, request):
        try:
            serializer = self.serializer_class(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
            else:
                return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Failed to create environment: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)


class EnvironmentListDetailView(APIView, MyAuthentication):
    serializer_class = EnvironmentSerializer

    # 用户组成员查看
    def get(self, request, pk):
        try:
            environment = Environment.objects.get(pk=pk)
            serializer = self.serializer_class(instance=environment, many=False)
            return Response({
                "code": 2000,
                "status": "success",
                "msg": "操作成功！",
                "data": serializer.data,
            }, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Failed to retrieve environment for ID {pk}: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk):
        try:
            environment = Environment.objects.get(pk=pk)
            serializer = self.serializer_class(instance=environment, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Failed to update environment for ID {pk}: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        try:
            environment = Environment.objects.get(pk=pk)
        except Environment.DoesNotExist:
            logger.error(f"Environment with ID {pk} not found")
            return Response({
                "code": 2001,
                "status": "error",
                "msg": "环境不存在！"},
                status=status.HTTP_404_NOT_FOUND)
        environment.delete()
        return Response(public_success_response('环境删除成功'), status=status.HTTP_200_OK)

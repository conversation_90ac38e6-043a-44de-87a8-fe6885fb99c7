import logging
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.filters import OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q

from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from project.models import DataSource, Project
from project.project_views.serializers.data_source_serializer import (
    DataSourceSerializer, 
    DataSourceListSerializer, 
    DataSourceTestConnectionSerializer
)
from project.project_views.utils.database_utils import DatabaseConnectionManager

logger = logging.getLogger('ats-console')


class DataSourceListView(APIView, MyAuthentication):
    """数据源列表视图"""
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    pagination_class = PageNumberPagination
    serializer_class = DataSourceListSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['create_time', 'name']

    def get(self, request):
        """获取数据源列表"""
        try:
            # 获取查询参数
            project_id = request.query_params.get('project_id')
            database_type = request.query_params.get('database_type')
            search_name = request.query_params.get('search_name', '')
            is_active = request.query_params.get('is_active')
            
            if not project_id:
                return Response(public_error_response('项目ID不能为空'), status=status.HTTP_400_BAD_REQUEST)
            
            # 基础查询
            queryset = DataSource.objects.filter(project_id=project_id)
            
            # 条件过滤
            if database_type:
                queryset = queryset.filter(database_type=database_type)
            
            if search_name:
                queryset = queryset.filter(
                    Q(name__icontains=search_name) | 
                    Q(desc__icontains=search_name)
                )
            
            if is_active is not None:
                queryset = queryset.filter(is_active=is_active.lower() == 'true')
            
            # 排序
            queryset = queryset.order_by('-create_time')
            
            # 分页
            page_size = int(request.query_params.get('page_size', 10))
            page_number = int(request.query_params.get('page', 1))
            
            paginator = PageNumberPagination()
            paginator.page_size = page_size
            paginator.page = page_number
            
            page = paginator.paginate_queryset(queryset, request)
            serializer = self.serializer_class(page, many=True)
            
            response_data = {
                "code": 2000,
                "status": "success",
                "msg": "操作成功",
                "data": serializer.data
            }
            return paginator.get_paginated_response(response_data)
            
        except Exception as e:
            logger.error(f"获取数据源列表失败: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """创建数据源"""
        try:
            # 获取项目ID并验证项目存在
            project_id = request.data.get('project')
            if not project_id:
                return Response(public_error_response('项目ID不能为空'), status=status.HTTP_400_BAD_REQUEST)
            
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                return Response(public_error_response('项目不存在'), status=status.HTTP_400_BAD_REQUEST)
            
            # 序列化数据
            serializer = DataSourceSerializer(
                data=request.data, 
                context={'project_id': project_id}
            )
            
            if serializer.is_valid():
                # 设置创建人
                serializer.validated_data['creator'] = request.user.username
                data_source = serializer.save()
                
                logger.info(f"用户 {request.user.username} 创建了数据源: {data_source.name}")
                return Response(public_success_response(serializer.data), status=status.HTTP_201_CREATED)
            else:
                return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"创建数据源失败: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DataSourceDetailView(APIView, MyAuthentication):
    """数据源详情视图"""
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    serializer_class = DataSourceSerializer

    def get(self, request, pk):
        """获取数据源详情"""
        try:
            data_source = DataSource.objects.get(pk=pk)
            serializer = self.serializer_class(data_source)
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
            
        except DataSource.DoesNotExist:
            return Response(public_error_response('数据源不存在'), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"获取数据源详情失败: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, pk):
        """更新数据源"""
        try:
            data_source = DataSource.objects.get(pk=pk)
            serializer = self.serializer_class(
                data_source, 
                data=request.data, 
                context={'project_id': data_source.project_id}
            )
            
            if serializer.is_valid():
                serializer.save()
                logger.info(f"用户 {request.user.username} 更新了数据源: {data_source.name}")
                return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
            else:
                return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
                
        except DataSource.DoesNotExist:
            return Response(public_error_response('数据源不存在'), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"更新数据源失败: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk):
        """删除数据源"""
        try:
            data_source = DataSource.objects.get(pk=pk)
            data_source_name = data_source.name
            data_source.delete()
            
            logger.info(f"用户 {request.user.username} 删除了数据源: {data_source_name}")
            return Response(public_success_response('数据源删除成功'), status=status.HTTP_200_OK)
            
        except DataSource.DoesNotExist:
            return Response(public_error_response('数据源不存在'), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"删除数据源失败: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DataSourceTestConnectionView(APIView, MyAuthentication):
    """数据源连接测试视图"""
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    serializer_class = DataSourceTestConnectionSerializer

    def post(self, request):
        """测试数据源连接"""
        try:
            serializer = self.serializer_class(data=request.data)
            
            if serializer.is_valid():
                result = DatabaseConnectionManager.test_connection(
                    database_type=serializer.validated_data['database_type'],
                    host=serializer.validated_data['host'],
                    port=serializer.validated_data['port'],
                    database_name=serializer.validated_data['database_name'],
                    username=serializer.validated_data['username'],
                    password=serializer.validated_data['password'],
                    charset=serializer.validated_data.get('charset', 'utf8mb4')
                )
                
                if result['success']:
                    logger.info(f"用户 {request.user.username} 测试数据源连接成功")
                    return Response({
                        "code": 2000,
                        "status": "success",
                        "msg": result['message'],
                        "data": {"connection_test": True}
                    }, status=status.HTTP_200_OK)
                else:
                    logger.warning(f"用户 {request.user.username} 测试数据源连接失败: {result['message']}")
                    return Response({
                        "code": 2001,
                        "status": "error",
                        "msg": result['message'],
                        "data": {"connection_test": False}
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            else:
                return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"测试数据源连接异常: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DataSourceTestByIdView(APIView, MyAuthentication):
    """通过ID测试已存在数据源连接"""
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes

    def post(self, request, pk):
        """通过数据源ID测试连接"""
        try:
            data_source = DataSource.objects.get(pk=pk)
            
            result = DatabaseConnectionManager.test_connection(
                database_type=data_source.database_type,
                host=data_source.host,
                port=data_source.port,
                database_name=data_source.database_name,
                username=data_source.username,
                password=data_source.password,
                charset=data_source.charset
            )
            
            if result['success']:
                logger.info(f"用户 {request.user.username} 测试数据源 {data_source.name} 连接成功")
                return Response({
                    "code": 2000,
                    "status": "success",
                    "msg": result['message'],
                    "data": {"connection_test": True}
                }, status=status.HTTP_200_OK)
            else:
                logger.warning(f"用户 {request.user.username} 测试数据源 {data_source.name} 连接失败: {result['message']}")
                return Response({
                    "code": 2001,
                    "status": "error",
                    "msg": result['message'],
                    "data": {"connection_test": False}
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except DataSource.DoesNotExist:
            return Response(public_error_response('数据源不存在'), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"测试数据源连接异常: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DataSourceExecuteSqlView(APIView, MyAuthentication):
    """数据源SQL执行视图"""
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes

    def post(self, request, pk):
        """执行SQL语句"""
        try:
            data_source = DataSource.objects.get(pk=pk)
            sql = request.data.get('sql', '').strip()
            
            if not sql:
                return Response(public_error_response('SQL语句不能为空'), status=status.HTTP_400_BAD_REQUEST)
            
            # 安全检查：禁止危险操作
            dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE']
            if any(keyword in sql.upper() for keyword in dangerous_keywords):
                logger.warning(f"用户 {request.user.username} 尝试执行危险SQL: {sql}")
                return Response({
                    "code": 2001,
                    "status": "error",
                    "msg": "为了安全考虑，当前只支持SELECT查询语句",
                    "data": {}
                }, status=status.HTTP_400_BAD_REQUEST)
            
            result = DatabaseConnectionManager.execute_sql(pk, sql)
            
            if result['success']:
                logger.info(f"用户 {request.user.username} 在数据源 {data_source.name} 中执行SQL成功")
                return Response({
                    "code": 2000,
                    "status": "success",
                    "msg": result['message'],
                    "data": {
                        "result": result['data'],
                        "affected_rows": result['affected_rows']
                    }
                }, status=status.HTTP_200_OK)
            else:
                logger.error(f"用户 {request.user.username} 在数据源 {data_source.name} 中执行SQL失败: {result['message']}")
                return Response({
                    "code": 2001,
                    "status": "error",
                    "msg": result['message'],
                    "data": {}
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except DataSource.DoesNotExist:
            return Response(public_error_response('数据源不存在'), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"执行SQL异常: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_500_INTERNAL_SERVER_ERROR)

from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.my_modelviewset import MyModelViewSet
from ...models import Environment_Auth
from ..serializers.environment_auth_serializer import EnvironmentAuthSerializer


class EnvironmentUrlView(MyModelViewSet, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    pagination_class = None
    serializer_class = EnvironmentAuthSerializer

    def get_queryset(self):
        queryset = Environment_Auth.objects.all()
        env_id = self.request.query_params.get('environment_id', None)
        if env_id:
            queryset = queryset.filter(environment_id=env_id)
            return queryset
        return queryset

import json
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.filters import OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from project.models import Project, Environment
from base.models import MyUser
from api_test.models import Api_Tree_Folder, Scene_Tree_Folder, Case_Tree_Folder
from ui_test.models import UiTestTreeFolder
from project.project_views.serializers.project_serializer import ProjectSerializer
import logging

logger = logging.getLogger('ats-console')


class ProjectListView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    pagination_class = PageNumberPagination
    serializer_class = ProjectSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['create_time']

    # 全量项目查看
    def get(self, request):
        # print("request.query_params", request.query_params)
        # 使用正确的参数名从查询字符串中获取user_id
        user_id = request.query_params.get('user_id')  # 修改这里
        group_id_list_str = request.query_params.get('group_id')
        # 确保group_id_list_str不为空
        if group_id_list_str:
            group_id_list_str = f'[{group_id_list_str}]'
            group_id_list = json.loads(group_id_list_str)
        else:
            group_id_list = []
        projec_query_name = request.query_params.get('project_name', default='')
        # 超级管理员用户组 查全量
        if 1 in group_id_list:
            if projec_query_name != '':
                projects = Project.objects.all().filter(name__contains=projec_query_name).order_by('create_time')
            else:
                projects = Project.objects.all().order_by('create_time')
        elif projec_query_name != '':
            projects = Project.objects.filter(members__id=user_id).filter(name__contains=projec_query_name) \
                .order_by('create_time')
        else:
            projects = Project.objects.filter(members__id=user_id).order_by('create_time')
        page_size = request.query_params.get('page_size', default=10)
        page_number = request.query_params.get('page', default=1)

        try:
            paginator = PageNumberPagination()
            paginator.page_size = int(page_size)
            paginator.page = int(page_number)
        except ValueError:
            return Response({
                "code": 2002,
                "status": "error",
                "msg": "分页参数格式错误！",
            }, status.HTTP_400_BAD_REQUEST)

        page = paginator.paginate_queryset(projects, request)
        serializer = self.serializer_class(page, many=True)
        serialized_data = serializer.data.copy()

        # 遍历序列化结果，查询每个项目的用户名称
        for item in serialized_data:
            item['member_info'] = []
            for userId in item['members']:
                user = MyUser.objects.get(id=userId)
                item['member_info'].append(
                    {"member_id": userId, 'member_username': user.username, 'member_name': user.name,
                     'member_job': user.job_title})

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serialized_data
        }
        return paginator.get_paginated_response(response_data)

    def post(self, request):
        try:
            serializer = self.serializer_class(data=request.data)
            if serializer.is_valid():
                project = serializer.save()
                # 创建一个顶级目录给接口那边树
                Api_Tree_Folder.objects.create(name='顶级目录', type='1', project=project)
                case_tree = Case_Tree_Folder.objects.create(name='顶级目录', type='1', project=project)
                Scene_Tree_Folder.objects.create(name='顶级目录', type='1', project=project)
                # 创建 AI 生成目录，并将 parent_id 设为刚刚创建的顶级目录的 id
                Case_Tree_Folder.objects.create(name='AI生成', type='1', project=project, parent_id=case_tree.id)
                UiTestTreeFolder.objects.create(name='顶级目录', type='1', project=project)

                return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
            else:
                return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Failed to create project: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)


class ProjectListDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    serializer_class = ProjectSerializer

    # 用户组成员查看
    def get(self, request, pk):
        try:
            project = Project.objects.get(pk=pk)
            print("project.name", project.name)
            serializer = self.serializer_class(instance=project, many=False)
            data = serializer.data.copy()

            # 遍历序列化结果，查询每个项目的用户名称
            data['user_info'] = []
            for userId in data['members']:
                user = MyUser.objects.get(id=userId)
                data['user_info'].append(
                    {"member_id": userId, 'member_username': user.username, 'member_name': user.name,
                     'member_job': user.job_title})

            # 取环境数量
            count_environments = Environment.objects.filter(project=project).count()
            # 取创建人的姓名
            creator = MyUser.objects.filter(username=project.creator).first()
            creator_name = creator.name if creator else None
            creator_username = serializer.data.get('creator')
            data['creator'] = f'{creator_username}({creator_name})'
            data['count_members'] = len(serializer.data.get('members'))
            data['count_environments'] = count_environments

            return Response({
                "code": 2000,
                "status": "success",
                "msg": "操作成功！",
                "data": data,
            }, status=status.HTTP_200_OK)
        except Project.DoesNotExist:
            return Response(public_error_response('项目不存在！'), status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk):
        try:
            project = Project.objects.get(pk=pk)
            serializer = self.serializer_class(instance=project, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Failed to update project: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        try:
            project = Project.objects.get(pk=pk)
        except Project.DoesNotExist:
            logger.error(f"Project with ID {pk} not found")
            return Response({
                "code": 2001,
                "status": "error",
                "msg": "项目不存在！"},
                status=status.HTTP_404_NOT_FOUND)
        project.delete()
        return Response(public_success_response('项目删除成功'), status=status.HTTP_200_OK)

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.filters import OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from project.models import Environment_Data, Environment_Url, Environment_Auth
from project.project_views.serializers.environment_data_serializer import EnvironmentDataSerializer
from project.project_views.serializers.environment_auth_serializer import EnvironmentAuthSerializer
import logging

logger = logging.getLogger('ats-console')


class EnvironmentDataListView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    pagination_class = PageNumberPagination
    serializer_class = EnvironmentDataSerializer
    auth_serializer_class = EnvironmentAuthSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['create_time']

    def get(self, request):
        environment_id = request.query_params.get('environment_id')
        environment_data = Environment_Data.objects.filter(environment_id=environment_id).order_by('create_time')
        environment_url = Environment_Url.objects.filter(environment_id=environment_id).first()
        environment_auth = Environment_Auth.objects.filter(environment_id=environment_id).first()
        if environment_url is None:
            url = None
        else:
            url = environment_url.url
            logger.info(f"Retrieved URL for environment {environment_id}: {url}")

        if environment_auth is None:
            auth_data = None
        else:
            auth_data = self.auth_serializer_class(instance=environment_auth, many=False)
            auth_data = auth_data.data
            logger.info(f"Authentication data retrieved for environment {environment_id}")

        page_size = request.query_params.get('page_size', default=10)
        page_number = request.query_params.get('page', default=1)

        try:
            paginator = PageNumberPagination()
            paginator.page_size = int(page_size)
            paginator.page = int(page_number)
        except ValueError:
            return Response({
                "code": 2002,
                "status": "error",
                "msg": "分页参数格式错误！",
            }, status.HTTP_400_BAD_REQUEST)

        page = paginator.paginate_queryset(environment_data, request)
        serializer = self.serializer_class(page, many=True)

        response_data = {
            "code": 2000,
            "status": "success",
            "msg": "操作成功！",
            "data": serializer.data,
            "url": url,
            "auth": auth_data
        }
        return paginator.get_paginated_response(response_data)

    def post(self, request):
        try:
            serializer = self.serializer_class(data=request.data)
            # print("data11111111111111111",request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
            else:
                return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Failed to create environment data: {str(e)}")  # Error: 创建数据失败日志
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)


class EnvironmentDataDetailView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = EnvironmentDataSerializer

    def get(self, request, pk):
        try:
            environment_data = Environment_Data.objects.get(pk=pk)
            serializer = self.serializer_class(instance=environment_data, many=False)
            environment_url = Environment_Url.objects.filter(environment_id=pk).first()
            if environment_url is None:
                url = None
            else:
                url = environment_url.url
            response_data = {
                "data": serializer.data,
                "url": url
            }
            return Response(public_success_response(response_data))
        except Exception as e:
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk):
        try:
            environment_data = Environment_Data.objects.get(pk=pk)
            print("environment_data.key",environment_data.key)
            serializer = self.serializer_class(instance=environment_data, data=request.data)
            if pk != request.data.get('environment'):
                return Response(public_error_response('不可修改其他环境的数据！'))
            if serializer.is_valid():
                serializer.save()
                return Response(public_success_response(serializer.data), status=status.HTTP_200_OK)
            else:
                return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Failed to update environment data for ID {pk}: {str(e)}")
            return Response(public_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        try:
            environment_data = Environment_Data.objects.get(pk=pk)
            environment_data.delete()
            return Response(public_success_response('环境数据删除成功'))
        except Exception as e:
            logger.error(f"Failed to delete environment data for ID {pk}: {str(e)}")
            return Response(public_error_response(str(e)))


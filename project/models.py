from django.db import models

from base.models import MyUser


class Environment(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100, db_comment='环境名称')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    desc = models.CharField(max_length=200, null=True, blank=True, db_comment='描述')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='environments')


class Project(models.Model):
    name = models.Char<PERSON>ield(max_length=100, db_comment='项目名称')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    desc = models.CharField(max_length=200, null=True, blank=True, db_comment='描述')
    members = models.ManyToManyField(MyUser, blank=True, related_name='projects')

    class Meta:
        db_table = 'project'


class Public_Data(models.Model):
    key = models.CharField(max_length=100, db_comment='公共数据名')
    type = models.CharField(max_length=1,
                            db_comment='公共数据类型;1:变量;2:basic_auth认证;3:bearer_token认证;4:base_url;5:ui_test')
    variable_type = models.CharField(max_length=1, null=True, db_comment='变量类型;1:string;2:int;3:数组;4:boolean;5:object;6:number;7:sql')
    desc = models.CharField(max_length=255, null=True, blank=True, db_comment='公共数据说明')
    value = models.CharField(max_length=500, db_comment='公共数据内容')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='public_data')

    def __str__(self):
        return f"项目数据：{self.key}={self.value}"


class Environment_Data(models.Model):
    key = models.CharField(max_length=100, db_comment='环境变量名')
    type = models.CharField(max_length=1,
                            db_comment='环境数据类型;1:变量;')
    variable_type = models.CharField(max_length=1, null=True, db_comment='变量类型;1:string;2:int;3:数组;4:boolean;5:object;6:number')
    desc = models.CharField(max_length=255, null=True, blank=True, db_comment='公共数据说明')
    value = models.CharField(max_length=500, db_comment='环境变量值')
    value_generate = models.CharField(max_length=50, null=True, blank=True, db_comment='环境变量生成方式')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    environment = models.ForeignKey('Environment', on_delete=models.CASCADE)


class Environment_Url(models.Model):
    url = models.CharField(max_length=100, null=True, blank=True, db_comment='环境域名')
    desc = models.CharField(max_length=100, null=True, blank=True, db_comment='描述')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    environment = models.OneToOneField('Environment', on_delete=models.CASCADE)


class Environment_Auth(models.Model):
    auth_username = models.CharField(max_length=100, null=True, blank=True, db_comment='账号')
    type = models.CharField(max_length=1, db_comment='类型;1:BASIC_AUTH;2:token')
    auth_password = models.CharField(max_length=500, null=True, blank=True, db_comment='密码')
    token_value = models.CharField(max_length=500, null=True, blank=True, db_comment='token值')
    desc = models.CharField(max_length=255, null=True, blank=True, db_comment='备注')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    environment = models.OneToOneField('Environment', on_delete=models.CASCADE)


class Switch_Projet(models.Model):
    username = models.CharField(max_length=20, db_comment='用户名')
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, db_comment='默认项目')

    class Meta:
        db_table = 'default_project'


class Openai_Config(models.Model):
    name = models.CharField(max_length=100, db_comment='名称')
    type = models.CharField(max_length=1, db_comment='类型;1:api_test;2:ui_test')
    value = models.JSONField(null=True, blank=True, db_comment='值')
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, db_comment='项目')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')

    class Meta:
        db_table = 'openai_config'
        db_table_comment = 'openai配置表'


class DataSource(models.Model):
    """数据源模型"""
    DATABASE_TYPES = (
        ('mysql', 'MySQL'),
        ('postgresql', 'PostgreSQL'),
    )
    
    name = models.CharField(max_length=100, db_comment='数据源名称')
    database_type = models.CharField(max_length=20, choices=DATABASE_TYPES, db_comment='数据库类型')
    host = models.CharField(max_length=200, db_comment='主机地址')
    port = models.PositiveIntegerField(db_comment='端口号')
    database_name = models.CharField(max_length=100, db_comment='数据库名称')
    username = models.CharField(max_length=100, db_comment='用户名')
    password = models.CharField(max_length=500, db_comment='密码')
    charset = models.CharField(max_length=20, default='utf8mb4', db_comment='字符集')
    desc = models.CharField(max_length=500, null=True, blank=True, db_comment='描述')
    is_active = models.BooleanField(default=True, db_comment='是否启用')
    project = models.ForeignKey('Project', on_delete=models.CASCADE, related_name='data_sources', db_comment='所属项目')
    creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')

    class Meta:
        db_table = 'data_source'
        db_table_comment = '数据源表'
        unique_together = ['name', 'project']  # 同一项目下数据源名称唯一

    def __str__(self):
        return f"{self.project.name} - {self.name} ({self.database_type})"

# class Faker_Data(models.Model):
#     key = models.CharField(max_length=100, db_comment='内置Faker数据名')
#     type = models.CharField(max_length=1,
#                             db_comment='内置Faker数据类型;1:变量;')
#     variable_type = models.CharField(max_length=1, null=True, db_comment='变量类型;1:string;2:int;3:数组')
#     desc = models.CharField(max_length=255, null=True, blank=True, db_comment='内置Faker数据说明')
#     value = models.CharField(max_length=500, db_comment='内置Faker数据内容')
#     creator = models.CharField(max_length=100, null=True, blank=True, db_comment='创建人')
#     create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')

#     class Meta:
#         db_table = 'default_faker_data'

#     def __str__(self):
#         return f"内置Faker_Data：{self.key}={self.value}"
    
    



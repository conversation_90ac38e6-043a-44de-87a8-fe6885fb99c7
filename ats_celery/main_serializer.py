from rest_framework import serializers
from django_celery_beat.models import PeriodicTask, CrontabSchedule, IntervalSchedule, ClockedSchedule
from datetime import datetime
from django.utils.timezone import make_aware
import pytz

class IntervalScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = IntervalSchedule
        fields = '__all__'

class ClockedScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClockedSchedule
        fields = '__all__'
    
    #前端组件自动帮转了

    # def to_internal_value(self, data):
    #     clocked_time_str = data.get('clocked_time', None)
    #     if clocked_time_str:
    #         # 解析时间字符串
    #         naive_time = datetime.strptime(clocked_time_str, '%Y-%m-%dT%H:%M:%S')
    #         # 使用 pytz 设置北京时间（UTC+8）
    #         beijing_tz = pytz.timezone('Asia/Shanghai')
    #         aware_time = make_aware(naive_time, timezone=beijing_tz)
    #         # 转换为 UTC 时间
    #         utc_time = aware_time.astimezone(pytz.utc)
    #         # 更新数据中的时间为 UTC 时间
    #         data['clocked_time'] = utc_time.strftime('%Y-%m-%dT%H:%M:%SZ')
    #     return super().to_internal_value(data)

class CrontabScheduleSerializer(serializers.ModelSerializer):
    timezone = serializers.CharField(default='Asia/Shanghai')  # 将 timezone 字段处理为字符串

    class Meta:
        model = CrontabSchedule
        fields = '__all__'

class PeriodicTaskSerializer(serializers.ModelSerializer):
    crontab = CrontabScheduleSerializer(required=False)  # 设置为非必需
    interval = IntervalScheduleSerializer(required=False)  # 设置为非必需
    clocked = ClockedScheduleSerializer(required=False)  # 设置为非必需

    class Meta:
        model = PeriodicTask
        fields = '__all__'

    def create(self, validated_data):
        schedule_fields = ['crontab', 'interval', 'clocked']
        schedule_data = {key: validated_data.pop(key, None) for key in schedule_fields if validated_data.get(key)}

        if len(schedule_data) > 1:
            raise serializers.ValidationError("只能指定一种调度方式（crontab, interval, 或 clocked）。")

        crontab = CrontabSchedule.objects.create(**schedule_data.get('crontab')) if 'crontab' in schedule_data else None
        interval = IntervalSchedule.objects.create(**schedule_data.get('interval')) if 'interval' in schedule_data else None
        clocked = ClockedSchedule.objects.create(**schedule_data.get('clocked')) if 'clocked' in schedule_data else None
        # 确保任务名称被正确设置
        # 必须重置每个定期任务的“上次运行时间”,否则取的时区不对
        last_run_at = validated_data.pop('last_run_at', None)
        task_name = validated_data.pop('task')  # 默认使用 test_celery 任务
        return PeriodicTask.objects.create(crontab=crontab, interval=interval, clocked=clocked, task=task_name, last_run_at=last_run_at, **validated_data)

    def update(self, instance, validated_data):
        schedule_fields = ['crontab', 'interval', 'clocked']
        schedule_data = {key: validated_data.pop(key, None) for key in schedule_fields if validated_data.get(key)}

        if len(schedule_data) > 1:
            raise serializers.ValidationError("只能指定一种调度方式（crontab, interval, 或 clocked）。")

        if 'crontab' in schedule_data:
            CrontabSchedule.objects.filter(id=instance.crontab.id).update(**schedule_data['crontab'])
        if 'interval' in schedule_data:
            IntervalSchedule.objects.filter(id=instance.interval.id).update(**schedule_data['interval'])
        if 'clocked' in schedule_data:
            ClockedSchedule.objects.filter(id=instance.clocked.id).update(**schedule_data['clocked'])
        # 更新任务名称
        instance.task = validated_data.get('task', instance.task)
        instance.save()
        return super().update(instance, validated_data)
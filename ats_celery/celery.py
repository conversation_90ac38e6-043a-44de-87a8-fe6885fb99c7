from __future__ import absolute_import, unicode_literals
import os
from celery import Celery

#读django项目变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ats.settings')
#适配windows开发，防止报错
os.environ.setdefault('FORKED_BY_MULTIPROCESSING', '1')
#创建celery实例，名字无所谓
app = Celery('ats')
#加载django配置，以CELERY开头的配置
app.config_from_object('django.conf:settings', namespace='CELERY')

# 禁用节点自动发现
# app.conf.worker_enable_remote_control = False
# app.conf.worker_state_db = None

# 使用数据库调度器
app.conf.beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'

# 自动发现所有已注册app中的tasks.py
app.autodiscover_tasks(['ats_celery.tasks', 'api_test.smart_api_test_views.main_views.tasks'])

# 时区设置
app.conf.timezone = 'Asia/Shanghai'
# 禁用UTC
app.conf.enable_utc = False

# Redis 配置优化
app.conf.broker_transport_options = {
    'visibility_timeout': 3600,  # 1小时
    'socket_timeout': 30,
    'socket_connect_timeout': 30,
}

# 心跳检测和任务超时配置
app.conf.broker_heartbeat = 10
app.conf.broker_connection_timeout = 30
app.conf.task_soft_time_limit = 600
app.conf.task_time_limit = 1200
app.conf.worker_prefetch_multiplier = 4
app.conf.worker_max_tasks_per_child = 200
app.conf.task_acks_late = True
app.conf.worker_concurrency = 10
app.conf.task_queue_max_priority = 10
app.conf.broker_pool_limit = 10

# 任务路由设置
app.conf.task_default_queue = 'celery'
app.conf.task_create_missing_queues = True
app.conf.task_default_exchange = 'celery'
app.conf.task_default_exchange_type = 'direct'
app.conf.task_default_routing_key = 'celery'

# 错误处理和重试策略
app.conf.task_reject_on_worker_lost = True
app.conf.task_acks_on_failure_or_timeout = False

# 结果后端配置（如果需要存储任务结果）
app.conf.result_expires = 3600  # 结果过期时间：1小时

# 日志配置
app.conf.worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
app.conf.worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')

# # 时区，使用亚洲上海时区，和下面UTC是否启用是一对，两者只能使用一个
# app.conf.timezone = 'Asia/Shanghai'
# # 是否使用UTC
# app.conf.enable_utc = False
# 保持在启动时重试连接的现有行为
app.conf.broker_connection_retry_on_startup = True
# 任务的定时配置
# app.conf.beat_schedule = {
#     #名字自定义
#     'run-scene-every-min': {
#         #执行对应的函数
#         'task': 'ats_celery.tasks.run_scene',
#         #定时执行
#         'schedule': crontab(minute='*'),  # 注意这里是每分钟执行一次
#         # 传递参数看情况传元组
#         'args': ('KiZArmh7',),
#     },
#     # 'call-api-every-hour11local': {
#     #     'task': 'ats_celery.tasks.call_api',
#     #     'schedule': crontab(minute='*'),  # 注意这里是每分钟执行一次
#     # },
# }
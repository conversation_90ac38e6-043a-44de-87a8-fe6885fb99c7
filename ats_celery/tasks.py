from .celery import app
import requests
import logging
from django.conf import settings
import subprocess
import os
from datetime import datetime
from ui_test.models import UiTestTaskExecution
from django.utils import timezone
import threading

logger = logging.getLogger('ats-console')

# @shared_task
# def update_token():
#     response = requests.post('http://127.0.0.1:8000/api/login/', data={
#         'username': 'admin',
#         'password': 'admin'
#     })
#     token = response.json().get('access_token')
#     print(token)
#     cache.set('api_token', token, timeout=3600)  # 假设 token 的有效期是 1 小时
#     return token

'''
@shared_task 装饰器用于创建一个可以在多个应用中共享的任务。当你有多个项目或者应用，
并且想要在这些项目之间共享某些任务时，这非常有用。
使用 @shared_task 不需要显式地绑定任务到某个 Celery 应用实例。
这意味着，如果你的项目结构中有多个 Celery 应用配置，@shared_task 可以让你定义一次任务，
然后在任何应用中都可以使用它。
'''
# @shared_task()
# def call_api():
#     response = requests.get('https://httpbin.org/get')
#     print("hello")  # 确认任务执行到此处
#     return response.text  # 确保返回响应文本


'''
@app.task 装饰器是将任务绑定到特定的 Celery 应用实例。这是最常见的任务定义方式，适用于大多数单一应用项目。
使用 @app.task 定义的任务与特定的 Celery 实例关联，这意味着它们依赖于该实例的配置和上下文。
'''
# @app.task()
# def test_celery():
#     print(123123)
#     return 123123

from django.utils import timezone
from datetime import timedelta
@app.task()
def run_scene(scene_code):
    # time1 = timezone.now() + timedelta(minutes=5)
    # print("time1", time1)
    if not scene_code:
        logger.error("Scene code cannot be empty")
        return {"error": "Scene code cannot be empty"}
    headers = {'Internal-Call': 'True'}
    response = requests.post(f'http://127.0.0.1:{settings.NGINX_PORT}/api/api_test/run_scene_api/', headers=headers, data={
        "scene_code": scene_code
    })
    if response.status_code != 200 or not response.content:
        logger.error("Server response exception")
        return {"error": "Server response exception"}
    try:
        response_data = response.json()
        if response_data.get('code') != 2000:
            logger.error("run_scene task execution failed")
            return {"error": "run_scene task execution failed"}
        print("response_data", response_data)
        return {"success": "run_scene task execution success"}
    except ValueError:
        logger.error("Response format error, cannot be parsed as JSON")
        return {"error": "Response format error, cannot be parsed as JSON"}

@app.task()
def run_ui_test(yaml_path, task_execution_id=None, openai_config=None):
    """
    执行UI测试的异步任务
    :param yaml_path: yaml配置文件的路径
    :param task_execution_id: 任务执行记录ID
    :param openai_config: OpenAI配置信息
    :return: 执行结果
    """
    try:
        # 获取任务执行记录
        if task_execution_id:
            task_execution = UiTestTaskExecution.objects.get(id=task_execution_id)
            task_execution.status = 'running'
            task_execution.save()

        # 获取任务ID和名称
        task_id = task_execution.task.id if task_execution_id else None
        task_name = task_execution.task.name if task_execution_id else "unknown"
        
        # 创建日志文件目录
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(yaml_path)), 'logs')
        os.makedirs(logs_dir, exist_ok=True)
        
        # 创建日志文件
        log_filename = f"task_{task_id}_{task_execution_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        log_path = os.path.join(logs_dir, log_filename)
        
        # 构建环境变量
        env = os.environ.copy()
        if openai_config:
            env.update(openai_config)
        
        # 构建命令
        command = f"midscene {yaml_path}"
        
        # 获取当前时间的格式化函数
        def get_time_str():
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # 记录开始运行的信息
        with open(log_path, 'w', encoding='utf-8') as log_file:
            start_message = f"[{get_time_str()}] [INFO] 开始执行UI测试任务: {task_name} (ID: {task_id})\n"
            start_message += f"[{get_time_str()}] [INFO] 命令: {command}\n"
            start_message += f"[{get_time_str()}] [INFO] -----------------------------------------------------------\n"
            log_file.write(start_message)
            log_file.flush()
            os.fsync(log_file.fileno())  # 强制刷新到磁盘
        
        # 执行命令并实时获取输出
        try:
            # 使用Popen启动进程，实现实时捕获输出
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # 行缓冲
                env=env
            )
            
            # 创建线程函数来读取标准输出
            def read_stdout():
                report_path = None
                for line in iter(process.stdout.readline, ''):
                    if line:
                        # 替换欢迎信息
                        line = line.replace("Welcome to @midscene/cli", "Welcome to @staryea/smartui").strip()
                        
                        # 写入日志文件 - 直接append而不是保持文件打开
                        log_message = f"[{get_time_str()}] [INFO] {line}\n"
                        with open(log_path, 'a', encoding='utf-8') as log_file:
                            log_file.write(log_message)
                            log_file.flush()
                            os.fsync(log_file.fileno())  # 强制刷新到磁盘
                        
                        logger.info(f"UI测试输出: {line}")
                        
                        # 检查是否包含报告路径
                        if 'report:' in line:
                            relative_report_path = line.split('report:')[-1].strip()
                            if relative_report_path.startswith('./'):
                                relative_report_path = relative_report_path[2:]  # 移除开头的 ./
                            report_path = os.path.join(os.path.dirname(os.path.dirname(yaml_path)), 
                                                relative_report_path)
                            if task_execution_id:
                                task_execution.report_path = report_path
                                task_execution.save(update_fields=['report_path'])
                
                return report_path
            
            # 创建线程函数来读取错误输出
            def read_stderr():
                real_errors = []
                for line in iter(process.stderr.readline, ''):
                    if line:
                        # 过滤掉 Node.js 的废弃警告信息
                        if not any(warning in line for warning in [
                            'MIDSCENE_DEBUG_AI_PROFILE is deprecated',
                            'The `punycode` module is deprecated',
                            'Use `node --trace-deprecation'
                        ]):
                            line = line.strip()
                            
                            # 写入日志文件 - 直接append而不是保持文件打开
                            log_message = f"[{get_time_str()}] [ERROR] {line}\n"
                            with open(log_path, 'a', encoding='utf-8') as log_file:
                                log_file.write(log_message)
                                log_file.flush()
                                os.fsync(log_file.fileno())  # 强制刷新到磁盘
                            
                            logger.error(f"UI测试错误: {line}")
                            real_errors.append(line)
                
                return '\n'.join(real_errors)
            
            # 创建并启动线程
            stdout_thread = threading.Thread(target=read_stdout)
            stderr_thread = threading.Thread(target=read_stderr)
            
            stdout_thread.start()
            stderr_thread.start()
            
            # 等待进程完成
            returncode = process.wait()
            
            # 等待线程完成
            stdout_thread.join()
            stderr_thread.join()
            
            # 捕获任何剩余的输出
            stdout_remainder = process.stdout.read()
            stderr_remainder = process.stderr.read()
            
            # 处理剩余输出
            if stdout_remainder:
                with open(log_path, 'a', encoding='utf-8') as log_file:
                    for line in stdout_remainder.splitlines():
                        line = line.replace("Welcome to @midscene/cli", "Welcome to @staryea/smartui").strip()
                        log_file.write(f"[{get_time_str()}] [INFO] {line}\n")
                        logger.info(f"UI测试输出: {line}")
            
            # 处理剩余错误
            filtered_stderr = []
            if stderr_remainder:
                with open(log_path, 'a', encoding='utf-8') as log_file:
                    for line in stderr_remainder.splitlines():
                        if not any(warning in line for warning in [
                            'MIDSCENE_DEBUG_AI_PROFILE is deprecated',
                            'The `punycode` module is deprecated',
                            'Use `node --trace-deprecation'
                        ]):
                            line = line.strip()
                            log_file.write(f"[{get_time_str()}] [ERROR] {line}\n")
                            logger.error(f"UI测试错误: {line}")
                            filtered_stderr.append(line)
            
            # 写入任务完成信息
            with open(log_path, 'a', encoding='utf-8') as log_file:
                end_message = f"[{get_time_str()}] [INFO] -----------------------------------------------------------\n"
                end_message += f"[{get_time_str()}] [INFO] 任务完成时间: {get_time_str()}\n"
                end_message += f"[{get_time_str()}] [INFO] 退出代码: {returncode}\n"
                log_file.write(end_message)
                log_file.flush()
                os.fsync(log_file.fileno())  # 强制刷新到磁盘
            
            # 读取完整日志文件用于更新任务执行记录
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    full_log = f.read()
            except:
                full_log = ""
            
            # 检查日志中是否包含错误模式
            def check_log_for_errors(log_content):
                # 定义错误模式列表
                error_patterns = [
                    "error:", 
                    "Assertion failed:", 
                    "退出代码: 1"
                ]
                
                # 要忽略的错误信息列表
                ignore_patterns = [
                    "git: not found",
                    "but the script will continue"
                ]
                
                # 检查是否存在需要忽略的错误
                for line in log_content.split('\n'):
                    # 如果行包含 [ERROR] 但不包含任何需要忽略的模式，则视为错误
                    if "[ERROR]" in line and not any(ignore in line for ignore in ignore_patterns):
                        return True, f"日志中检测到错误: '{line.strip()}'"
                
                # 检查日志中是否包含任何错误模式
                for pattern in error_patterns:
                    if pattern in log_content:
                        return True, f"日志中检测到错误: '{pattern}'"
                
                # 如果退出代码不为0，也视为失败
                if "退出代码:" in log_content and "退出代码: 0" not in log_content:
                    return True, "退出代码非0"
                    
                return False, ""
            
            # 更新任务执行记录
            if task_execution_id:
                task_execution.execution_output = full_log
                task_execution.end_time = timezone.now()
                task_execution.log_path = log_path
                
                # 检查日志内容中是否有错误
                has_error, error_message = check_log_for_errors(full_log)
                
                # 检查执行结果
                filtered_stderr_str = '\n'.join(filtered_stderr)
                if returncode == 0 and not filtered_stderr_str and not has_error:
                    task_execution.status = 'success'
                else:
                    task_execution.status = 'failed'
                    # 如果标准错误输出为空但日志中检测到错误
                    if not filtered_stderr_str and has_error:
                        task_execution.error_message = error_message
                    else:
                        task_execution.error_message = filtered_stderr_str
                
                task_execution.save()
            
            # 返回结果
            if returncode == 0 and not filtered_stderr and not check_log_for_errors(full_log)[0]:
                return {
                    "success": True,
                    "message": "UI测试执行成功",
                    "log_path": log_path,
                    "report_path": task_execution.report_path if task_execution_id else None
                }
            else:
                error_msg = '\n'.join(filtered_stderr)
                if not error_msg:
                    _, error_msg = check_log_for_errors(full_log)
                
                return {
                    "success": False,
                    "message": "UI测试执行失败",
                    "error": error_msg,
                    "log_path": log_path
                }
                
        except Exception as e:
            error_message = str(e)
            logger.error(f"执行UI测试时发生错误: {error_message}")
            
            # 将错误写入日志文件
            with open(log_path, 'a', encoding='utf-8') as log_file:
                error_message = f"[{get_time_str()}] [ERROR] 执行UI测试时发生错误: {error_message}\n"
                error_message += f"[{get_time_str()}] [INFO] 任务结束时间: {get_time_str()}\n"
                log_file.write(error_message)
                log_file.flush()
                os.fsync(log_file.fileno())  # 强制刷新到磁盘
            
            if task_execution_id:
                task_execution.status = 'failed'
                task_execution.error_message = error_message
                task_execution.end_time = timezone.now()
                task_execution.log_path = log_path
                task_execution.save()
                
            return {
                "success": False,
                "message": "执行UI测试时发生错误",
                "error": error_message,
                "log_path": log_path
            }
            
    except Exception as e:
        logger.error(f"执行UI测试时发生错误: {str(e)}")
        if task_execution_id:
            task_execution.status = 'failed'
            task_execution.error_message = str(e)
            task_execution.save()
        return {
            "success": False,
            "message": "执行UI测试时发生错误",
            "error": str(e)
        }
from django_celery_beat.models import PeriodicTask
from .main_serializer import PeriodicTaskSerializer
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.my_modelviewset import MyModelViewSet

class PeriodicTaskViewSet(MyModelViewSet, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    queryset = PeriodicTask.objects.all().order_by('id')
    serializer_class = PeriodicTaskSerializer
    ordering = ['id']
    pagination_class = None
    
    def get_object(self):
        name = self.kwargs.get('name')
        return PeriodicTask.objects.get(name=name)


from django.db import models

class UiTestTreeFolder(models.Model):
    name = models.CharField(max_length=100, db_comment='文件夹名')
    type = models.CharField(max_length=1, db_comment='树类型;1目录;0任务')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True)
    project = models.ForeignKey('project.Project', on_delete=models.CASCADE, null=True)
    # 新增字段，如果是任务节点，关联到具体任务
    related_task = models.OneToOneField('UiTestTask', on_delete=models.CASCADE, null=True, related_name='tree_node')

    class Meta:
        db_table = 'ui_test_tree_folder'
        db_table_comment = 'UI测试目录树'

    def __str__(self):
        return f"目录名: {self.name}"

class UiTestTask(models.Model):
    project = models.ForeignKey('project.Project', on_delete=models.CASCADE, db_comment='所属项目')
    name = models.CharField(max_length=100, db_comment='任务名称')
    description = models.TextField(null=True, blank=True, db_comment='任务描述')

    # target配置
    url = models.CharField(max_length=500, db_comment='访问URL')
    bridge_mode = models.CharField(max_length=20, default='false', null=True, blank=True, 
                                 db_comment='桥接模式:newTabWithUrl/currentTab')
    accept_insecure_certs = models.BooleanField(default=True, 
                                               db_comment='是否忽略HTTPS证书错误')

    # tasks配置 - 使用JSONField存储，因为这部分结构比较灵活
    tasks = models.JSONField(null=True, db_comment='任务配置')

    # yaml配置文件路径
    yaml_path = models.CharField(max_length=500, null=True, db_comment='YAML配置文件路径')

    # 基础字段
    create_time = models.DateTimeField(auto_now_add=True, db_comment='创建时间')
    update_time = models.DateTimeField(auto_now=True, db_comment='更新时间')
    creator = models.CharField(max_length=50, null=True, db_comment='创建人')

    class Meta:
        db_table = 'ui_test_task'
        db_table_comment = 'UI测试任务表'

    def __str__(self):
        return self.name

class UiTestTaskExecution(models.Model):
    task = models.ForeignKey(UiTestTask, on_delete=models.CASCADE, db_comment='关联的UI测试任务')
    status = models.CharField(max_length=20, db_comment='执行状态: pending/running/success/failed')
    start_time = models.DateTimeField(auto_now_add=True, db_comment='开始执行时间')
    end_time = models.DateTimeField(null=True, blank=True, db_comment='执行结束时间')
    
    # 文件路径信息
    report_path = models.CharField(max_length=500, null=True, blank=True, db_comment='测试报告路径')
    log_path = models.CharField(max_length=500, null=True, blank=True, db_comment='日志文件路径')
    
    # 执行结果
    error_message = models.TextField(null=True, blank=True, db_comment='错误信息')
    execution_output = models.TextField(null=True, blank=True, db_comment='执行输出')
    
    class Meta:
        db_table = 'ui_test_task_execution'
        db_table_comment = 'UI测试任务执行记录表'
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.task.name} - {self.start_time}"
    


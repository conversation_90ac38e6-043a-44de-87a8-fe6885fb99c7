from ...models import UiTestTask
from rest_framework import serializers


class UiTestTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = UiTestTask
        fields = '__all__'
        read_only_fields = ('create_time', 'update_time')


class UiTestTaskCreateUpdateSerializer(serializers.ModelSerializer):
    folder_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = UiTestTask
        fields = ['name', 'description', 'project', 'url', 'bridge_mode', 
                 'accept_insecure_certs', 'tasks', 'folder_id']

    def create(self, validated_data):
        folder_id = validated_data.pop('folder_id')
        instance = super().create(validated_data)
        instance.folder_id = folder_id
        instance.save()
        return instance

    def update(self, instance, validated_data):
        folder_id = validated_data.pop('folder_id', None)
        instance = super().update(instance, validated_data)
        if folder_id:
            instance.folder_id = folder_id
            instance.save()
        return instance




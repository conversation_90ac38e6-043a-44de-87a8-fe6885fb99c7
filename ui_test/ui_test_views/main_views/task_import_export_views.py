import json
from datetime import datetime
from django.http import HttpResponse
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ...models import UiTestTask, UiTestTreeFolder
from ..serializers.ui_test_task_serializer import UiTestTaskCreateUpdateSerializer


class UiTestTaskExportView(APIView):
    """UI测试任务导出视图"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        导出UI测试任务
        支持单个或批量导出
        
        请求参数:
        - task_ids: 任务ID列表 (必填)
        """
        try:
            task_ids = request.data.get('task_ids', [])
            
            if not task_ids:
                return Response(public_error_response("缺少task_ids参数"))
                
            if not isinstance(task_ids, list):
                return Response(public_error_response("task_ids必须是列表格式"))
            
            # 获取要导出的任务
            tasks = UiTestTask.objects.filter(id__in=task_ids)
            
            if not tasks.exists():
                return Response(public_error_response("未找到要导出的任务"))
            
            # 构建导出数据
            export_data = {
                "export_info": {
                    "export_time": datetime.now().isoformat(),
                    "export_user": request.user.username,
                    "total_tasks": tasks.count(),
                    "version": "1.0"
                },
                "tasks": []
            }
            
            for task in tasks:
                # 获取任务的目录路径信息
                folder_path = ""
                try:
                    tree_node = task.tree_node
                    path_parts = []
                    current_node = tree_node.parent
                    while current_node and current_node.name != '顶级目录':
                        path_parts.insert(0, current_node.name)
                        current_node = current_node.parent
                    folder_path = "/".join(path_parts) if path_parts else ""
                except UiTestTreeFolder.DoesNotExist:
                    folder_path = ""
                
                task_data = {
                    "id": task.id,
                    "name": task.name,
                    "description": task.description,
                    "url": task.url,
                    "bridge_mode": task.bridge_mode,
                    "accept_insecure_certs": task.accept_insecure_certs,
                    "tasks": task.tasks,
                    "folder_path": folder_path,
                    "create_time": task.create_time.isoformat() if task.create_time else None,
                    "creator": task.creator
                }
                export_data["tasks"].append(task_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if len(task_ids) == 1:
                filename = f"ui_test_task_{tasks.first().name}_{timestamp}.json"
            else:
                filename = f"ui_test_tasks_batch_{timestamp}.json"
            
            # 返回JSON文件供下载
            response = HttpResponse(
                json.dumps(export_data, ensure_ascii=False, indent=2),
                content_type='application/json'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
            
        except Exception as e:
            return Response(public_error_response(f"导出任务时发生错误: {str(e)}"))


class UiTestTaskImportView(APIView):
    """UI测试任务导入视图"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """
        导入UI测试任务
        
        请求参数:
        - file: 导入的JSON文件 (必填)
        - project_id: 项目ID (必填)
        - folder_id: 目标目录ID (必填)
        """
        try:
            # 获取参数
            file = request.FILES.get('file')
            project_id = request.data.get('project_id')
            folder_id = request.data.get('folder_id')
            
            if not file:
                return Response(public_error_response("缺少file参数"))
                
            if not project_id:
                return Response(public_error_response("缺少project_id参数"))
                
            if not folder_id:
                return Response(public_error_response("缺少folder_id参数"))
            
            # 验证项目是否存在
            try:
                from project.models import Project
                Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                return Response(public_error_response("指定的项目不存在"))
            
            # 验证目录是否存在且为目录类型
            try:
                target_folder = UiTestTreeFolder.objects.get(id=folder_id, project_id=project_id)
                if target_folder.type != '1':
                    return Response(public_error_response("指定的目录必须是目录类型，不能是任务节点"))
            except UiTestTreeFolder.DoesNotExist:
                return Response(public_error_response("指定的目录不存在或不属于该项目"))
            
            # 解析JSON文件
            try:
                file_content = file.read().decode('utf-8')
                import_data = json.loads(file_content)
            except json.JSONDecodeError:
                return Response(public_error_response("文件格式错误，请确保是有效的JSON文件"))
            except UnicodeDecodeError:
                return Response(public_error_response("文件编码错误，请使用UTF-8编码"))
            
            # 验证文件结构
            if 'tasks' not in import_data:
                return Response(public_error_response("文件格式错误，缺少tasks字段"))
            
            tasks_data = import_data['tasks']
            if not isinstance(tasks_data, list):
                return Response(public_error_response("文件格式错误，tasks必须是列表格式"))
            
            # 开始导入
            success_count = 0
            error_count = 0
            import_results = []
            
            for task_data in tasks_data:
                try:
                    original_name = task_data.get('name', '')
                    final_name = original_name
                    
                    # 检查任务名是否重复（在指定项目中），如果重复则自动重命名
                    if UiTestTask.objects.filter(name=original_name, project_id=project_id).exists():
                        # 生成新名称
                        counter = 1
                        while UiTestTask.objects.filter(
                            name=f"{original_name}_导入_{counter}",
                            project_id=project_id
                        ).exists():
                            counter += 1
                        final_name = f"{original_name}_导入_{counter}"
                    
                    # 准备任务数据，使用用户指定的目录ID
                    new_task_data = {
                        'name': final_name,
                        'description': task_data.get('description', ''),
                        'project': project_id,
                        'url': task_data.get('url', ''),
                        'bridge_mode': task_data.get('bridge_mode', 'false'),
                        'accept_insecure_certs': task_data.get('accept_insecure_certs', True),
                        'tasks': task_data.get('tasks', []),
                        'folder_id': folder_id  # 使用用户指定的目录ID
                    }
                    
                    # 使用序列化器创建任务
                    serializer = UiTestTaskCreateUpdateSerializer(data=new_task_data)
                    if serializer.is_valid():
                        # 创建任务
                        task = serializer.save(creator=request.user.username)
                        
                        # 创建任务节点并建立关联
                        tree_node = UiTestTreeFolder.objects.create(
                            name=final_name,
                            type='0',  # 0表示任务节点
                            parent_id=folder_id,
                            project_id=project_id,
                            related_task=task
                        )
                        
                        # 保存YAML配置文件（使用现有的方法）
                        from .ui_test_task_views import UiTestTaskView
                        task_view = UiTestTaskView()
                        task_view._save_yaml_file(task.id, serializer.validated_data, project_id)
                        
                        success_count += 1
                        message = "导入成功"
                        if final_name != original_name:
                            message = f"导入成功（已重命名为：{final_name}）"
                        
                        import_results.append({
                            "name": original_name,
                            "final_name": final_name,
                            "status": "success",
                            "message": message,
                            "task_id": task.id
                        })
                    else:
                        error_count += 1
                        import_results.append({
                            "name": original_name,
                            "status": "error",
                            "message": f"数据验证失败: {serializer.errors}"
                        })
                        
                except Exception as e:
                    error_count += 1
                    import_results.append({
                        "name": task_data.get('name', '未知'),
                        "status": "error", 
                        "message": f"导入失败: {str(e)}"
                    })
            
            # 返回导入结果
            return Response(public_success_response({
                "summary": {
                    "total": len(tasks_data),
                    "success": success_count,
                    "failed": error_count
                },
                "details": import_results,
                "target_folder": {
                    "id": target_folder.id,
                    "name": target_folder.name
                }
            }))
            
        except Exception as e:
            return Response(public_error_response(f"导入任务时发生错误: {str(e)}")) 
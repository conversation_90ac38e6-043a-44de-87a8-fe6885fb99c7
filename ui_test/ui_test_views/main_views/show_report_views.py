from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ui_test.models import UiTestTaskExecution
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.pagination import PageNumberPagination
from ats.public.public_class.myresponse import public_success_response, public_error_response
from django.conf import settings
import logging
import os

logger = logging.getLogger('ats-console')

def get_relative_report_path(full_path):
    """
    将完整的报告路径转换为相对路径
    """
    if not full_path:
        return ""
    # 获取midscene_run/report之后的路径部分
    if "midscene_run/report" in full_path:
        parts = full_path.split("midscene_run/report")
        if len(parts) > 1:
            return f"midscene_run/report{parts[1]}"
    return full_path

class TaskExecutionHistoryView(APIView):
    """
    获取UI测试任务的执行历史记录
    支持：
    1. GET方法：
       - 获取单个任务的所有执行记录（分页）
       - 获取单个任务的最新执行记录
    2. POST方法：
       - 批量获取多个任务的执行记录
       - 支持只获取最新记录
    """
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends

    def get(self, request):
        """
        获取单个任务的执行历史记录
        参数：
        - task_id: 任务ID（必填）
        - latest_only: 是否只返回最新记录（可选，默认false）
        - page: 页码（可选，默认1）
        - page_size: 每页大小（可选，默认10）
        """
        try:
            task_id = request.query_params.get('task_id')
            latest_only = request.query_params.get('latest_only', 'false').lower() == 'true'
            
            if not task_id:
                return Response(public_error_response("缺少task_id参数"))

            # 查询执行记录
            executions = UiTestTaskExecution.objects.filter(task_id=task_id).order_by('-start_time')
            
            if not executions.exists():
                return Response(public_error_response(f"未找到任务ID {task_id} 的执行记录"))

            # 如果只需要最新记录
            if latest_only:
                latest_execution = executions.first()
                data = {
                    "execution_id": latest_execution.id,
                    "task_name": latest_execution.task.name,
                    "report_path": latest_execution.report_path,
                    "report_url": f"http://{settings.SERVER_IP}:{settings.NGINX_PORT}/reports/{get_relative_report_path(latest_execution.report_path)}",
                    "status": latest_execution.status,
                    "start_time": latest_execution.start_time,
                    "end_time": latest_execution.end_time
                }
                return Response(public_success_response(data))

            # 分页查询所有记录
            paginator = PageNumberPagination()
            paginator.page_size = int(request.query_params.get('page_size', 10))
            page_executions = paginator.paginate_queryset(executions, request)
            
            execution_list = []
            for execution in page_executions:
                execution_list.append({
                    "execution_id": execution.id,
                    "task_name": execution.task.name,
                    "report_path": execution.report_path,
                    "status": execution.status,
                    "start_time": execution.start_time,
                    "end_time": execution.end_time,
                    "report_url": f"http://{settings.SERVER_IP}:{settings.NGINX_PORT}/reports/{get_relative_report_path(execution.report_path)}"
                })

            return paginator.get_paginated_response(public_success_response(execution_list))

        except Exception as e:
            logger.error(f"获取任务执行记录时发生错误: {str(e)}")
            return Response(public_error_response(f"获取任务执行记录时发生错误: {str(e)}"))

    def post(self, request):
        """
        批量获取多个任务的执行记录
        参数：
        {
            "task_ids": [1, 2, 3],  # 任务ID列表（必填）
            "latest_only": true      # 是否只返回最新记录（可选，默认true）
        }
        """
        try:
            task_ids = request.data.get('task_ids', [])
            latest_only = request.data.get('latest_only', True)

            if not task_ids:
                return Response(public_error_response("缺少task_ids参数"))

            result = []
            for task_id in task_ids:
                executions = UiTestTaskExecution.objects.filter(
                    task_id=task_id
                ).order_by('-start_time')

                if latest_only:
                    executions = executions[:1]

                for execution in executions:
                    result.append({
                        "task_id": task_id,
                        "execution_id": execution.id,
                        "task_name": execution.task.name,
                        "report_path": execution.report_path,
                        "status": execution.status,
                        "start_time": execution.start_time,
                        "end_time": execution.end_time,
                        "report_url": f"http://{settings.SERVER_IP}:{settings.NGINX_PORT}/reports/{get_relative_report_path(execution.report_path)}"
                    })

            if not result:
                return Response(public_error_response("未找到任何执行记录"))

            return Response(public_success_response(result))

        except Exception as e:
            logger.error(f"批量获取执行记录时发生错误: {str(e)}")
            return Response(public_error_response(f"批量获取执行记录时发生错误: {str(e)}"))

import os
import yaml
import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from project.models import Public_Data
from ats_celery.tasks import run_ui_test
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from ui_test.models import UiTestTask, UiTestTaskExecution
from ats.public.public_class.myresponse import public_success_response, public_error_response
from project.models import Openai_Config
from django.conf import settings

logger = logging.getLogger('ats-console')

class RunUITestView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def _save_yaml_file(self, task_id, task_data, project_id):
        """保存任务配置到YAML文件"""
        yaml_dir = os.path.join(settings.BASE_DIR, 'ui_test', 'yaml_configs')
        os.makedirs(yaml_dir, exist_ok=True)
        
        # 生成文件名：task_id_任务名称.yaml
        task_name = task_data.get('name', '').replace(' ', '_').lower()
        yaml_filename = f'{task_id}_{task_name}.yaml'
        yaml_path = os.path.join(yaml_dir, yaml_filename)
        
        # 构建YAML数据
        bridge_mode = task_data.get('bridge_mode')
        bridge_mode = False if str(bridge_mode).lower() == 'false' else bridge_mode
        
        # 自定义YAML表示类，用于控制列表的格式化
        class YAMLFormatter(yaml.SafeDumper):
            def increase_indent(self, flow=False, indentless=False):
                return super().increase_indent(flow, False)
            
            # 控制dictionaries的键的顺序
            def represent_dict(self, data):
                return self.represent_mapping('tag:yaml.org,2002:map', data.items())
        
        if task_data.get('url') is not None and task_data.get('url').startswith('{{') and task_data.get('url').endswith('}}'):
            public_data = Public_Data.objects.filter(key=task_data.get('url').replace('{{', '').replace('}}', '')).first()
            if public_data:
                task_data['url'] = public_data.value

        # 处理tasks中的{{key_name}}格式变量
        tasks = task_data.get('tasks', [])
        
        # 递归处理嵌套字典和列表中的变量
        def process_variable_references(data):
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, (dict, list)):
                        process_variable_references(value)
                    elif isinstance(value, str) and '{{' in value and '}}' in value:
                        # 提取变量名
                        import re
                        var_matches = re.findall(r'{{(.*?)}}', value)
                        processed_value = value
                        
                        for var_name in var_matches:
                            # 查找Public_Data中匹配的记录
                            public_data = Public_Data.objects.filter(
                                key=var_name, 
                                type='5',  # UI测试类型
                                project_id=project_id
                            ).first()
                            
                            if public_data:
                                # 替换变量引用为实际值
                                processed_value = processed_value.replace(f'{{{{{var_name}}}}}', public_data.value)
                        
                        data[key] = processed_value
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    if isinstance(item, (dict, list)):
                        process_variable_references(item)
                    elif isinstance(item, str) and '{{' in item and '}}' in item:
                        # 提取变量名
                        import re
                        var_matches = re.findall(r'{{(.*?)}}', item)
                        processed_value = item
                        
                        for var_name in var_matches:
                            # 查找Public_Data中匹配的记录
                            public_data = Public_Data.objects.filter(
                                key=var_name, 
                                type='5',  # UI测试类型
                                project_id=project_id
                            ).first()
                            
                            if public_data:
                                # 替换变量引用为实际值
                                processed_value = processed_value.replace(f'{{{{{var_name}}}}}', public_data.value)
                        
                        data[i] = processed_value
            
            return data
        
        # 处理tasks中的变量引用
        processed_tasks = process_variable_references(tasks)
        
        # 统一tasks中每个任务的字段顺序
        standardized_tasks = []
        for task in processed_tasks:
            standardized_task = {}
            # 按照期望的顺序添加字段
            if 'name' in task:
                standardized_task['name'] = task['name']
            if 'continueOnError' in task:
                standardized_task['continueOnError'] = task['continueOnError']
            if 'flow' in task:
                standardized_task['flow'] = task['flow']
            # 添加其他可能存在的字段
            for key, value in task.items():
                if key not in standardized_task:
                    standardized_task[key] = value
            standardized_tasks.append(standardized_task)

        yaml_data = {
            'target': {
                'url': task_data.get('url'),
                'bridgeMode': bridge_mode,
                'acceptInsecureCerts': task_data.get('accept_insecure_certs'),
                'viewportWidth': 1920,
                'viewportHeight': 1080
            },
            'tasks': standardized_tasks
        }
        
        # 保存YAML文件，使用自定义的Dumper确保正确的缩进
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_data, f, Dumper=YAMLFormatter, allow_unicode=True, 
                      sort_keys=False, default_flow_style=False, indent=2)
            
        # 更新任务的yaml_path字段
        UiTestTask.objects.filter(id=task_id).update(yaml_path=yaml_path)
    """
    执行UI测试的视图，支持多个任务
    """
    def post(self, request):
        try:
            task_ids = request.data.get('task_ids')
            openai_config_id = request.data.get('openai_config_id')
            
            if not task_ids:
                return Response(public_error_response("缺少task_id参数"))
            
            if not isinstance(task_ids, list):
                return Response(public_error_response("task_id必须是列表格式"))
                
            if not openai_config_id:
                return Response(public_error_response("缺少openai_config_id参数"))
                
            try:
                openai_config = Openai_Config.objects.get(id=openai_config_id)
                if not openai_config.value:
                    return Response(public_error_response("Openai配置值为空"))
            except Openai_Config.DoesNotExist:
                return Response(public_error_response(f"Openai配置不存在: {openai_config_id}"))
            results = []
            for task_id in task_ids:
                # 获取任务实例
                try:
                    task = UiTestTask.objects.get(id=task_id)
                except UiTestTask.DoesNotExist:
                    results.append({
                        "task_id": task_id,
                        "status": "error",
                        "message": f"任务不存在: {task_id}"
                    })
                    continue

                # 保存YAML配置文件
                # 将任务对象转换为字典格式，以便于_save_yaml_file方法处理
                task_data = {
                    'name': task.name,
                    'url': task.url,
                    'bridge_mode': task.bridge_mode,
                    'accept_insecure_certs': task.accept_insecure_certs,
                    'tasks': task.tasks
                }
                logger.info(f"运行开始前，先保存[{task.name}]的YAML文件")
                self._save_yaml_file(task_id, task_data, task.project_id)

                if not task.yaml_path:
                    results.append({
                        "task_id": task_id,
                        "status": "error",
                        "message": f"任务{task_id}的YAML配置文件路径不存在"
                    })
                    continue
                
                # 创建任务执行记录
                task_execution = UiTestTaskExecution.objects.create(
                    task=task,
                    status='pending'
                )
                
                # 异步执行UI测试任务，传入openai配置
                celery_task = run_ui_test.delay(task.yaml_path, task_execution.id, openai_config.value)
                
                results.append({
                    "task_id": task_id,
                    "status": "submitted",
                    "celery_task_id": celery_task.id,
                    "execution_id": task_execution.id
                })
                logger.info(f"提交UI测试任务成功，任务ID: {task_id}, 任务执行ID: {task_execution.id}, 异步任务ID: {celery_task.id}")
            return Response(public_success_response({
                "tasks": results
            }))
            
        except Exception as e:
            logger.error(f"提交UI测试任务时发生错误: {str(e)}")
            return Response(public_error_response(f"提交UI测试任务时发生错误: {str(e)}"))
from rest_framework.views import APIView
from ats.public.public_class.myresponse import public_success_response, public_error_response
from ats.public.public_class.myauthentication import MyAuthentication
from rest_framework.response import Response
from ..serializers.ui_test_tree_folder_serializer import UiTestTreeFolderSerializer
from ...models import UiTestTreeFolder, UiTestTask
import os
from django.conf import settings


class UiTestTreeFolderView(APIView, MyAuthentication):
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes
    filter_backends = MyAuthentication.filter_backends
    serializer_class = UiTestTreeFolderSerializer

    def get_child_folder(self, item):
        # 匹配下级子菜单
        second_level_folders = UiTestTreeFolder.objects.filter(parent_id=item.id)

        # 继续递归匹配是否有多级子菜单
        children = [self.get_child_folder(child) for child in second_level_folders]

        return {
            "id": item.id,
            "name": item.name,
            "type": item.type,
            "parent_id": item.parent_id,
            "children": children or None
        }

    def get(self, request):
        # 需要项目id筛选查询
        project_id = request.query_params.get('project_id')
        # 获取顶级目录
        first_level_folder = UiTestTreeFolder.objects.get(project_id=project_id, name='顶级目录', parent=None)

        # 递归下级目录
        child_folder = self.get_child_folder(first_level_folder)

        return Response(public_success_response(child_folder))

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))


class UiTestTreeFolderDetailView(APIView, MyAuthentication):
    serializer_class = UiTestTreeFolderSerializer

    def put(self, request, pk):
        folder = UiTestTreeFolder.objects.get(pk=pk)
        old_name = folder.name
        serializer = self.serializer_class(instance=folder, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            # 同时更新任务名
            if UiTestTask.objects.filter(name=old_name).exists():
                UiTestTask.objects.filter(name=old_name).update(name=request.data['name'])
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors))

    def delete(self, request, pk):
        try:
            folder = UiTestTreeFolder.objects.get(pk=pk)
            if folder.name == '顶级目录':
                return Response(public_error_response('顶级目录不允许删除！'))

            def get_all_child_folders(folder_id):
                """递归获取所有子目录ID"""
                folder_ids = [folder_id]
                children = UiTestTreeFolder.objects.filter(parent_id=folder_id)
                for child in children:
                    folder_ids.extend(get_all_child_folders(child.id))
                return folder_ids

            # 获取要删除的目录及其所有子目录的ID
            folder_ids = get_all_child_folders(pk)

            # 删除这些目录下的所有任务
            tasks = UiTestTask.objects.filter(tree_node__in=UiTestTreeFolder.objects.filter(id__in=folder_ids))
            for task in tasks:
                # 删除任务的YAML配置文件
                yaml_dir = os.path.join(settings.BASE_DIR, 'ui_test', 'yaml_configs')
                for filename in os.listdir(yaml_dir):
                    if filename.startswith(f'{task.id}_'):
                        yaml_path = os.path.join(yaml_dir, filename)
                        if os.path.exists(yaml_path):
                            os.remove(yaml_path)

            # 删除任务记录
            tasks.delete()

            # 删除目录及其子目录
            UiTestTreeFolder.objects.filter(id__in=folder_ids).delete()

            return Response(public_success_response('删除成功'))
        except UiTestTreeFolder.DoesNotExist:
            return Response(public_error_response('目录不存在！'))
        except Exception as e:
            return Response(public_error_response(f'删除失败：{str(e)}'))
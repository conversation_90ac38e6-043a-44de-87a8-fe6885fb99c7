from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from project.models import Public_Data
from ats.public.public_class.myresponse import public_success_response, public_error_response
from django.db.models import Q
import yaml
import os
from django.conf import settings

from ...models import UiTestTask, UiTestTreeFolder
from ..serializers.ui_test_task_serializer import UiTestTaskSerializer, UiTestTaskCreateUpdateSerializer


class UiTestTaskView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['create_time']

    def _save_yaml_file(self, task_id, task_data, project_id):
        """保存任务配置到YAML文件"""
        yaml_dir = os.path.join(settings.BASE_DIR, 'ui_test', 'yaml_configs')
        os.makedirs(yaml_dir, exist_ok=True)
        
        # 生成文件名：task_id_任务名称.yaml
        task_name = task_data.get('name', '').replace(' ', '_').lower()
        yaml_filename = f'{task_id}_{task_name}.yaml'
        yaml_path = os.path.join(yaml_dir, yaml_filename)
        
        # 构建YAML数据
        bridge_mode = task_data.get('bridge_mode')
        bridge_mode = False if str(bridge_mode).lower() == 'false' else bridge_mode
        
        # 自定义YAML表示类，用于控制列表的格式化
        class YAMLFormatter(yaml.SafeDumper):
            def increase_indent(self, flow=False, indentless=False):
                return super().increase_indent(flow, False)
        
        if task_data.get('url') is not None and task_data.get('url').startswith('{{') and task_data.get('url').endswith('}}'):
            public_data = Public_Data.objects.filter(key=task_data.get('url').replace('{{', '').replace('}}', '')).first()
            if public_data:
                task_data['url'] = public_data.value

        # 处理tasks中的{{key_name}}格式变量
        tasks = task_data.get('tasks', [])
        
        # 递归处理嵌套字典和列表中的变量
        def process_variable_references(data):
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, (dict, list)):
                        process_variable_references(value)
                    elif isinstance(value, str) and '{{' in value and '}}' in value:
                        # 提取变量名
                        import re
                        var_matches = re.findall(r'{{(.*?)}}', value)
                        processed_value = value
                        
                        for var_name in var_matches:
                            # 查找Public_Data中匹配的记录
                            public_data = Public_Data.objects.filter(
                                key=var_name, 
                                type='5',  # UI测试类型
                                project_id=project_id
                            ).first()
                            
                            if public_data:
                                # 替换变量引用为实际值
                                processed_value = processed_value.replace(f'{{{{{var_name}}}}}', public_data.value)
                        
                        data[key] = processed_value
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    if isinstance(item, (dict, list)):
                        process_variable_references(item)
                    elif isinstance(item, str) and '{{' in item and '}}' in item:
                        # 提取变量名
                        import re
                        var_matches = re.findall(r'{{(.*?)}}', item)
                        processed_value = item
                        
                        for var_name in var_matches:
                            # 查找Public_Data中匹配的记录
                            public_data = Public_Data.objects.filter(
                                key=var_name, 
                                type='5',  # UI测试类型
                                project_id=project_id
                            ).first()
                            
                            if public_data:
                                # 替换变量引用为实际值
                                processed_value = processed_value.replace(f'{{{{{var_name}}}}}', public_data.value)
                        
                        data[i] = processed_value
            
            return data
        
        # 处理tasks中的变量引用
        processed_tasks = process_variable_references(tasks)

        yaml_data = {
            'target': {
                'url': task_data.get('url'),
                'bridgeMode': bridge_mode,
                'acceptInsecureCerts': task_data.get('accept_insecure_certs'),
                'viewportWidth': 1920,
                'viewportHeight': 1080
            },
            'tasks': processed_tasks
        }
        
        # 保存YAML文件，使用自定义的Dumper确保正确的缩进
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(yaml_data, f, Dumper=YAMLFormatter, allow_unicode=True, 
                      sort_keys=False, default_flow_style=False, indent=2)
            
        # 更新任务的yaml_path字段
        UiTestTask.objects.filter(id=task_id).update(yaml_path=yaml_path)

    def _delete_yaml_file(self, task_id):
        """删除任务对应的YAML文件"""
        yaml_dir = os.path.join(settings.BASE_DIR, 'ui_test', 'yaml_configs')
        # 查找并删除匹配的文件
        for filename in os.listdir(yaml_dir):
            if filename.startswith(f'{task_id}_'):
                yaml_path = os.path.join(yaml_dir, filename)
                if os.path.exists(yaml_path):
                    os.remove(yaml_path)

    def _get_filtered_queryset(self, queryset, search_term):
        """根据搜索条件过滤查询集"""
        # 获取project_id参数
        project_id = self.request.query_params.get('project_id')
        folder_id = self.request.query_params.get('folder_id')
        # 新增：获取状态筛选参数
        status_filter = self.request.query_params.get('status', '').strip().lower()
        
        # project_id 是必传参数（仅针对列表查询）
        if not project_id:
            raise ValueError("获取任务列表时，project_id是必传参数")
            
        # 添加project_id过滤条件
        queryset = queryset.filter(project_id=project_id)

        # 如果传入了folder_id，先检查是否是任务节点
        if folder_id:
            try:
                folder = UiTestTreeFolder.objects.get(id=folder_id)
                if folder.type == '0':  # 如果是任务节点
                    # 直接通过关联关系获取任务
                    if folder.related_task:
                        return UiTestTask.objects.filter(id=folder.related_task.id)
                    return UiTestTask.objects.none()
                else:  # 如果是目录节点，获取该目录及其所有子目录下的任务
                    def get_all_child_folders(folder_id):
                        """递归获取所有子目录ID"""
                        folder_ids = [folder_id]
                        children = UiTestTreeFolder.objects.filter(parent_id=folder_id)
                        for child in children:
                            folder_ids.extend(get_all_child_folders(child.id))
                        return folder_ids
                    
                    # 获取所有相关的文件夹ID
                    folder_ids = get_all_child_folders(int(folder_id))
                    # 通过tree_node反向关联查询任务
                    task_ids = UiTestTreeFolder.objects.filter(
                        id__in=folder_ids, 
                        type='0',
                        related_task__isnull=False
                    ).values_list('related_task_id', flat=True)
                    queryset = queryset.filter(id__in=task_ids)
            except UiTestTreeFolder.DoesNotExist:
                raise ValueError("指定的目录或任务节点不存在！")
        
        # 新增：根据状态筛选任务
        if status_filter and status_filter not in ['all', '']:
            from ...models import UiTestTaskExecution
            
            # 获取所有符合状态条件的执行记录
            if status_filter == 'success':
                # 找出所有状态为success的任务ID
                task_ids_with_success = set()
                
                # 遍历当前查询集中的任务
                for task in queryset:
                    # 获取任务的最新执行记录
                    latest_execution = UiTestTaskExecution.objects.filter(
                        task_id=task.id
                    ).order_by('-start_time').first()
                    
                    # 如果存在执行记录且状态为success，则添加到集合中
                    if latest_execution and latest_execution.status == 'success':
                        task_ids_with_success.add(task.id)
                
                # 筛选出状态为success的任务
                queryset = queryset.filter(id__in=task_ids_with_success)
                
            elif status_filter == 'failed':
                # 找出所有状态为failed的任务ID
                task_ids_with_failed = set()
                
                # 遍历当前查询集中的任务
                for task in queryset:
                    # 获取任务的最新执行记录
                    latest_execution = UiTestTaskExecution.objects.filter(
                        task_id=task.id
                    ).order_by('-start_time').first()
                    
                    # 如果存在执行记录且状态为failed，则添加到集合中
                    if latest_execution and latest_execution.status == 'failed':
                        task_ids_with_failed.add(task.id)
                
                # 筛选出状态为failed的任务
                queryset = queryset.filter(id__in=task_ids_with_failed)
        
        if search_term:
            return queryset.filter(
                Q(name__icontains=search_term) |
                Q(description__icontains=search_term)
            )
        return queryset

    def get(self, request, pk=None):
        if pk:
            try:
                task = UiTestTask.objects.get(pk=pk)
                serializer = UiTestTaskSerializer(task)
                return Response(public_success_response(serializer.data))
            except UiTestTask.DoesNotExist:
                return Response(public_error_response("测试任务不存在！"))

        try:
            # 获取基础查询集
            tasks = UiTestTask.objects.all()
            
            # 处理搜索
            search_term = request.query_params.get('search', '').strip()
            tasks = self._get_filtered_queryset(tasks, search_term)
            
            # 排序
            tasks = tasks.order_by(request.query_params.get('ordering', '-create_time'))
            
            # 分页处理
            page_size = request.query_params.get('page_size', default=10)
            page_number = request.query_params.get('page', default=1)

            try:
                paginator = PageNumberPagination()
                paginator.page_size = int(page_size)
                paginator.page = int(page_number)
            except ValueError:
                return Response(public_error_response("分页参数格式错误！"))

            page = paginator.paginate_queryset(tasks, request)
            serializer = UiTestTaskSerializer(page, many=True)
            return paginator.get_paginated_response(public_success_response(serializer.data))
        except ValueError as e:
            return Response(public_error_response(str(e)))

    def post(self, request):
        serializer = UiTestTaskCreateUpdateSerializer(data=request.data)
        if serializer.is_valid():
            # 验证folder_id是否有效且类型是否为目录类型(1)
            folder_id = request.data.get('folder_id')
            if not folder_id:
                return Response(public_error_response("创建任务时必须指定目录ID(folder_id)！"))
                
            try:
                parent_folder = UiTestTreeFolder.objects.get(id=folder_id)
                if parent_folder.type != '1':
                    return Response(public_error_response("只能在目录类型下创建任务！"))
            except UiTestTreeFolder.DoesNotExist:
                return Response(public_error_response("指定的目录不存在！"))
            
            # 创建任务
            task = serializer.save(creator=request.user.username)
            
            # 创建任务节点并建立关联
            tree_node = UiTestTreeFolder.objects.create(
                name=request.data['name'],
                type='0',  # 0表示任务节点
                parent_id=folder_id,
                project_id=request.data['project'],
                related_task=task
            )
            
            # 保存YAML配置文件
            self._save_yaml_file(task.id, serializer.validated_data, request.data['project'])
            
            # 返回完整的任务信息
            response_serializer = UiTestTaskSerializer(task)
            return Response(public_success_response(response_serializer.data))
            
        return Response(public_error_response(serializer.errors))

    def put(self, request, pk):
        try:
            task = UiTestTask.objects.get(pk=pk)
        except UiTestTask.DoesNotExist:
            return Response(public_error_response("测试任务不存在！"))

        # 如果要更新folder_id，验证新的folder_id
        if 'folder_id' in request.data:
            folder_id = request.data.get('folder_id')
            try:
                parent_folder = UiTestTreeFolder.objects.get(id=folder_id)
                if parent_folder.type != '1':
                    return Response(public_error_response("只能将任务移动到目录类型下！"))
            except UiTestTreeFolder.DoesNotExist:
                return Response(public_error_response("指定的目录不存在！"))

            # 更新或创建树节点
            try:
                tree_node = task.tree_node
                # 如果已有树节点，更新其父节点
                tree_node.parent_id = folder_id
                tree_node.save()
            except UiTestTreeFolder.DoesNotExist:
                # 如果没有树节点，创建一个新的
                tree_node = UiTestTreeFolder.objects.create(
                    name=task.name,
                    type='0',
                    parent_id=folder_id,
                    project_id=task.project_id,
                    related_task=task
                )

        # 如果要更新名称，同步更新树节点名称
        if 'name' in request.data:
            try:
                task.tree_node.name = request.data['name']
                task.tree_node.save()
            except UiTestTreeFolder.DoesNotExist:
                pass  # 如果没有树节点，就不需要更新

        serializer = UiTestTaskCreateUpdateSerializer(task, data=request.data, partial=True)
        if serializer.is_valid():
            task = serializer.save()
            # 更新YAML配置文件
            self._save_yaml_file(task.id, serializer.validated_data, task.project_id)
            # 返回完整的任务信息
            response_serializer = UiTestTaskSerializer(task)
            return Response(public_success_response(response_serializer.data))
        return Response(public_error_response(serializer.errors))

    def delete(self, request, pk=None):
        if pk:
            try:
                task = UiTestTask.objects.get(pk=pk)
                # 删除对应的树节点（会通过CASCADE自动删除）
                try:
                    task.tree_node.delete()
                except UiTestTreeFolder.DoesNotExist:
                    pass  # 如果没有树节点，直接跳过
                # 删除YAML配置文件
                self._delete_yaml_file(task.id)
                task.delete()
                return Response(public_success_response("测试任务删除成功！"))
            except UiTestTask.DoesNotExist:
                return Response(public_error_response("测试任务不存在！"))
        else:
            ids = request.data.get('ids', [])
            if not ids:
                return Response(public_error_response("未提供任务ID！"))
            
            # 获取所有相关任务
            tasks = UiTestTask.objects.filter(id__in=ids)
            
            # 删除对应的树节点（会通过CASCADE自动删除）
            tree_nodes = UiTestTreeFolder.objects.filter(related_task__in=tasks)
            tree_nodes.delete()
            
            # 删除多个任务的YAML配置文件
            for task_id in ids:
                self._delete_yaml_file(task_id)
            
            tasks.delete()
            return Response(public_success_response("测试任务批量删除成功！"))


class UiTestTaskCopyView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        task_id = request.data.get('task_id')
        
        if not task_id:
            return Response(public_error_response("必须提供要复制的任务ID(task_id)！"))
            
        try:
            # 获取源任务
            source_task = UiTestTask.objects.get(pk=task_id)
            project_id = source_task.project_id
            
            # 获取源任务的树节点
            try:
                source_tree_node = UiTestTreeFolder.objects.get(related_task=source_task)
                parent_folder_id = source_tree_node.parent_id  # 获取父目录ID，用于在同级目录创建
            except UiTestTreeFolder.DoesNotExist:
                return Response(public_error_response("源任务没有关联的树节点！"))
            
            # 创建新任务数据字典
            task_data = {
                'name': f"{source_task.name}_copy",
                'description': source_task.description,
                'project': project_id,
                'url': source_task.url,
                'bridge_mode': source_task.bridge_mode,
                'accept_insecure_certs': source_task.accept_insecure_certs,
                'tasks': source_task.tasks,
                'folder_id': parent_folder_id
            }
            
            # 使用序列化器验证和创建新任务
            create_serializer = UiTestTaskCreateUpdateSerializer(data=task_data)
            if create_serializer.is_valid():
                # 创建任务
                new_task = create_serializer.save(creator=request.user.username)
                
                # 创建任务节点并建立关联
                tree_node = UiTestTreeFolder.objects.create(
                    name=task_data['name'],
                    type='0',  # 0表示任务节点
                    parent_id=parent_folder_id,
                    project_id=project_id,
                    related_task=new_task
                )
                
                # 使用已有的方法保存YAML配置文件
                yaml_saver = UiTestTaskView()
                yaml_saver._save_yaml_file(new_task.id, create_serializer.validated_data, project_id)
                
                # 返回完整的新任务信息
                response_serializer = UiTestTaskSerializer(new_task)
                return Response(public_success_response(response_serializer.data))
            else:
                return Response(public_error_response(create_serializer.errors))
                
        except UiTestTask.DoesNotExist:
            return Response(public_error_response("要复制的任务不存在！"))
        except Exception as e:
            return Response(public_error_response(f"复制任务时出错：{str(e)}"))


class UiTestTaskMoveView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        try:
            # 获取需要移动的任务ID和目标文件夹ID
            task_id = request.data.get('task_id')
            target_folder_id = request.data.get('target_folder_id')
            
            # 验证参数
            if not task_id or not target_folder_id:
                return Response(public_error_response('缺少必要参数: task_id 或 target_folder_id'))
            
            # 获取要移动的任务
            try:
                task = UiTestTask.objects.get(pk=task_id)
            except UiTestTask.DoesNotExist:
                return Response(public_error_response('任务不存在'))
                
            # 获取任务对应的树节点
            try:
                task_node = UiTestTreeFolder.objects.get(related_task=task)
            except UiTestTreeFolder.DoesNotExist:
                return Response(public_error_response('任务没有关联的树节点'))
            
            # 获取目标文件夹
            try:
                target_folder = UiTestTreeFolder.objects.get(id=target_folder_id)
            except UiTestTreeFolder.DoesNotExist:
                return Response(public_error_response('目标文件夹不存在'))
            
            # 验证目标是否为目录类型
            if target_folder.type != '1':
                return Response(public_error_response('目标必须是目录类型'))
            
            # 记录原父目录ID，用于返回
            old_parent_id = task_node.parent_id
            
            # 更新任务节点的父目录
            task_node.parent_id = target_folder_id
            task_node.save()
            
            return Response(public_success_response({
                'message': '任务移动成功',
                'task_id': task.id,
                'task_name': task.name,
                'old_parent_id': old_parent_id,
                'new_parent_id': target_folder.id
            }))
            
        except Exception as e:
            return Response(public_error_response(f'移动失败，错误信息: {str(e)}'))

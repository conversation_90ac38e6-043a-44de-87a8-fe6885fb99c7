from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ui_test.models import UiTestTaskExecution
from ats.public.public_class.myauthentication import MyAuthentication
from ats.public.public_class.myresponse import public_success_response, public_error_response
from django.db.models import Max, F, Subquery, OuterRef
import logging
import os
from django.utils import timezone

logger = logging.getLogger('ats-console')

class TaskStatusView(APIView):
    """
    获取UI测试任务的执行状态
    """
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes

    def get(self, request):
        """
        获取任务执行状态
        参数：
        - task_id: 任务ID（必填）
        - execution_id: 执行ID（可选，如果不提供则返回最新执行记录）
        """
        try:
            task_id = request.query_params.get('task_id')
            execution_id = request.query_params.get('execution_id')

            if not task_id and not execution_id:
                return Response(public_error_response("缺少task_id或execution_id参数"))

            try:
                if execution_id:
                    # 如果提供了execution_id，直接查询该记录
                    execution = UiTestTaskExecution.objects.get(id=execution_id)
                else:
                    # 如果只提供了task_id，查询该任务的最新执行记录
                    execution = UiTestTaskExecution.objects.filter(
                        task_id=task_id
                    ).order_by('-start_time').first()
                    
                    if not execution:
                        return Response(public_error_response(f"未找到任务ID {task_id} 的执行记录"))

            except UiTestTaskExecution.DoesNotExist:
                return Response(public_error_response("未找到执行记录"))

            # 返回任务状态信息
            data = {
                "execution_id": execution.id,
                "task_id": execution.task.id,
                "task_name": execution.task.name,
                "status": execution.status,
                "start_time": execution.start_time,
                "end_time": execution.end_time,
                "error_message": execution.error_message,
                "report_path": execution.report_path
            }
            return Response(public_success_response(data))

        except Exception as e:
            logger.error(f"获取任务状态时发生错误: {str(e)}")
            return Response(public_error_response(f"获取任务状态时发生错误: {str(e)}"))

    def post(self, request):
        """
        获取所有任务的最新执行状态
        参数：
        - project_id: 项目ID（必填）
        """
        try:
            project_id = request.data.get('project_id')
            if not project_id:
                return Response(public_error_response("缺少project_id参数"))

            # 使用子查询获取每个任务的最新执行记录
            latest_executions = (
                UiTestTaskExecution.objects
                .filter(task__project_id=project_id)  # 添加项目ID过滤
                .values('task_id')
                .annotate(max_id=Max('id'))
                .values('max_id')
            )

            # 获取所有最新的执行记录
            executions = (
                UiTestTaskExecution.objects
                .filter(id__in=latest_executions)
                .select_related('task')
                .order_by('task_id')
            )

            # 构建响应数据
            data = []
            for execution in executions:
                task_status = {
                    "execution_id": execution.id,
                    "task_id": execution.task.id,
                    "task_name": execution.task.name,
                    "status": execution.status,
                    "start_time": execution.start_time,
                    "end_time": execution.end_time,
                    "error_message": execution.error_message,
                    "report_path": execution.report_path
                }
                data.append(task_status)

            return Response(public_success_response(data))

        except Exception as e:
            logger.error(f"获取所有任务状态时发生错误: {str(e)}")
            return Response(public_error_response(f"获取所有任务状态时发生错误: {str(e)}"))

class TaskLogView(APIView):
    """
    获取UI测试任务的执行日志
    """
    authentication_classes = MyAuthentication.authentication_classes
    permission_classes = MyAuthentication.permission_classes

    def get(self, request):
        """
        获取任务执行日志
        参数：
        - task_id: 任务ID（必填）
        - execution_id: 执行ID（可选，如果不提供则返回最新执行记录的日志）
        """
        try:
            task_id = request.query_params.get('task_id')
            execution_id = request.query_params.get('execution_id')

            if not task_id and not execution_id:
                return Response(public_error_response("缺少task_id或execution_id参数"))

            try:
                if execution_id:
                    # 如果提供了execution_id，直接查询该记录
                    execution = UiTestTaskExecution.objects.get(id=execution_id)
                else:
                    # 如果只提供了task_id，查询该任务的最新执行记录
                    execution = UiTestTaskExecution.objects.filter(
                        task_id=task_id
                    ).order_by('-start_time').first()
                    
                    if not execution:
                        return Response(public_error_response(f"未找到任务ID {task_id} 的执行记录"))

            except UiTestTaskExecution.DoesNotExist:
                return Response(public_error_response("未找到执行记录"))
            
            # 检查是否有日志文件
            log_path = execution.log_path
            log_content = ""
            
            # 如果日志文件存在，直接用cat命令读取
            if log_path and os.path.exists(log_path):
                try:
                    # 直接使用cat命令读取文件内容
                    import subprocess
                    result = subprocess.run(['cat', log_path], capture_output=True, text=True)
                    log_content = result.stdout
                    
                    # 如果内容太长，只保留最后的部分
                    if log_content:
                        lines = log_content.splitlines()
                        max_lines = 5000  # 最多显示5000行
                        if len(lines) > max_lines:
                            log_content = '\n'.join(lines[-max_lines:])
                except Exception as e:
                    logger.error(f"读取日志文件时发生错误: {str(e)}")
                    # 如果读取文件失败，回退到使用数据库中存储的日志
                    log_content = execution.execution_output or ""
            # 如果日志文件不存在，使用数据库中存储的日志
            else:
                log_content = execution.execution_output or ""
            
            # 过滤掉特定的错误信息
            if log_content:
                filtered_lines = []
                for line in log_content.splitlines():
                    # 过滤掉 "/bin/sh: 1: git: not found" 相关的错误行
                    if "/bin/sh: 1: git: not found" not in line:
                        filtered_lines.append(line)
                log_content = '\n'.join(filtered_lines)
                
            # 获取当前时间，用于前端显示最后更新时间
            current_time = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 返回执行日志
            data = {
                "execution_id": execution.id,
                "task_id": execution.task.id,
                "task_name": execution.task.name,
                "status": execution.status,
                "execution_output": log_content,
                "error_message": execution.error_message or "",
                "log_path": log_path,
                "last_updated": current_time
            }
            
            # 添加禁用缓存的响应头
            response = Response(public_success_response(data))
            response["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response["Pragma"] = "no-cache"
            response["Expires"] = "0"
            
            return response

        except Exception as e:
            logger.error(f"获取任务日志时发生错误: {str(e)}")
            return Response(public_error_response(f"获取任务日志时发生错误: {str(e)}")) 
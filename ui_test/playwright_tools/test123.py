import time
from playwright.sync_api import sync_playwright

def test_run(playwright):
    browser = playwright.chromium.launch(headless=True)

    # 开启录制视频，视频保存在 videos/ 目录
    context = browser.new_context(
        viewport={"width": 1920, "height": 1080},
        record_video_dir="videos/",
        record_video_size={"width": 1920, "height": 1080}
    )

    page = context.new_page()

    try:
        page.goto("http://124.225.137.100:8000/portal/login")
        page.get_by_role("textbox", name="请输入账号").fill("admin")
        page.get_by_role("textbox", name="请输入密码").fill("Aistar2025@123")
        page.get_by_role("textbox", name="请输入验证码").fill("-9999")
        page.get_by_role("button", name="登录").click()
        page.get_by_role("button", name="门").click()
        page.get_by_text("退出").click()
        page.get_by_role("button", name="确定").click()
        time.sleep(2)
        assert page.get_by_text("成功退出登录").is_visible()

        # 可选：截图
        page.screenshot(path="screenshot.png")
    
    finally:
        context.close()  # 视频会在这一步后写入磁盘
        browser.close()

# with sync_playwright() as playwright:
#     test_run(playwright)
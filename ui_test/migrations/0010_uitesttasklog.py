# Generated by Django 4.2.5 on 2025-04-21 16:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ui_test', '0009_remove_uitesttask_folder_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UiTestTaskLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_text', models.TextField(db_comment='日志内容')),
                ('log_time', models.DateTimeField(auto_now_add=True, db_comment='日志时间')),
                ('log_type', models.CharField(db_comment='日志类型: info/error', default='info', max_length=10)),
                ('execution', models.ForeignKey(db_comment='关联的任务执行', on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='ui_test.uitesttaskexecution')),
            ],
            options={
                'db_table': 'ui_test_task_log',
                'db_table_comment': 'UI测试任务实时日志',
                'ordering': ['log_time'],
            },
        ),
    ]

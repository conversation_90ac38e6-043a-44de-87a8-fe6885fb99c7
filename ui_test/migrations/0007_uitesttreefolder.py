# Generated by Django 4.2.5 on 2025-04-18 17:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0002_openai_config'),
        ('ui_test', '0006_alter_uitesttask_accept_insecure_certs_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UiTestTreeFolder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_comment='文件夹名', max_length=100)),
                ('type', models.Char<PERSON>ield(db_comment='树类型;1目录;0任务', max_length=1)),
                ('parent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='ui_test.uitesttreefolder')),
                ('project', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'ui_test_tree_folder',
                'db_table_comment': 'UI测试目录树',
            },
        ),
    ]

# Generated by Django 4.2.5 on 2025-04-15 10:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ui_test', '0003_rename_uitestcase_uitesttask'),
    ]

    operations = [
        migrations.AddField(
            model_name='uitesttask',
            name='yaml_path',
            field=models.CharField(db_comment='YAML配置文件路径', max_length=500, null=True),
        ),
        migrations.CreateModel(
            name='UiTestTaskExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(db_comment='执行状态: pending/running/success/failed', max_length=20)),
                ('start_time', models.DateTimeField(auto_now_add=True, db_comment='开始执行时间')),
                ('end_time', models.DateTimeField(blank=True, db_comment='执行结束时间', null=True)),
                ('log_path', models.CharField(blank=True, db_comment='日志文件路径', max_length=500, null=True)),
                ('report_path', models.CharField(blank=True, db_comment='测试报告路径', max_length=500, null=True)),
                ('error_message', models.TextField(blank=True, db_comment='错误信息', null=True)),
                ('execution_output', models.TextField(blank=True, db_comment='执行输出', null=True)),
                ('task', models.ForeignKey(db_comment='关联的UI测试任务', on_delete=django.db.models.deletion.CASCADE, to='ui_test.uitesttask')),
            ],
            options={
                'db_table': 'ui_test_task_execution',
                'db_table_comment': 'UI测试任务执行记录表',
                'ordering': ['-start_time'],
            },
        ),
    ]

# Generated by Django 4.2.5 on 2025-04-16 10:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ui_test', '0005_remove_uitesttaskexecution_log_path'),
    ]

    operations = [
        migrations.AlterField(
            model_name='uitesttask',
            name='accept_insecure_certs',
            field=models.BooleanField(db_comment='是否忽略HTTPS证书错误', default=True),
        ),
        migrations.AlterField(
            model_name='uitesttask',
            name='bridge_mode',
            field=models.CharField(blank=True, db_comment='桥接模式:newTabWithUrl/currentTab', default='false', max_length=20, null=True),
        ),
    ]

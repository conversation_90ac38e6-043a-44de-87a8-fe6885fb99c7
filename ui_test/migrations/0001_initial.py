# Generated by Django 4.2.5 on 2025-04-14 17:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('project', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UiTestCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON>ield(db_comment='配置名称', max_length=100)),
                ('description', models.TextField(blank=True, db_comment='配置描述', null=True)),
                ('url', models.CharField(db_comment='访问URL', max_length=500)),
                ('bridge_mode', models.CharField(blank=True, db_comment='桥接模式:newTabWithUrl/currentTab', max_length=20, null=True)),
                ('accept_insecure_certs', models.<PERSON><PERSON>an<PERSON>ield(db_comment='是否忽略HTTPS证书错误', default=False)),
                ('tasks', models.JSONField(db_comment='任务配置', null=True)),
                ('create_time', models.DateTimeField(auto_now_add=True, db_comment='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, db_comment='更新时间')),
                ('creator', models.CharField(db_comment='创建人', max_length=50, null=True)),
                ('project', models.ForeignKey(db_comment='所属项目', on_delete=django.db.models.deletion.CASCADE, to='project.project')),
            ],
            options={
                'db_table': 'ui_test_case',
                'db_table_comment': 'UI测试用例表',
            },
        ),
    ]

from django.urls import path, re_path
from django.conf import settings
from django.conf.urls.static import static
from .ui_test_views.main_views import ui_test_task_views
from ui_test.ui_test_views.main_views.task_runner_views import RunUITestView
from .ui_test_views.main_views.show_report_views import TaskExecutionHistoryView
from .ui_test_views.main_views.task_status_views import TaskStatusView, TaskLogView
from .ui_test_views.main_views.ui_test_tree_folder_view import UiTestTreeFolderView, UiTestTreeFolderDetailView
from .ui_test_views.main_views.task_import_export_views import UiTestTaskExportView, UiTestTaskImportView


urlpatterns = [
    re_path(r'^task/(?:(?P<pk>\d+)/)?$', ui_test_task_views.UiTestTaskView.as_view()),
    path('task_copy/', ui_test_task_views.UiTestTaskCopyView.as_view(), name='task_copy'),
    path('task_move/', ui_test_task_views.UiTestTaskMoveView.as_view(), name='task_move'),
    path('run_ui_test/', RunUITestView.as_view(), name='run_ui_test'),
    # 报告相关接口
    path('run_ui_test/', RunUITestView.as_view(), name='run_ui_test'),
    path('executions/', TaskExecutionHistoryView.as_view(), name='task_executions'),
    # 任务状态和日志接口
    path('task_status/', TaskStatusView.as_view(), name='task_status'),
    path('task_log/', TaskLogView.as_view(), name='task_log'),
    # UI测试目录树接口
    path('ui_test_tree_folder/', UiTestTreeFolderView.as_view(), name='ui_test_tree_folder'),
    path('ui_test_tree_folder/<int:pk>/', UiTestTreeFolderDetailView.as_view(), name='ui_test_tree_folder_detail'),
    # 任务导入导出接口
    path('task_export/', UiTestTaskExportView.as_view(), name='task_export'),
    path('task_import/', UiTestTaskImportView.as_view(), name='task_import'),
]
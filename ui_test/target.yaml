target:
  # 访问的 URL，必填。如果提供了 `serve` 参数，则提供相对路径
  url: https://www.baidu.com

  # 桥接模式，可选，默认 false，可以为 'newTabWithUrl' 或 'currentTab'。更多详情请参阅后文
  bridgeMode: 'newTabWithUrl'

  # 是否忽略 HTTPS 证书错误，可选，默认 false
  acceptInsecureCerts: true

tasks:
  - name:
    continueOnError: true
    flow:
      - aiAction: 在搜索框内输入 '查询今日天气'，然后点击 百度一下 按钮

      # 点击一个元素，用 prompt 描述元素位置
      - aiTap: <prompt>
        deepThink: <boolean> # 可选，是否使用深度思考（deepThink）来精确定位元素

      # 鼠标悬停一个元素，用 prompt 描述元素位置
      - aiHover: <prompt>
        deepThink: <boolean> # 可选，是否使用深度思考（deepThink）来精确定位元素

      # 输入文本到一个元素，用 prompt 描述元素位置
      - aiInput: <输入框的最终文本内容>
        locate: <prompt>
        deepThink: <boolean> # 可选，是否使用深度思考（deepThink）来精确定位元素

      # 在元素上按下某个按键（如 Enter，Tab，Escape 等），用 prompt 描述元素位置
      - aiKeyboardPress: <按键>
        locate: <prompt>
        deepThink: <boolean> # 可选，是否使用深度思考（deepThink）来精确定位元素

      # 全局滚动，或滚动 prompt 描述的元素
      - aiScroll:
        direction: 'up' # 或 'down' | 'left' | 'right'
        scrollType: 'once' # 或 'untilTop' | 'untilBottom' | 'untilLeft' | 'untilRight'
        distance: <number> # 可选，滚动距离，单位为像素
        locate: <prompt> # 可选，执行滚动的元素
        deepThink: <boolean> # 可选，是否使用深度思考（deepThink）来精确定位元素

      # 数据提取
      # ----------------

      # 执行一个查询，返回一个 JSON 对象
      - aiQuery: <prompt> # 记得在提示词中描述输出结果的格式
        name: <name> # 查询结果在 JSON 输出中的 key


      # 更多 API
      # ----------------

      # 等待某个条件满足，并设置超时时间(ms，可选，默认 30000)
      - aiWaitFor: <prompt>
        timeout: 100000ms

      # 执行一个断言
      - aiAssert: 断言：title中 包含 '查询今日天气_百度搜索' 这个内容

      # 等待一定时间
      - sleep: 1000ms
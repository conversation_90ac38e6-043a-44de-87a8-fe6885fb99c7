import os
from datetime import timedel<PERSON>, datetime
from pathlib import Path

# 测试##cursor
BASE_DIR = Path(__file__).resolve().parent.parent
MEDIA_URL = ''
MEDIA_ROOT = os.path.join(BASE_DIR, 'base/files/')
SECRET_KEY = 'django-insecure-i-owj818owuo##gd14!466)z3y)dxes5c!$-#vq^5$&nks5muh'
SERVER_IP = '127.0.0.1'
#SERVER_PORT = 8000
#开发环境用8000
NGINX_PORT = 8000  

log_directory = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(log_directory):
    os.makedirs(log_directory)

# 获取当前日期，格式化为字符串
current_date = datetime.now().strftime("%Y-%m-%d")

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{levelname}] [{asctime}] [{module}] {message}',
            'style': '{',
        },
        'simple': {
            'format': '[{levelname}] {message}',
            'style': '{',
        },
        'colored': {  # 添加彩色日志格式器
            '()': 'colorlog.ColoredFormatter',
            'format': '{log_color}[{levelname}] [{asctime}] [{module}] {message}',
            'style': '{',
            'log_colors': {
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            },
        },
    },
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, f'logs/ats-{current_date}.log'),
            'formatter': 'verbose',
            'encoding': 'utf-8',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'colored',  # 使用彩色格式器
        },
    },
    'loggers': {
        'ats-console': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'ats-file': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

DEBUG = True

ALLOWED_HOSTS = ['*']

# CORS_ALLOW_ALL_ORIGINS = True


CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173"
]

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'base',
    'project',
    'api_test',
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'django_filters',
    'ats_celery',
    'django_celery_beat',
    'ui_test'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    # 'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'ats.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ats.wsgi.application'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ats_dev',
        'USER': 'ats',
        'PASSWORD': 'Ats123!',
        'HOST': '***************',
        'PORT': 3306,
        'CHARSET': 'utf8'
    }
}

# MinIO 配置
MINIO_ENDPOINT = '***************:9000'
MINIO_ACCESS_KEY = 'admin'
MINIO_SECRET_KEY = 'admin123'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=12000),  # 访问令牌的有效期
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),  # 刷新令牌的有效期
}

# Celery 配置
CELERY_BROKER_URL = 'redis://:ats123@***************:6379/0'
CELERY_RESULT_BACKEND = 'redis://:ats123@***************:6379/1'
CELERY_WORKER_CONCURRENCY = 10
# 任务预取功能，会尽量多拿 n 个，以保证获取的通讯成本可以压缩。
CELERY_PREFETCH_MULTIPLIER = 20
# 有些情况下可以防止死锁
CELERY_FORCE_EXECV = True
# celery 的 worker 执行多少个任务后进行重启操作
CELERY_WORKER_MAX_TASKS_PER_CHILD = 100
# 禁用所有速度限制，如果网络资源有限，不建议开启。
# CELERY_DISABLE_RATE_LIMITS = True
# celery内容等消息的格式设置
CELERY_ACCEPT_CONTENT = ['application/json', ]
# 指定任务序列化方式
CELERY_TASK_SERIALIZER = 'json'
# 指定结果序列化的方式
CELERY_RESULT_SERIALIZER = 'json'

CELERY_TIMEZONE = 'Asia/Shanghai'

CELERY_ENABLE_UTC = False

DJANGO_CELERY_BEAT_TZ_AWARE = False

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = False

STATIC_URL = 'static/'

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 指定自定义User模型
AUTH_USER_MODEL = 'base.MyUser'


from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from ats.public.public_class.myresponse import public_success_response, public_error_response
from functools import wraps


# try except装饰器
def handle_exceptions(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            return Response(public_error_response(str(e)))

    return wrapper


# 重写增删改查的方法，增加一下自定义字段
class MyModelViewSet(ModelViewSet):
    @handle_exceptions
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return Response(public_success_response(serializer.data), status=status.HTTP_200_OK,
                            headers=headers)
        return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)

    @handle_exceptions
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(public_success_response(serializer.data))

    @handle_exceptions
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_response = self.get_paginated_response(serializer.data)
            return Response(public_success_response(paginated_response.data))
        serializer = self.get_serializer(queryset, many=True)
        return Response(public_success_response(serializer.data))

    @handle_exceptions
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(public_success_response(serializer.data))
        return Response(public_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)

    @handle_exceptions
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response(public_success_response("删除成功！"))

    @handle_exceptions
    def destroys(self, request):
        queryset = self.filter_queryset(self.get_queryset())
        ids = request.data.get('ids', [])

        if not ids:
            return Response(public_error_response('未提供批量删除的ID！'), status=status.HTTP_400_BAD_REQUEST)
        queryset.filter(id__in=ids).delete()
        return Response(public_success_response('批量删除成功！'))

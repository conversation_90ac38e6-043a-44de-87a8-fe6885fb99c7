import os, logging
import colorlog  # 导入 colorlog
from ats import settings  # 确保能够导入 settings

logger = logging.getLogger('ats-console')

def add_log_file_handler(file_path):
    # 获取日志文件的目录路径
    directory = os.path.dirname(file_path)
    
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
    
    # 移除特定的 FileHandler
    for handler in logger.handlers[:]:
        if hasattr(handler, 'is_api_log_handler'):
            logger.removeHandler(handler)
            handler.close()
    
    # 创建新的 FileHandler
    file_handler = logging.FileHandler(file_path, mode='w')  # 'w' 模式每次都会覆盖旧文件
    file_handler.is_api_log_handler = True  # 标记为 API 日志处理器
    # 使用标准格式器，包括时间戳
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',  # 添加时间戳
        datefmt='%Y-%m-%d %H:%M:%S'  # 时间格式
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
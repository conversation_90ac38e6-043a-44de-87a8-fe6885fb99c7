def public_success_response(serializer_data=""):
    success_response_data = {
        "code": 2000,
        "status": "success",
        "msg": "操作成功！",
        "data": serializer_data
    }

    return success_response_data


# def public_error_response(serializer_error_data=""):
#     error_response_data = {
#         "code": 2001,
#         "status": "error",
#         "msg": "操作失败！",
#         "data": serializer_error_data
#     }

#     return error_response_data


def public_error_response(serializer_error_data=""):
    # 初始化一个列表来收集所有的错误信息
    error_messages = []

    # 遍历serializer_error_data中的所有错误
    if isinstance(serializer_error_data, dict):
        for field, messages in serializer_error_data.items():
            if isinstance(messages, list):
                # 将每个字段的错误信息添加到error_messages列表中
                error_messages.extend([str(message) for message in messages])
            else:
                # 对于非列表类型的错误信息，直接添加到列表中
                error_messages.append(str(messages))
    else:
        # 如果serializer_error_data不是字典类型，直接将其转换为字符串并添加到列表中
        error_messages.append(str(serializer_error_data))

    # 将所有的错误信息合并成一个字符串，每条错误信息之间用分号隔开
    error_message = "; ".join(error_messages) if error_messages else "操作失败！"

    error_response_data = {
        "code": 2001,
        "status": "error",
        "msg": error_message,  # 使用合并后的错误信息
        "data": serializer_error_data  # 保留原始的错误数据，以便前端需要时使用
    }

    return error_response_data
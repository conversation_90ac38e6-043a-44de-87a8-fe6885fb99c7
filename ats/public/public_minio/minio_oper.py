import os
import json
from minio import Minio
from minio.error import S3Error


def create_minio_client(endpoint, access_key, secret_key):
    return Minio(
        endpoint=endpoint,
        access_key=access_key,
        secret_key=secret_key,
        secure=False
    )

def create_bucket(client, bucket_name, public=True):
    found = client.bucket_exists(bucket_name)
    if not found:
        client.make_bucket(bucket_name)
        # print(f"桶 '{bucket_name}' 创建成功。")
    else:
        pass
        # print(f"桶 '{bucket_name}' 已经存在。")
    # 定义桶的访问策略为 'public'
    if public:
        public_policy = {
            "Version": "2012-10-17",
            "Statement": [
            {
                "Effect": "Allow",
                "Principal": {"AWS": ["*"]},
                "Action": [
                    "s3:GetBucketLocation",
                    "s3:ListBucket"
                ],
                "Resource": [f"arn:aws:s3:::{bucket_name}"]
            },
            {
                "Effect": "Allow",
                "Principal": {"AWS": ["*"]},
                "Action": [
                    "s3:GetObject"
                ],
                    "Resource": [f"arn:aws:s3:::{bucket_name}/*"]
            }]
        }
        # 将策略应用到桶
        client.set_bucket_policy(bucket_name, json.dumps(public_policy))
        # print(f"Bucket '{bucket_name}' is now public!")


def upload_file(client, bucket_name, file_path):
    object_name = os.path.basename(file_path)
    try:
        client.fput_object(
            bucket_name, object_name, file_path,
        )
        print(f"'{file_path}' 上传到 '{bucket_name}' 成功.")
    except S3Error as exc:
        print("上传失败：", exc)
    
def upload_file_to_minio(client, bucket_name, file, object_name):
    try:
        client.put_object(bucket_name, object_name, file, file.size)
        print(f"'{file.name}' 上传到 '{bucket_name}' 成功.")
    except S3Error as exc:
        print("上传失败：", exc)

def download_file(client, bucket_name, object_name, file_path):
    try:
        client.fget_object(bucket_name, object_name, file_path)
        print(f"'{object_name}' 从 '{bucket_name}' 下载成功.")
    except S3Error as exc:
        print("下载失败：", exc)


def list_objects(client, bucket_name):
    try:
        objects = client.list_objects(bucket_name)
        for obj in objects:
            print(obj.object_name)
    except S3Error as exc:
        print("列出文件失败：", exc)


def delete_file(client, bucket_name, object_name):
    try:
        client.remove_object(bucket_name, object_name)
        print(f"'{object_name}' 从 '{bucket_name}' 删除成功。")
    except S3Error as exc:
        print("删除失败：", exc)

def delete_bucket(client, bucket_name):
    try:
        client.remove_bucket(bucket_name)
        print(f"存储桶 '{bucket_name}' 删除成功。")
    except S3Error as exc:
        print("删除存储桶失败：", exc)


def copy_file(client, source_bucket_name, source_object_name, target_bucket_name, target_object_name):
    try:
        source = {
            "bucket_name": source_bucket_name,
            "object_name": source_object_name,
        }
        client.copy_object(target_bucket_name, target_object_name, source)
        print(f"'{source_object_name}' 已从 '{source_bucket_name}' 复制到 '{target_bucket_name}'。")
    except S3Error as exc:
        print("复制文件失败：", exc)


# if __name__ == "__main__":
#     endpoint = "111.229.114.90:9000"
#     access_key = "Admin"
#     secret_key = "Admin123"

#     # 创建客户端
#     client = create_minio_client(endpoint, access_key, secret_key)

#     create_bucket(client, "gph-bucket")

#     # 上传文件
#     upload_file(client, "gph-bucket", "C:\\Users\\<USER>\\Desktop\\ats\\ats_django\\api_test\\api_test_views\\api_test_run\\minio_test\\弹性平台测试用例.xlsx")

#     # 下载文件
#     download_file(client, "gph-bucket", "弹性平台测试用例.xlsx", "C:\\Users\\<USER>\\Desktop\\ats\\ats_django\\api_test\\api_test_views\\api_test_run\\minio_test\\弹性平台测试用例.xlsx")

#     # 列出桶中的文件
#     list_objects(client, "gph-bucket")

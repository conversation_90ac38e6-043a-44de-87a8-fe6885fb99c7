from django.contrib import admin
from django.urls import path, include
from base import urls as base_urls
from project import urls as project_urls
from ats_celery import urls as ats_celery_urls
from ui_test import urls as ui_test_urls

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(base_urls)),
    path('api/project/', include(project_urls)),
    path('api/api_test/', include('api_test.urls')),
    path('api/celery/', include(ats_celery_urls)),
    path('api/ui_test/', include(ui_test_urls))
]
